package com.light.contants;

/**
 * <AUTHOR>
 * @date 2022/4/12 15:11
 */
public interface ConstantsInteger {

    /**
     * 合法
     */
    int STATUS_YES = 1;
    /**
     * 非法
     */
    int STATUS_NO = 0;

    int NUM_0 = 0;
    int NUM_1 = 1;
    int NUM_2 = 2;
    int NUM_3 = 3;
    int NUM_7 = 7;
    int NUM_10 = 10;

    int NUM_1000 = 1000;

    int NUM_10000 = 10000;

    // 好未来成功状态码
    Integer TAL_CODE_SUCCESS = 20000;
    // 好未来搜题失败
    Integer TAL_SEARCH_ITEM_EMPTY = 300017053;

    // 学科网成功状态码
    Integer HDW_CODE_SUCCESS = 2000000;
    // 返回给前端的没有搜到学科网题目的状态码（本系统）
    Integer SEARCH_ITEM_EMPTY = 900161214;
}
