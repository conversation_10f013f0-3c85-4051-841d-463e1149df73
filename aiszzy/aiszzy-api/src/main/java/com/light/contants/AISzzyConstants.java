package com.light.contants;

import com.light.enums.ThirdSourceTypeEnum;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class AISzzyConstants {


    public static final Integer THIRD_USER_SOURCE_INTEFACE = 1;
    //学科网Appcode
    public static final String THIRD_USER_XKW_APP_CODE = ThirdSourceTypeEnum.XKW.getCode();
    // 安卓设备软件激活码位数
    public static final Integer ACTIVATION_CODE_LENGTH = 6;

    //黑色块边距28+17，28+30
    public static final Integer BLACK_BLOCK_MARGIN = 28;
    //黑色块宽
    public static final Integer BBLACK_BLOCK_MARGIN_W = 14;
    //黑色块高
    public static final Integer BLACK_BLOCK_MARGIN_H = 28;
    //打印内边距宽
    public static final Integer PAPER_MARGIN_W = 17;
    //打印内边距高
    public static final Integer PAPER_MARGIN_H = 30;
    /**
     * A4
     */
    public static final String PAGE_SIZE_A4 = "A4";
    /**
     * A3
     */
    public static final String PAGE_SIZE_A3 = "A3";
    /**
     * 分割符
     */
    public static final String SPLIT_CHAR = ";";

    //    是否有疑问 0：无，1：存在 2已经处理
    @Getter
    public enum DoubtStatus {
        NO(0, "无"),
        EXIST(1, "存在"),
        DEAL(2, "2已经处理");
        private Integer type;
        private String value;

        DoubtStatus(Integer type, String value) {
            this.type = type;
            this.value = value;
        }
    }

    //    1学号没有，2重复学号，3作业页数不全，4题目疑问
    @Getter
    public enum DoubtType {
        NOERROR(1, "学号没有"),
        REPEAT(2, "2重复学号"),
        ANSWER(4, "题目疑问");
        private Integer type;
        private String value;

        DoubtType(Integer type, String value) {
            this.type = type;
            this.value = value;
        }
    }

    @Getter
    public enum HomeworkType {
        ORDINARY(1, "普通"),
        PTINT(2, "印刷");
        private Integer type;
        private String value;

        HomeworkType(Integer type, String value) {
            this.type = type;
            this.value = value;
        }
    }

    @Getter
    public enum QuestionShowType {
        IMAGE(0L, "图片"),

        HTML(1L, "html");
        private Long type;
        private String value;

        QuestionShowType(Long type, String value) {
            this.type = type;
            this.value = value;
        }

    }

    @Getter
    public enum Term {
        LAST(1, "上学期"),

        NEXT(2, "下学期");
        private Integer semester;
        private String value;

        Term(Integer semester, String value) {
            this.semester = semester;
            this.value = value;
        }

    }

    @Getter
    public enum InsideSourceType {
        RESOURCE_QUESTION("resource_question"),
        SCHOOL_RESOURCE_QUESTION("school_resource_question");
        private String type;

        InsideSourceType(String type) {
            this.type = type;
        }


    }

    /**
     * 生成规则类型 0普通作业，1分层作业，2教辅关联作业，3靶向作业
     */
    @Getter
    public enum HomeWorkGenerateRuleType {
        ORDINARY(0, "普通作业"),
        LAYERED(1, "分层作业"),
        PRACTICEBOOK(2, "教辅关联作业"),
        TARGET(3, "靶向作业"),
        SCHOOL(4, "校本作业");

        HomeWorkGenerateRuleType(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;

    }

    /**
     * 来源（1、自建；2、引用教辅；3引用作业本）
     */
    @Getter
    public enum HomeWorkSourceType {
        ORDINARY(1, "普通作业"),
        PRACTICEBOOK(2, "引用教辅"),
        HOMEWORKBOOK(3, "引用作业本"),
        SCHOOLBOOK(4, "校本作业");

        HomeWorkSourceType(Integer code, String value) {
            this.code = code;
            this.value = value;
        }


        private Integer code;
        private String value;

    }

    /**
     * 处理状态
     */
    @Getter
    public enum DealStatus {
        NOT_DEAL(0, "未处理"),
        DEAL_SUCCESS(1, "已处理"),
        DEAL_ERROR(2, "处理异常");

        DealStatus(Integer code, String value) {
            this.code = code;
            this.value = value;
        }


        private Integer code;
        private String value;


    }

    /**
     * 获取教辅文件类型
     *
     * @param extName the extname 文件类型字符串
     * @return {@link Integer }
     */
    public static Integer getPracticeBookFileValByType(String extName) {
        if (extName.equalsIgnoreCase("zip")) {
            return 2;
        }
        if (extName.equalsIgnoreCase("pdf")) {
            return 1;
        }
        return null;
    }


    /**
     * 获取学科网学段
     *
     * @param gradeId 年级 ID
     * @return {@link Integer }
     */
    public static Integer getXkwStageByGrade(Integer gradeId) {
        if (gradeId < 7) {
            return 2;
        }
        if (gradeId > 9) {
            return 4;
        }
        return 3;
    }

    /**
     * 获取学科网学段
     *
     * @param stage 学段
     * @return {@link Integer }
     */
    public static List<Integer> getXkwGradeListByStage(Integer stage) {
        if (stage == 2) {
            return IntStream.range(1, 7).boxed().collect(Collectors.toList());
        }
        if (stage == 3) {
            return IntStream.range(7, 10).boxed().collect(Collectors.toList());
        }
        if (stage == 4) {
            return IntStream.range(10, 13).boxed().collect(Collectors.toList());
        }
        return null;
    }


    /**
     * 获取学科网学科
     *
     * @param subject the subject 学科
     * @return {@link String }
     */
    public static String getXkwSubject(String subject) {
        if (subject.equals("30")) {
            return "7";
        }
        return subject;
    }
}
