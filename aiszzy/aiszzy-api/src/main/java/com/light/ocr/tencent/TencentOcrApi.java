package com.light.ocr.tencent;

import cn.hutool.core.io.IoUtil;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;

@Slf4j
@Service
public class TencentOcrApi {

    @Resource
    private TencentOcrProperties tencentOcrProperties;

    /**
     *  公式识别
     * @param imgBase64 the img base64 图片 Base64数据
     * @return {@link TextFormula[] }
     * @throws TencentCloudSDKException
     */
    public TextFormula[] matchOcr(String imgBase64) throws TencentCloudSDKException {
        Credential cred = new Credential(tencentOcrProperties.getAccessKey(), tencentOcrProperties.getAccessSecret());
        OcrClient client = new OcrClient(cred, tencentOcrProperties.getRegion());
        FormulaOCRRequest request = new FormulaOCRRequest();
        request.setImageBase64(imgBase64);
        FormulaOCRResponse formulaOCRResponse = client.FormulaOCR(request);
        log.debug("【腾讯OCR】 腾讯公式识别， 响应:{}", FormulaOCRResponse.toJsonString(formulaOCRResponse));
        return formulaOCRResponse.getFormulaInfos();
    }


    /**
     *  腾讯文字识别
     * @param imgBase64 the img base64 图片 Base64数据
     * @param imageUrl 图片的 Url 地址。 支持的图片格式：PNG、JPG、JPEG，暂不支持 GIF 格式。 支持的图片大小：所下载图片经 Base64 编码后不超过7M。图片下载时间不超过 3 秒。 图片存储于腾讯云的 Url 可保障更高的下载速度和稳定性，建议图片存储于腾讯云。 非腾讯云存储的 Url 速度和稳定性可能受一定影响。
     * @param scene 场景字段，默认不用填写。 可选值:only_hw 表示只输出手写体识别结果，过滤印刷体。
     * @return GeneralHandwritingOCRResponse
     * @throws TencentCloudSDKException
     */
    public GeneralHandwritingOCRResponse getHandwritingOCR(String imgBase64, String imageUrl, String scene) {
        try {
            // 实例化一个请求对象,每个接口都会对应一个request对象
            GeneralHandwritingOCRRequest req = new GeneralHandwritingOCRRequest();

            if (imageUrl != null) {
                req.setImageUrl(imageUrl);
            }else if (imgBase64 != null) {
                req.setImageBase64(imgBase64);
            }else {
                return null;
            }

            req.setScene(scene);
            //默认开启单字的四点定位坐标输出
            req.setEnableWordPolygon(true);

            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            Credential cred = new Credential(tencentOcrProperties.getAccessKey(), tencentOcrProperties.getAccessSecret());
            OcrClient client = new OcrClient(cred, tencentOcrProperties.getRegion());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            // 返回的resp是一个GeneralHandwritingOCRResponse的实例，与请求对象对应
            GeneralHandwritingOCRResponse resp = client.GeneralHandwritingOCR(req);
            // 输出json格式的字符串回包
            log.debug("【腾讯OCR】 腾讯文字识别， 响应:{}", AbstractModel.toJsonString(resp));

            return resp;

        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
        return null;
    }


//    public static void main(String[] args) {
//        try {
//            // System.out.println(containRed("https://aiszzy-1347059756.cos.ap-nanjing.myqcloud.com/fhsljy/20250724/ec74968d-8dc9-416d-808f-3e91c7d2c6bc/Doc1753321670_1.jpg"));
//
//            getHandwritingOCR(null,"https://aiszzy-1347059756.cos.ap-nanjing.myqcloud.com/fhsljy/20250725/1e909261-8364-40fd-90c3-6947f395ee4a/Doc1753436002_1.jpg?imageMogr2/cut/1600x250x0x0","only_hw");
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
}
