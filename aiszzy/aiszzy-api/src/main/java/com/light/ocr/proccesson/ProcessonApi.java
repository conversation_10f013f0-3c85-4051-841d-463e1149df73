package com.light.ocr.proccesson;

public interface ProcessonApi {


    /**
     *  一般图片识别
     * @param content the img content
     * @return {@link String }
     */
    String commonOcr(byte[] content);

    /**
     *  打印图片识别
     * @param content the img content
     * @return {@link String }
     */
    String printedOcr(byte[] content);

    /**
     * 手写体识别
     * @param content the img content
     * @return {@link String }
     */
    String handwrittenOcr(byte[] content);
}
