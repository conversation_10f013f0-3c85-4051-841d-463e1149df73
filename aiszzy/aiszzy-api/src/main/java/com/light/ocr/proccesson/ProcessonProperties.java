package com.light.ocr.proccesson;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

@Data
@ConfigurationProperties(prefix = "processon")
@Configuration
public class ProcessonProperties {


    @NestedConfigurationProperty
    private Ocr ocr = new Ocr();

    /**
     * 解密 key
     */
    private String key;

    /**
     * 向量值
     */
    private String iv;

    /**
     * 环境
     */
    private String env;

    /**
     * app id
     */
    private String appid;

    /**
     * 域名
     */
    private String referer;


    /**
     *  ocr 信息
     * <AUTHOR>
     * @date 2025/07/01
     */
    @Data
    public static class Ocr {

        /**
         * 接口请求地址
         */
        private String url;
    }
}
