package com.light.ocr.tencent;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@RefreshScope
@ConfigurationProperties("tencent.ocr")
public class TencentOcrProperties {

    private String accessKey;

    private String accessSecret;

    private String region;
}
