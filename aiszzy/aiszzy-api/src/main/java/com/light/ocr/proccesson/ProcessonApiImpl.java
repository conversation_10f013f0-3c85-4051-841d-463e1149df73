package com.light.ocr.proccesson;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class ProcessonApiImpl implements ProcessonApi {

    @Resource
    private ProcessonProperties processonProperties;

    @Override
    public String commonOcr(byte[] content) {
        return orc(ProcessonOcrPicTypeEnum.PRINTED, content);
    }

    @Override
    public String printedOcr(byte[] content) {
        return orc(ProcessonOcrPicTypeEnum.PRINTED, content);
    }

    @Override
    public String handwrittenOcr(byte[] content) {
        return orc(ProcessonOcrPicTypeEnum.HANDWRITTEN, content);
    }

    public String orc(ProcessonOcrPicTypeEnum type, byte[] content) {
        Map<String, Object> map = new HashMap<>();
        map.put("picType", type.getVal());
        map.put("content", Base64.encode(content));
        long value = System.currentTimeMillis();
        map.put("time", value);
        String jsonStr = JSONUtil.toJsonStr(map);
        String encrypt = aes(jsonStr);
        Map<String, String> param = new HashMap<>();
        param.put("info", encrypt);
        param.put("env", processonProperties.getEnv());
        param.put("time", value + "");
        return this.execute(this.processonProperties.getOcr().getUrl(),param);
    }


    public String aes(String content) {
        byte[] decodeKey = java.util.Base64.getDecoder().decode(processonProperties.getKey());
        byte[] decodeIv = java.util.Base64.getDecoder().decode(processonProperties.getIv());
        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, decodeKey, decodeIv);
        // 加密并进行Base转码
        return aes.encryptBase64(content);
    }




    private String execute(String url, Map<String, String> body ) {
        String jsonBody = JSONUtil.toJsonStr(body);
        log.debug("【processon 接口请求】 请求地址: {}", url);
        HttpRequest request = HttpUtil.createRequest(Method.POST, url).addRequestInterceptor(httpRequest -> {
            Map<String, String> headers = new HashMap<>();
            headers.put("partnerInfo", processonProperties.getAppid());
            headers.put("appid", processonProperties.getAppid());
            headers.put("X-Referer", processonProperties.getReferer());
            httpRequest.addHeaders(headers);

        }).addResponseInterceptor(httpResponse -> {
            String resp = httpResponse.body();
            log.debug("【processon 接口请求】 请求地址: {}, 响应信息：{}", url, resp);
            ProcessonApiResult bean = JSONUtil.toBean(resp, ProcessonApiResult.class);
            if (!bean.isSuccess()) {
                log.error("【processon 接口请求】,接口请求失败， 请求地址: {},  响应信息：{}", url, resp);
                throw new RuntimeException("processon 接口调用失败");
            }
        }).body(jsonBody);
        try (HttpResponse execute = request.execute()){
            String resp = execute.body();

            ProcessonApiResult bean = JSONUtil.toBean(resp, ProcessonApiResult.class);
            return bean.getData();
        } catch (Exception e) {
            log.error("【processon 接口请求】,接口请求失败， 请求地址: {},  异常信息: {} ", url, ExceptionUtil.stacktraceToString(e));
            return null;
        }
    }


    @Data
    public static class ProcessonApiResult {

        private String code;

        private String msg;

        private String data;


        public boolean isSuccess() {
            return this.code.equals("200");
        }
    }
}
