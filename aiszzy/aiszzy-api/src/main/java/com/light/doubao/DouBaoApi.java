package com.light.doubao;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.light.enums.CorrectResultEnum;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.*;

/**
 * 调用豆包接口聊天接口
 */
@Slf4j
@Service
public class DouBaoApi {

    @Resource
    private DouBaoProperties douBaoProperties;

    /**
     * 提示词
     */
    private static final String PROMPT_TEXT = "请返回图片中是否含有线条交叉。如果没有线条，返回：3。如果有线条交叉返回：2。如果没有线条交叉返回：1。其他情况返回对应的说明";

    /**
     * 判断图片是否有线条交叉
     *
     * @param imageUrl 图片文件URL路径
     * @return 判断结果
     * @throws IOException        读取文件时发生错误
     * @throws URISyntaxException URL 解析错误
     */
    public DouBaoCorrectResult correctResult(String imageUrl) throws IOException, URISyntaxException {
        try {
            System.out.println("请求地址：imageUrl\n" + imageUrl);
            String requestBodyJson = buildRequestBodyJson(imageUrl);

            String response = sendRequest(requestBodyJson);

            return parseResponse(response);

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 判断豆包回复是否正确
     *
     * @param response 豆包原始响应
     * @return 豆包判断结果
     */
    private DouBaoCorrectResult parseResponse(String response) {
        JSONObject resultObject = JSONObject.parseObject(response);

        DouBaoCorrectResult result = new DouBaoCorrectResult();
        result.setTunnelResponse(response);
        if (resultObject != null && resultObject.getJSONArray("choices") != null
                && !resultObject.getJSONArray("choices").isEmpty()) {
            JSONObject choice = (JSONObject) resultObject.getJSONArray("choices").get(0);
            JSONObject message = choice.getJSONObject("message");
            if (message != null && message.getLong("content") != null) {
                result.setContent(message.getLong("content"));
                result.setReasoningContent(message.getString("reasoning_content"));
            }
        }

        if (result.getContent() == null) {
            result.setContent(CorrectResultEnum.UNKNOWN.getCode());
            result.setReasoningContent(CorrectResultEnum.UNKNOWN.getReason());
        }
        return result;

    }

    /**
     * 构造请求体的 JSON 字符串（手动拼接或通过 Jackson 序列化）
     * 这里使用 Jackson 动态构造复杂嵌套结构
     */
    private String buildRequestBodyJson(String url) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();

        // 构造 content 数组中的元素（文本 + 图片）
        // 文本内容
        Map<String, Object> textContent = new LinkedHashMap<>();
        textContent.put("type", "text");
        textContent.put("text", PROMPT_TEXT);

        // 图片 URL 内容
        Map<String, Object> imageUrl = new LinkedHashMap<>();
        imageUrl.put("url", url);

        Map<String, Object> imageContent = new LinkedHashMap<>();
        imageContent.put("type", "image_url");
        imageContent.put("image_url", imageUrl);

        // 消息内容数组（文本 + 图片）
        java.util.List<Map<String, Object>> messageContentList = new ArrayList<>();
        messageContentList.add(textContent);
        messageContentList.add(imageContent);

        // 用户消息对象
        Map<String, Object> userMessage = new LinkedHashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", messageContentList);

        List<Map<String, Object>> messages = new ArrayList<>();
        messages.add(userMessage);

        // 最终请求体
        Map<String, Object> requestBody = new LinkedHashMap<>();
        requestBody.put("model", douBaoProperties.getModel());
        requestBody.put("stream", false);  // 非流式响应
        requestBody.put("messages", messages);  // 消息数组

        // 序列化为 JSON 字符串（保留嵌套结构）
        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(requestBody);
    }

    /**
     * 将本地图片文件转换为 Base64 编码字符串
     *
     * @param imageUrlPath 图片文件路径
     * @return Base64 编码的图片数据
     * @throws IOException 如果文件读取失败
     */
    public String convertLocalImageToBase64(String imageUrlPath) throws IOException, URISyntaxException {
        // 2. 读取图片二进制数据
        byte[] imageBytes;
        BufferedImage image = ImageIO.read(new URL(imageUrlPath));
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            String imageType = imageUrlPath.substring(imageUrlPath.lastIndexOf(".") + 1);

            // 根据图片格式选择不同的编码格式
            ImageIO.write(image, imageType.toLowerCase(), baos);

            imageBytes = baos.toByteArray();
        }

        // 3. 进行Base64编码
        String base64Image = Base64.getEncoder().encodeToString(imageBytes);

        // 4. 输出结果（可选）
        //System.out.println("data:image/jpeg;base64," + base64Image);
        String extName = FileUtil.extName(imageUrlPath);
        // 注意 这是非标准的base64
        return "data:image/" + extName + ";base64," + base64Image;
    }

    /**
     * 发送 HTTP POST 请求并处理响应
     */
    private String sendRequest(String requestBodyJson) throws IOException {

        OkHttpClient client = new OkHttpClient();

        // 构建请求体（application/json 格式）
        RequestBody requestBody = RequestBody.create(MediaType.get("application/json; charset=utf-8"), requestBodyJson);

        // 构建请求头（包含 Authorization）
        Request request = new Request.Builder().url(douBaoProperties.getApiUrl()).post(requestBody).addHeader("Authorization", douBaoProperties.getAuthToken()).build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                System.err.println("请求失败，状态码：" + response.code());
                System.err.println("错误信息：" + response.body().string());
                return response.toString();
            }

            // 解析响应（非流式，直接读取完整 JSON）
            String responseBody = response.body().string();
            System.out.println("响应结果：\n" + responseBody);

            return responseBody;
        }
    }
}
