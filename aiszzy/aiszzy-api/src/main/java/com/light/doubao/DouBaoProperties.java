package com.light.doubao;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@RefreshScope
@ConfigurationProperties("doubao.prop")
public class DouBaoProperties {
    private String authToken;

    private String apiUrl;

    private String promptText;

    private String model;
}