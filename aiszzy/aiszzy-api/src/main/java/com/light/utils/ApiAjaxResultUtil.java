package com.light.utils;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.light.core.entity.AjaxResult;
import com.light.user.userThirdRelationship.entity.vo.UserThirdRelationshipVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ApiAjaxResultUtil {

    public static <T> T toBean(AjaxResult ajaxResult, Class<T> clazz) {
        if (ajaxResult.isSuccess()) {
            T bean = JSONUtil.toBean(JSONUtil.toJsonStr(ajaxResult.getData()), clazz);
            return bean;
        }
        return null;
    }

    public static <T> List<T> toList(AjaxResult ajaxResult, Class<T> clazz) {
        if (ajaxResult.isSuccess()) {
            List<T> resList = JSONUtil.toList(JSONUtil.toJsonStr(ajaxResult.getData()), clazz);
            return resList;
        }
        return new ArrayList();
    }

    public static <T> List<T> dataList(AjaxResult ajaxResult, Class<T> clazz) {
        if (ajaxResult.isSuccess()) {
            Map data = (Map) ajaxResult.getData();
            if (data != null && data.containsKey("list")) {
                Object list = data.get("list");
                List<T> resList = JSONUtil.toList(JSONUtil.toJsonStr(list), clazz);

                return resList;
            }
        }
        return new ArrayList();
    }
}
