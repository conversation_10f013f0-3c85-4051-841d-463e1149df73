package com.light.utils;

import com.light.core.utils.SpringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

public class ApiRequestLogUtil {
    public static final String API_REQUEST_LOG_EXCHANGE = "api.request.log.exchange";//第三方日志交换机
    public static final String API_REQUEST_LOG_LOGIN_LOG_QUEUE = "api.request.log.queue";//第三方日志队列


    public static void publishMessage(String value) {
        RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
        if (rabbitTemplate == null) {
            return;
        }
        rabbitTemplate.convertAndSend(API_REQUEST_LOG_EXCHANGE, API_REQUEST_LOG_LOGIN_LOG_QUEUE, value);
    }
}
