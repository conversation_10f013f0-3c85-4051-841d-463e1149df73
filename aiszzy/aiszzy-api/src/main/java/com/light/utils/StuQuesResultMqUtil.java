package com.light.utils;

import com.light.core.utils.SpringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

public class StuQuesResultMqUtil {
    public static final String STU_QUES_RESULT_EXCHANGE = "stu.ques.result.exchange";//第三方日志交换机
    public static final String STU_QUES_RESULT_QUEUE = "stu.ques.result.queue";//第三方日志队列


    public static void publishMessage(String value) {
        RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
        if (rabbitTemplate == null) {
            return;
        }
        rabbitTemplate.convertAndSend(STU_QUES_RESULT_EXCHANGE, STU_QUES_RESULT_QUEUE, value);
    }
}
