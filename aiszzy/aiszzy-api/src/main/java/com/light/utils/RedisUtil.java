package com.light.utils;

import cn.hutool.json.JSONUtil;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.SpringUtils;
import com.light.redis.component.RedisComponent;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;
import java.util.function.Function;

public class RedisUtil {

    public static AjaxResult lock(String lockKey, Function<String, AjaxResult> c) {
        RedissonClient redissonClient = SpringUtils.getBean(RedissonClient.class);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            lock.lock(10, TimeUnit.SECONDS);
            return c.apply(lockKey);
        } catch (Exception e) {
            return AjaxResult.fail();
        } finally {
            lock.unlock();
        }
    }

    public static Object getObject(String key, Function<String, Object> fun) {
        RedisComponent redisComponent = SpringUtils.getBean(RedisComponent.class);
        Object o = redisComponent.get(key);
        if (o != null) {
            return o;
        }
        Object apply = fun.apply(key);
        redisComponent.set(key, JSONUtil.toJsonStr(apply));
        return apply;
    }

    public static <T> T getOrSet(String key, Class<T> clazz, Function<String, T> fun) {
        RedisComponent redisComponent = SpringUtils.getBean(RedisComponent.class);
        Object o = redisComponent.get(key);
        if (o != null) {
            return JSONUtil.toBean(o.toString(), clazz);
        }
        T apply = fun.apply(key);
        redisComponent.set(key, JSONUtil.toJsonStr(apply));
        return apply;
    }


}
