package com.light.utils;

import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class TreeUtil {


    /**
     *  树形结构处理
     * @param treeVOList the tree vo list
     * @param pid parent id
     * @param childrenFunc children  function
     * @return {@link List }<{@link T }>
     */
    public static <T extends TreeBean<T>> List<T> tree(List<T> treeVOList , Object pid, Consumer<T> childrenFunc){
        //获取子结构
        List<T> list = treeVOList.stream().filter(val -> String.valueOf(val.getParentTreeKey()).equals(String.valueOf(pid)))
                .collect(Collectors.toList());

        List<T> childes = treeVOList.stream().filter(val -> !String.valueOf(val.getParentTreeKey()).equals(String.valueOf(pid)))
                .collect(Collectors.toList());

        //给子菜单赋值
        list.forEach(val-> {
            List<T> tree = tree(childes, val.getTreeKey(), childrenFunc);
            val.setChildren(tree);
            childrenFunc.accept(val);
        });
        if(list.isEmpty()){
            return new ArrayList<>();
        }
        return list;
    }


    /**
     *  树形结构转 list
     * @param treeVOList the tree vo list
     * @return {@link List }<{@link T }>
     */
    public static <T extends TreeBean<T>> List<T> tree2List(List<T> treeVOList){
        if(CollUtil.isEmpty(treeVOList)){
            return new ArrayList<>();
        }
        return treeVOList.stream().map(x-> {
            List<T> children = tree2List(x.getChildren());
            children.add(x);
            return children;
        }).flatMap(List::stream).collect(Collectors.toList());
    }


}
