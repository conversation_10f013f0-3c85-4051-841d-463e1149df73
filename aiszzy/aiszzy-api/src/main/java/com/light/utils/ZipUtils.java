package com.light.utils;

import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ZipUtil;
import com.google.common.collect.Maps;
import com.light.beans.ImageModel;
import com.light.core.exception.WarningException;
import com.light.core.exception.util.ThrowableUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

/**
 *  解压工具类
 * <AUTHOR>
 * @date 2025/07/10
 */
@Slf4j
public class ZipUtils {


    /**
     *  解压 图片zip 转 map 数据
     * @param inputStream the inputStream 流
     * @return {@link Map }<{@link String }, {@link byte[] }>
     */
    public  static Map<String, ImageModel> unImgZip2ImgMap(InputStream inputStream, BiFunction<BufferedImage,String, ImageModel> byteFunc) {

        Map<String, ImageModel> result = Maps.newHashMap();
        try(ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            List<String> imgExtNameList = Arrays.asList("png","jpg","jpeg");

            ZipEntry nextEntry = zipInputStream.getNextEntry();
            while(nextEntry != null) {
                // zip 文件信息
                String name = nextEntry.getName();
                String extName = FileUtil.extName(name);
                if(!imgExtNameList.contains(extName.toLowerCase())) {
                    log.error("【图片压缩包解压】 压缩包内包含非图片类型文件, 文件名称:{}", name);
                    throw new WarningException("压缩包内包含非图片类型文件，无法解析");
                }
                int pageNo = 0;
                try {
                    pageNo = NumberUtil.parseInt(name.substring(0, name.lastIndexOf(".")));
                } catch (NumberFormatException e) {
                    log.error("【图片压缩包解压】 压缩包内图片名称包含非数字内容, 文件名称:{}", name);
                    throw new WarningException("压缩包内图片名称包含非数字内容，无法解析页码");
                }
                BufferedImage img = cn.hutool.core.img.ImgUtil.read(zipInputStream);

                result.put(name, byteFunc.apply(img, name));

                nextEntry = zipInputStream.getNextEntry();
            }

            return result;
        } catch (IOException e) {
            log.error("【压缩包读取图片】 压缩包读取图片异常, 异常信息:{}", ThrowableUtil.getStackTrace(e));
            throw new WarningException("文件接失败");
        }finally {
            IoUtil.close(inputStream);
        }
    }
}
