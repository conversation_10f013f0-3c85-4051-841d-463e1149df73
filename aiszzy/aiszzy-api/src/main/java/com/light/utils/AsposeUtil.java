package com.light.utils;

import com.aspose.words.*;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import java.io.ByteArrayOutputStream;
import java.io.StringWriter;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Map;
import java.util.Properties;

public class AsposeUtil {

    public static void registerWord2412() {
        try {
            Class<?> zzodClass = Class.forName("com.aspose.words.zzod");
            Constructor<?> constructors = zzodClass.getDeclaredConstructors()[0];
            constructors.setAccessible(true);
            Object instance = constructors.newInstance(null, null);
            java.lang.reflect.Field zzWws = zzodClass.getDeclaredField("zzWws");
            zzWws.setAccessible(true);
            zzWws.set(instance, 1);
            java.lang.reflect.Field zzVZC = zzodClass.getDeclaredField("zzVZC");
            zzVZC.setAccessible(true);
            zzVZC.set(instance, 1);

            Class<?> zz83Class = Class.forName("com.aspose.words.zz83");
            constructors.setAccessible(true);
            constructors.newInstance(null, null);

            java.lang.reflect.Field zzZY4 = zz83Class.getDeclaredField("zzZY4");
            zzZY4.setAccessible(true);
            ArrayList<Object> zzwPValue = new ArrayList<>();
            zzwPValue.add(instance);
            zzZY4.set(null, zzwPValue);

            Class<?> zzXuRClass = Class.forName("com.aspose.words.zzXuR");
            java.lang.reflect.Field zzWE8 = zzXuRClass.getDeclaredField("zzWE8");
            zzWE8.setAccessible(true);
            zzWE8.set(null, 128);
            java.lang.reflect.Field zzZKj = zzXuRClass.getDeclaredField("zzZKj");
            zzZKj.setAccessible(true);
            zzZKj.set(null, false);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getVelocityContext(Map<String, Object> map, String template) {
        VelocityContext context = new VelocityContext(map);

        Properties prop = new Properties();
        prop.put("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        Velocity.init(prop);
        //获取模板列表
        StringWriter sw = new StringWriter();
        Template tpl = Velocity.getTemplate(template, "UTF-8");
        tpl.merge(context, sw);
        return sw.toString();
    }

    public static byte[] htmlToPDFByTemplate(Map<String, Object> map, String template) {
        return htmlToPDF(getVelocityContext(map, template));
    }

    public static byte[] htmlTWordByTemplate(Map<String, Object> map, String template) {
        return htmlToWord(getVelocityContext(map, template));
    }

    public static byte[] htmlToWord(String html) {
        try {
            registerWord2412();
            Document doc = new Document();
            DocumentBuilder builder = new DocumentBuilder(doc);

            builder.insertHtml(html);
            Section section = doc.getSections().get(0);
            PageSetup pageSetup = section.getPageSetup();
            pageSetup.setTopMargin(10);
            pageSetup.setLeftMargin(20);
            pageSetup.setRightMargin(20);
            pageSetup.setBottomMargin(0);


            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            doc.save(byteArrayOutputStream, SaveOptions.createSaveOptions(SaveFormat.DOTX));
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] htmlToPDF(String html) {
        try {
            registerWord2412();
            Document doc = new Document();
            DocumentBuilder builder = new DocumentBuilder(doc);

            builder.insertHtml(html);
            Section section = doc.getSections().get(0);
            PageSetup pageSetup = section.getPageSetup();
            pageSetup.setTopMargin(10);
            pageSetup.setLeftMargin(20);
            pageSetup.setRightMargin(20);
            pageSetup.setBottomMargin(0);

            builder.moveToHeaderFooter(HeaderFooterType.FOOTER_PRIMARY);
            builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER);
// 为当前页码添加字段
            builder.insertField("PAGE", "");
// 添加任何自定义文本
            builder.write(" / ");
// 为文档中的总页码添加字段
            builder.insertField("NUMPAGES", "");

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            doc.save(byteArrayOutputStream, SaveOptions.createSaveOptions(SaveFormat.PDF));
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
