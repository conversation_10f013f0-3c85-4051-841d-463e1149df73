package com.light.utils;

import com.light.core.utils.SpringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

public class UploadFileMqUtil {
    public static final String UPLADE_FILE_EXCHANGE = "upload.file.exchange";//第三方日志交换机
    public static final String UPLADE_FILE_QUEUE = "upload.file.queue";//第三方日志队列


    public static void publishMessage(String value) {
        RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
        if (rabbitTemplate == null) {
            return;
        }
        rabbitTemplate.convertAndSend(UPLADE_FILE_EXCHANGE, UPLADE_FILE_QUEUE, value);
    }
}
