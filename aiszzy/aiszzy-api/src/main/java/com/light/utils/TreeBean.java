package com.light.utils;

import java.util.List;

public interface TreeBean<T> {


    /**
     * 标识
     *
     * @return {@link String }
     */
    public String getTreeKey();

    /**
     * 父级标识
     *
     * @return {@link String }
     */
    public String getParentTreeKey();

    /**
     * 子集
     *
     * @return {@link List }<{@link T }>
     */
    public List<T> getChildren();

    /**
     * 设置子集
     *
     * @param children the children
     */
    public void setChildren(List<T> children);

}
