package com.light.utils;

import com.alibaba.fastjson.JSON;
import com.light.core.exception.WarningException;
import com.light.redis.component.RedisComponent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class ProgressService {

    @Resource
    private RedisComponent redisComponent;

    /**
     * 创建任务
     * @param taskId the task id 任务 ID
     * @param business the business 业务属性
     */
    public boolean create(String taskId, Business business) {

        Progress progress = getProgress(taskId, business);
        if(progress != null && !progress.getStatus().equals(Status.FAILURE.getVal()) && !progress.getStatus().equals(Status.SUCCESS.getVal())) {
            throw new WarningException(business.getBusiness() + "正在进行中");
        }
        if(progress == null) {
            progress = new Progress();
        }
        progress.setStatus(Status.CREATING.getVal());
        progress.setMessage(null);
        progress.setFinishNum(0);
        progress.setTaskTotalNum(0);
        progress.setKey(taskId);
        progress.setBusiness(business.getKey());
        String key = this.getProgressKey(taskId, business);
        this.redisComponent.del(this.getFinishTaskNumKey(taskId, business));
        return this.redisComponent.set(key, JSON.toJSONString(progress), 24 * 60 * 60);
    }


    /**
     *  校验是否已经存在任务
     * @param taskId the task id
     * @param business the business
     * @return boolean
     */
    public boolean hasTask(String taskId, Business business) {
        String key = this.getProgressKey(taskId, business);
        return this.redisComponent.hasKey(key);
    }


    public Progress getProgress(String taskId, Business business) {
        String key = this.getProgressKey(taskId, business);
        Object o = this.redisComponent.get(key);
        if(o == null) {
            return null;
        }
        Progress javaObject = JSON.toJavaObject(JSON.parseObject(o.toString()), Progress.class);
        Optional.ofNullable(this.redisComponent.get(this.getFinishTaskNumKey(taskId, business)))
                .ifPresent(x-> javaObject.setFinishNum(Integer.parseInt(x.toString())));

        return javaObject;
    }



    public boolean configTotalTaskNum(String taskId, Business business, int totalTaskNum) {
        Progress progress = getProgress(taskId, business);
        if(progress == null) {
            return false;
        }
        progress.setTaskTotalNum(totalTaskNum);
        String progressKey = this.getProgressKey(taskId, business);
        return this.redisComponent.set(progressKey, JSON.toJSONString(progress), 24 * 60 * 60);
    }


    public void configStatus(String taskId, Business business, Integer status, String msg) {
        Progress progress = getProgress(taskId, business);
        if(progress == null) {
            return;
        }
        progress.setStatus(status);
        progress.setMessage(msg);
        this.redisComponent.set(this.getProgressKey(taskId, business), JSON.toJSONString(progress), 24 * 60 * 60);
    }

    public void deleteProgress(String taskId, Business business) {
        String key = this.getProgressKey(taskId, business);
        this.redisComponent.del(key, this.getFinishTaskNumKey(taskId, business));
    }


    public void addFinishTaskNum(String taskId, Business business, int finishTaskNum) {
        String finishTaskNumKey = getFinishTaskNumKey(taskId, business);
        this.redisComponent.incr(finishTaskNumKey, finishTaskNum);
        this.redisComponent.expire(finishTaskNumKey, 24 * 60 * 60);
    }






    private String getFinishTaskNumKey(String taskId, Business business) {
        return this.getProgressKey(taskId, business) + ":finishTaskNum";
    }


    private String getProgressKey(String taskId, Business business) {
        return "cache:progress:" + business.getKey() + ":" + taskId;
    }


    @Data
    public static class Progress {

        /**
         * 唯一标识
         */
        private String key;

        /**
         * 业务属性
         */
        private String business;

        /**
         * 总任务数量
         */
        private Integer taskTotalNum = 0;

        /**
         * 完成任务数量
         */
        private Integer finishNum = 0;

        /**
         *  状态 1 创建中 2 进行中 3 成功 4 失败
         */
        private Integer status = 1;

        /**
         * 描述
         */
        private String message;

    }

    @AllArgsConstructor
    @Getter
    public enum Business {

        USER_PAPER_ANALYSIS("userpaper:analysis", "校本解析"),
        PRACTICE_BOOK_ANALYSIS("practiceBook:analysis", "教辅解析"),

        ;

        private final String key;

        private final String business;
    }

    @AllArgsConstructor
    @Getter
    public enum Status {
        //状态 1 创建中 2 进行中 3 成功 4 失败
        // 枚举值
        CREATING(1, "创建中"),
        IN_PROGRESS(2, "进行中"),
        SUCCESS(3, "成功"),
        FAILURE(4, "失败");

        private final int val;

        private String desc;
    }
}
