package com.light.utils;

import com.light.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StringUtil extends StringUtils {


    public static boolean containsHtmlTags(String text) {
        if (org.apache.commons.lang3.StringUtils.isBlank(text)) {
            return false;
        }
        // 使用正则表达式检查是否包含HTML标签
        // 匹配 <tag> 或 </tag> 或 <tag/> 格式的标签
        return text.matches(".*<[^>]+>.*");
    }


    /**
     * 从HTML文本中提取纯文本内容
     * 移除所有HTML标签，保留文本内容
     *
     * @param htmlText 包含HTML标签的文本
     * @return 提取后的纯文本内容
     */
    public static String extractTextFromHtml(String htmlText) {
        if (org.apache.commons.lang3.StringUtils.isBlank(htmlText)) {
            return htmlText;
        }

        try {
            // 移除HTML标签，保留文本内容
            String text = htmlText.replaceAll("<[^>]+>", "");

            // 处理HTML实体字符
            text = text.replace("&nbsp;", " ")
                    .replace("&lt;", "<")
                    .replace("&gt;", ">")
                    .replace("&amp;", "&")
                    .replace("&quot;", "\"")
                    .replace("&#39;", "'")
                    .replace("&apos;", "'");

            // 清理多余的空白字符
            text = text.replaceAll("\\s+", " ").trim();

            return text;
        } catch (Exception e) {
            log.warn("HTML文本提取失败，返回原始文本: {}", e.getMessage());
            return htmlText;
        }
    }
}
