package com.light.beans;

import java.util.List;

import lombok.Data;

/**
 * @Classname similarRecommendBo
 * @Description 相似题推荐请求
 * @Date 2025/4/9 20:57
 * @Created by admin
 */
@Data
public class SimilarRecommendBo {
    /**
     * 课程id
     */
    private Integer course_id;
    /**
     * 公式图片格式，支持两种：png或svg，默认是svg
     */
    private String formula_pic_format;
    /**
     * 教材ID：非必填，此参数用于过滤超纲试题；高中课程，教材ID必填，按照具体教材过滤超纲试题；小初课程，入参教材版本ID或教材ID，按教材版本过滤超纲试题，否则按主流教材版本过滤。
     */
    private Integer textbook_id;
    /**
     * 试题知识点ID集合
     */
    private List<Integer> kpoint_ids;
    /**
     * 试题类型ID集合
     */
    private List<Integer> type_ids;
    /**
     * 返回的最大试题数，默认为5道题
     */
    private Integer count;
    /**
     * 题干文本（不超过2000字）（题干文本和试题ID必传其中一个）
     */
    private String text;
    /**
     * 教材版本ID：非必填，此参数用于过滤超纲试题；高中课程，教材ID必填，按照具体教材过滤超纲试题；小初课程，入参教材版本ID或教材ID，按教材版本过滤超纲试题，否则按主流教材版本过滤。
     */
    private Integer version_id;
    /**
     * 是否过滤超纲试题：0 不过滤、1 过滤，默认为不过滤；语文、英语学科课程此策略不生效
     */
    private Integer filter_exceeds_scope_ques;
    /**
     * 试题ID（题干文本和试题ID必传其中一个）
     */
    private String question_id;
    /**
     * 试题难度等级ID集合（17 容易 18 较易 19 一般 20 较难 21 困难），最多传5个
     */
    private List<Integer> difficulty_levels;
}
