package com.light.beans;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;

/**
 * 学科网API通用响应包装类
 * 
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
public class XkwApiResponseVo<T> extends HashMap<String, Object> {

    /** 响应码 */
    private Integer code = 200;

    /** 响应消息 */
    private String message;

    /** 响应数据 */
    private T data;

    /** 是否成功 */
    public boolean isSuccess() {
        return code != null && (code == 200 || code == 2000000);
    }
}
