package com.light.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *  好未来框体 响应 VO
 * <AUTHOR>
 * @date 2025/07/10
 */
@Data
public class HwlOpenComeducationHandTextVO {

    @JSONField(name = "poses")                             // 旋转后每行的坐标（四个点）
    private List<Point> poses;                              // 识别置信度
    @JSONField(name = "texts")                          // 字符级位置信息（可选）
    private String texts;                               // 识别结果（top10）
    @JSONField(name = "Confidence")
    private BigDecimal Confidence;                            // 识别方差
    @JSONField(name = "stdofconfi")
    private BigDecimal stdofconfi;                            // 识别方差

}

