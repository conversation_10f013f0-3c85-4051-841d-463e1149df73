package com.light.beans;

import lombok.Data;
import java.util.List;

/**
 * 学科网题目信息
 * 
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
public class XkwQuestionVo {

    /** 试题ID */
    private String id;

    /** 试题题干（HTML格式） */
    private String stem;

    /** 试题答案（HTML格式） */
    private String answer;

    /** 试题解析（HTML格式） */
    private String explanation;

    /** 试题类型 */
    private IdNamePair<String> type;

    /** 试题类型ID */
    private String type_id;

    /** 课程 */
    private IdNamePair<Integer> course;

    /** 课程ID */
    private Integer course_id;

    /**
     * 相似度 0 ～ 100 , 相似度越大
     */
    private Integer similarity;

    /** 试题难度等级（17 容易，18 较易，19 一般，20 较难，21 困难） */
    private Integer difficulty_level;

    /** 试题难度，0~1之间的数字，值越小难度越大 */
    private Double difficulty;

    /** 知识点ID列表 */
    private List<String> kpoint_ids;

    /** 知识点列表 */
    private List<IdNamePair<Integer>> kpoints;

    /** 标签ID列表 */
    private List<String> tag_ids;

    /** 标签列表 */
    private List<IdNamePair<Integer>> tags;

    /** 试题出现在试卷中的年份，可能多个 */
    private List<Integer> years;

    /** 教材目录ID列表 */
    private List<String> catalog_ids;

    /** 教材目录列表 */
    private List<IdNamePair<Integer>> catalogs;

    /** 试卷类型ID列表 */
    private List<String> paper_type_ids;

    /** 单词ID列表 */
    private List<String> en_word_ids;

    /** 单词列表 */
    private List<IdNamePair<Integer>> en_words;

    /** 试题是否包含媒体内容（0 没有，1 有音频 2 有解题视频 3 音频和解题视频都有） */
    private Integer media;

    /** 解题视频封面 */
    private List<String> exp_video_posters;

    /** 在线作答（0 不支持，1 支持） */
    private Integer answer_scoreable;

    /** 试题入库日期 */
    private String create_date;

    /** 推题方式，0=算法推荐，1=人工推荐 */
    private Integer recommend_mode;
}
