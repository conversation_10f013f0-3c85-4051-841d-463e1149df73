package com.light.beans;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 *  好未来框体 响应 VO
 * <AUTHOR>
 * @date 2025/07/10
 */
@Data
public class HwlOpenAutomaticBoxVO {

    @J<PERSON>NField(name = "full_time")
    private int fullTime;

    @JSONField(name = "processTime")
    private int processTime;

    private int rotate;

    private List<ItemData> data;

    @JSONField(name = "single")
    private List<SingleData> single;



    @Data
    public static class ItemData {

        @JSONField(name = "item_type")
        private int itemType;

        @JSONField(name = "item_level")
        private Integer itemLevel; // Nullable field

        @JSONField(name = "item_position_rotate")
        private List<List<Integer>> itemPositionRotate;

        @JSONField(name = "item_position_show")
        private List<List<Integer>> itemPositionShow;

        @JSONField(name = "item_position")
        private List<List<Integer>> itemPosition;

    }


    @Data
    public static class SingleData {

        @JSONField(name = "item_position_rotate")
        private List<List<Integer>> itemPositionRotate;

        @JSONField(name = "item_position_show")
        private List<List<Integer>> itemPositionShow;

        @JSONField(name = "item_position")
        private List<List<Integer>> itemPosition;

    }
}

