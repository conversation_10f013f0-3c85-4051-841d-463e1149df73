package com.light.beans;

import lombok.Data;
import java.util.List;

@Data
public class ImageRequestBo {
    //至少需要填写其中一项，当同时存在时，优先使用image_base64
    //图片的url地址
    private String imageUrl;
    //base64编码的二进制图片数据
    private String imageBase64;
    //锚点位置[x,y]，以图像左上角为坐标原点	若不填写，或者填写非法值，则以图片中心点作为锚点位置(单位：像素)
    private List<Integer> anchor;

    /**
     * 裁剪图片坐标信息
     */
    private List<ImageCoordinates> coordinates;

    /**
     * 搜题图片
     */
    private String image;
    /**
     * 搜题的关键字
     */
    private String words;

    // ========== 教育通用OCR相关参数 ==========
    /**
     * 功能类型：0-完整识别，1-印刷文字识别，2-手写文字识别，3-印刷行图识别，4-手写行图识别，5-公式识别
     */
    private Integer function;

    /**
     * 是否检测旋转图片朝向，默认true
     */
    private Boolean detectDirection;

    /**
     * 文理科选择：liberat-文科，science-理科
     */
    private String subject;

    /**
     * 是否打印图片中的文字，默认true
     */
    private Boolean textInImage;

    /**
     * 是否打印表格中的文字，默认true
     */
    private Boolean textInTable;

    /**
     * 是否自适应学拍拍结果，默认false
     */
    private Boolean isXuePaiPai;

    // ========== 作业批改相关参数 ==========
    /**
     * 作业批改图片URL数组，单次最多支持8张图片
     */
    private List<String> imageUrls;
}
