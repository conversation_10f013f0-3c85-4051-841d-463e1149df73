package com.light.aiszzy.practiceBook.entity.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PracticeBookCatalogExcel implements Serializable {

    /**
     *  目录级别
     */
    @NotNull(message = "目录不能为空")
    @Excel(name = "目录", addressList = true, replace = {"一级目录_1", "二级目录_2", "三级目录_3"})
    private String level;

    /**
     * 目录名称
     */
    @NotNull(message = "目录名称不能为空")
    @Excel(name = "目录名称")
    private String name;

    /**
     * 开始页
     */
    @NotNull(message = "开始页码不能为空")
    @Excel(name = "开始页码")
    private Long startPage;

    /**
     * 结束页
     */
    @NotNull(message = "结束页码不能为空")
    @Excel(name = "结束页码")
    private Long endPage;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    private String oid;

    private String parentOid = "0";

    private String superiorsOids = "0";

    private Integer orderNum;


    public PracticeBookCatalogBo toBookCatalogBo(String practiceBookOid) {
        PracticeBookCatalogBo vo = new PracticeBookCatalogBo();
        vo.setOid(this.getOid());
        vo.setPracticeBookOid(practiceBookOid);
        vo.setName(this.getName());
        vo.setPageStart(this.getStartPage());
        vo.setPageEnd(this.getEndPage());
        vo.setParentOid(this.getParentOid());
        vo.setSuperiorsOids(this.getSuperiorsOids());
        vo.setOrderNum(this.getOrderNum());
        vo.setLevel(Integer.parseInt(this.getLevel().trim()));
        return vo;
    }
}
