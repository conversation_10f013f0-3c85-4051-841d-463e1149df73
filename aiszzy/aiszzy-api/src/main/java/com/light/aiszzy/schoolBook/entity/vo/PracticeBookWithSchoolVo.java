package com.light.aiszzy.schoolBook.entity.vo;

import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 教辅与学校关联信息VO - 继承教辅基础信息
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PracticeBookWithSchoolVo extends PracticeBookVo {

    private static final long serialVersionUID = 1L;

    // ========== 教辅来源字段 ===========
    @ApiModelProperty("来源：1学科网教辅，2凤凰教辅")
    private Integer practiceBookSourceType;

    // ========== 学校开通信息字段 ==========
    /**
     * 开通记录自增主键
     */
    @ApiModelProperty("开通记录自增主键")
    private Long schoolBookId;

    /**
     * 开通记录OID
     */
    @ApiModelProperty("开通记录OID")
    private String schoolBookOid;

    /**
     * 学校CODE
     */
    @ApiModelProperty("学校CODE")
    private String orgCode;

    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String orgName;

    /**
     * 学校区域
     */
    @ApiModelProperty("学校区域")
    private String orgAreaName;

    /**
     * 学校区域CODE
     */
    @ApiModelProperty("学校区域CODE")
    private String orgAreaCode;

    /**
     * 学校市区
     */
    @ApiModelProperty("学校市区")
    private String orgCityName;

    /**
     * 学校市区CODE
     */
    @ApiModelProperty("学校市区CODE")
    private String orgCityCode;

    /**
     * 学校省
     */
    @ApiModelProperty("学校省")
    private String orgProvinceName;

    /**
     * 学校省CODE
     */
    @ApiModelProperty("学校省CODE")
    private String orgProvinceCode;

    /**
     * 开通状态 1启用 2停用
     */
    @ApiModelProperty("开通状态")
    private Integer schoolBookStatus;

    /**
     * 开通开始日期
     */
    @ApiModelProperty("开通开始日期")
    private Date startDate;

    /**
     * 开通结束日期
     */
    @ApiModelProperty("开通结束日期")
    private Date endDate;

    /**
     * 教辅或作业本类型
     */
    @ApiModelProperty("教辅或作业本类型")
    private String bookType;

    /**
     * 计算状态（1启用，2停用，3使用中，4即将到期，5已到期，6未开始）
     */
    @ApiModelProperty("计算状态（1启用，2停用，3使用中，4即将到期，5已到期，6未开始）")
    private Integer computedStatus;

    /**
     * 开通记录创建时间
     */
    @ApiModelProperty("开通记录创建时间")
    private Date schoolBookCreateTime;

    /**
     * 开通记录更新时间
     */
    @ApiModelProperty("开通记录更新时间")
    private Date schoolBookUpdateTime;

    /**
     * 开通记录创建者
     */
    @ApiModelProperty("开通记录创建者")
    private String schoolBookCreateBy;

    /**
     * 开通记录更新者
     */
    @ApiModelProperty("开通记录更新者")
    private String schoolBookUpdateBy;
}
