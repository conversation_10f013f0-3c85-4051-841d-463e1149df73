package  com.light.aiszzy.apiRequestLog.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.apiRequestLog.entity.vo.ApiRequestLogVo;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogBo;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 第三方请求日志表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface ApiRequestLogApi  {

	/**
	 * 查询第三方请求日志表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/apiRequestLog/pageList")
	@ApiOperation(value = "分页查询第三方请求日志表",httpMethod = "POST")
	AjaxResult<PageInfo<ApiRequestLogVo>> getApiRequestLogPageListByCondition(@RequestBody ApiRequestLogConditionBo condition);

	/**
	 * 查询所有第三方请求日志表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/apiRequestLog/list")
	@ApiOperation(value = "查询所有第三方请求日志表",httpMethod = "POST")
	AjaxResult<List<ApiRequestLogVo>> getApiRequestLogListByCondition(@RequestBody ApiRequestLogConditionBo condition);


	/**
	 * 新增第三方请求日志表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/apiRequestLog/add")
	@ApiOperation(value = "新增第三方请求日志表",httpMethod = "POST")
	AjaxResult addApiRequestLog(@Validated @RequestBody ApiRequestLogBo apiRequestLogBo);

	/**
	 * 修改第三方请求日志表
	 * @param apiRequestLogBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/apiRequestLog/update")
	@ApiOperation(value = "修改第三方请求日志表",httpMethod = "POST")
	AjaxResult updateApiRequestLog(@Validated @RequestBody ApiRequestLogBo apiRequestLogBo);

	/**
	 * 查询第三方请求日志表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/apiRequestLog/detail")
	@ApiOperation(value = "查询第三方请求日志表详情",httpMethod = "GET")
	AjaxResult<ApiRequestLogVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除第三方请求日志表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/apiRequestLog/delete")
	@ApiOperation(value = "删除第三方请求日志表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

