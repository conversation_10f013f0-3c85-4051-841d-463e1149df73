package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkClassInfoVo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业班级信息，包含疑问项汇总接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
public interface HomeworkClassInfoApi  {

	/**
	 * 查询作业班级信息，包含疑问项汇总列表
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@PostMapping("/homeworkClassInfo/pageList")
	@ApiOperation(value = "分页查询作业班级信息，包含疑问项汇总",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkClassInfoVo>> getHomeworkClassInfoPageListByCondition(@RequestBody HomeworkClassInfoConditionBo condition);

	/**
	 * 查询所有作业班级信息，包含疑问项汇总列表
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@PostMapping("/homeworkClassInfo/list")
	@ApiOperation(value = "查询所有作业班级信息，包含疑问项汇总",httpMethod = "POST")
	AjaxResult<List<HomeworkClassInfoVo>> getHomeworkClassInfoListByCondition(@RequestBody HomeworkClassInfoConditionBo condition);


	/**
	 * 新增作业班级信息，包含疑问项汇总
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@PostMapping("/homeworkClassInfo/add")
	@ApiOperation(value = "新增作业班级信息，包含疑问项汇总",httpMethod = "POST")
	AjaxResult addHomeworkClassInfo(@Validated @RequestBody HomeworkClassInfoBo homeworkClassInfoBo);

	/**
	 * 修改作业班级信息，包含疑问项汇总
	 * @param homeworkClassInfoBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@PostMapping("/homeworkClassInfo/update")
	@ApiOperation(value = "修改作业班级信息，包含疑问项汇总",httpMethod = "POST")
	AjaxResult updateHomeworkClassInfo(@Validated @RequestBody HomeworkClassInfoBo homeworkClassInfoBo);

	/**
	 * 查询作业班级信息，包含疑问项汇总详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@GetMapping("/homeworkClassInfo/detail")
	@ApiOperation(value = "查询作业班级信息，包含疑问项汇总详情",httpMethod = "GET")
	AjaxResult<HomeworkClassInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业班级信息，包含疑问项汇总
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-22 17:01:24
	 */
	@GetMapping("/homeworkClassInfo/delete")
	@ApiOperation(value = "删除作业班级信息，包含疑问项汇总",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

