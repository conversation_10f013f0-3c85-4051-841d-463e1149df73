package com.light.aiszzy.xkw.xkwPaperInfo.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 题目信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class XkwPaperInfoVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 试卷id
     */
    private String paperId;

    /**
     * 副标题
     */
    private String headSubTitle;

    /**
     * 试卷信息
     */
    private String headTestInfo;

    /**
     * 学生填写部分
     */
    private String headStudentInput;

    /**
     * 试卷标题
     */
    private String headMainTitle;

    /**
     * 提示
     */
    private String headNotice;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 试卷题目
     */
    private String bodyQuestions;

    /**
     * 作业oid
     */
    private String homeworkOid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除（1：正常 2：删除）
     */
    private Long isDelete;

}
