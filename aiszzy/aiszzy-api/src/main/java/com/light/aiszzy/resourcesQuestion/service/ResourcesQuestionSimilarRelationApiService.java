package com.light.aiszzy.resourcesQuestion.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.resourcesQuestion.api.ResourcesQuestionSimilarRelationApi;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 资源库题目相似题接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "resourcesQuestionSimilarRelationApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = ResourcesQuestionSimilarRelationApiService.ResourcesQuestionSimilarRelationApiFallbackFactory.class)
@Component
public interface ResourcesQuestionSimilarRelationApiService  extends ResourcesQuestionSimilarRelationApi {

	@Component
	class ResourcesQuestionSimilarRelationApiFallbackFactory implements FallbackFactory<ResourcesQuestionSimilarRelationApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(ResourcesQuestionSimilarRelationApiService.ResourcesQuestionSimilarRelationApiFallbackFactory.class);
		@Override
		public ResourcesQuestionSimilarRelationApiService create(Throwable cause) {
			ResourcesQuestionSimilarRelationApiService.ResourcesQuestionSimilarRelationApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new ResourcesQuestionSimilarRelationApiService() {
				public AjaxResult getResourcesQuestionSimilarRelationPageListByCondition(ResourcesQuestionSimilarRelationConditionBo condition){
					return AjaxResult.fail("资源库题目相似题查询失败");
				}
				public AjaxResult getResourcesQuestionSimilarRelationListByCondition(ResourcesQuestionSimilarRelationConditionBo condition){
					return AjaxResult.fail("资源库题目相似题查询失败");
				}

				public AjaxResult addResourcesQuestionSimilarRelation(ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo){
					return AjaxResult.fail("资源库题目相似题新增失败");
				}

				public AjaxResult updateResourcesQuestionSimilarRelation(ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo){
					return AjaxResult.fail("资源库题目相似题更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("资源库题目相似题获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("资源库题目相似题删除失败");
				}
			};
		}
	}
}