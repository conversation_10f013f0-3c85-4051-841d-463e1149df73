package com.light.aiszzy.resourcesQuestion.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionSimilarRelationVo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 资源库题目相似题接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface ResourcesQuestionSimilarRelationApi  {

	/**
	 * 查询资源库题目相似题列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/resourcesQuestionSimilarRelation/pageList")
	@ApiOperation(value = "分页查询资源库题目相似题",httpMethod = "POST")
	AjaxResult<PageInfo<ResourcesQuestionSimilarRelationVo>> getResourcesQuestionSimilarRelationPageListByCondition(@RequestBody ResourcesQuestionSimilarRelationConditionBo condition);

	/**
	 * 查询所有资源库题目相似题列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/resourcesQuestionSimilarRelation/list")
	@ApiOperation(value = "查询所有资源库题目相似题",httpMethod = "POST")
	AjaxResult<List<ResourcesQuestionSimilarRelationVo>> getResourcesQuestionSimilarRelationListByCondition(@RequestBody ResourcesQuestionSimilarRelationConditionBo condition);


	/**
	 * 新增资源库题目相似题
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/resourcesQuestionSimilarRelation/add")
	@ApiOperation(value = "新增资源库题目相似题",httpMethod = "POST")
	AjaxResult addResourcesQuestionSimilarRelation(@Validated @RequestBody ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo);

	/**
	 * 修改资源库题目相似题
	 * @param resourcesQuestionSimilarRelationBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/resourcesQuestionSimilarRelation/update")
	@ApiOperation(value = "修改资源库题目相似题",httpMethod = "POST")
	AjaxResult updateResourcesQuestionSimilarRelation(@Validated @RequestBody ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo);

	/**
	 * 查询资源库题目相似题详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/resourcesQuestionSimilarRelation/detail")
	@ApiOperation(value = "查询资源库题目相似题详情",httpMethod = "GET")
	AjaxResult<ResourcesQuestionSimilarRelationVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除资源库题目相似题
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/resourcesQuestionSimilarRelation/delete")
	@ApiOperation(value = "删除资源库题目相似题",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

