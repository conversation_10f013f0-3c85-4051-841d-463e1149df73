package com.light.aiszzy.practiceBook.entity.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.light.aiszzy.practiceBook.entity.bean.PracticeBookPositionQuestion;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 教辅目录每页图片记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class PracticeBookPageVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每一条目录记录
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 二维码使用，8位，字母加数字，尝试10次，重复返回报错
     */
    private String practiceBookCode;

    /**
     * 教辅OID
     */
    private String practiceBookOid;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 修改后框题个数（实际）
     */
    private Long questionNum;

    /**
     * 解析框题个数
     */
    private Long analysisQuestionNum;

    /**
     * 完成题目数量
     */
    private Long finishQuestionNum;

    /**
     * 解析结果
     */
    private String analysisJson;

    private String questionJson;

    /**
     * 整页标注是否完成  0：未完成 1：完成
     */
    private Long status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;



    public List<PracticeBookPositionQuestion> getPositionQuestionList() {
        if(StrUtil.isNotEmpty(this.questionJson)) {
            return JSON.parseArray(questionJson, PracticeBookPositionQuestion.class);
        }
        return Collections.emptyList();
    }

}
