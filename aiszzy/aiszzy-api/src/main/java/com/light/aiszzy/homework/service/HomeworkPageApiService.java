package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkPageApi;
import com.light.aiszzy.homework.entity.bo.HomeworkPageBo;
import com.light.aiszzy.homework.entity.bo.HomeworkPageConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业每页题目坐标信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@FeignClient(contextId = "homeworkPageApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkPageApiService.HomeworkPageApiFallbackFactory.class)
@Component
public interface HomeworkPageApiService  extends HomeworkPageApi {

	@Component
	class HomeworkPageApiFallbackFactory implements FallbackFactory<HomeworkPageApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkPageApiFallbackFactory.class);
		@Override
		public HomeworkPageApiService create(Throwable cause) {
			HomeworkPageApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkPageApiService() {
				public AjaxResult getHomeworkPagePageListByCondition(HomeworkPageConditionBo condition){
					return AjaxResult.fail("作业每页题目坐标信息查询失败");
				}
				public AjaxResult getHomeworkPageListByCondition(HomeworkPageConditionBo condition){
					return AjaxResult.fail("作业每页题目坐标信息查询失败");
				}

				public AjaxResult addHomeworkPage(HomeworkPageBo homeworkPageBo){
					return AjaxResult.fail("作业每页题目坐标信息新增失败");
				}

				public AjaxResult updateHomeworkPage(HomeworkPageBo homeworkPageBo){
					return AjaxResult.fail("作业每页题目坐标信息更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业每页题目坐标信息获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业每页题目坐标信息删除失败");
				}
			};
		}
	}
}