package com.light.aiszzy.homeworkResult.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultDoubtVo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 扫描结构每页处理结果
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkResultDoubtApi  {

	/**
	 * 查询扫描结构每页处理结果列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResultDoubt/pageList")
	@ApiOperation(value = "分页查询扫描结构每页处理结果",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkResultDoubtVo>> getHomeworkResultDoubtPageListByCondition(@RequestBody HomeworkResultDoubtConditionBo condition);

	/**
	 * 查询所有扫描结构每页处理结果列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResultDoubt/list")
	@ApiOperation(value = "查询所有扫描结构每页处理结果",httpMethod = "POST")
	AjaxResult<List<HomeworkResultDoubtVo>> getHomeworkResultDoubtListByCondition(@RequestBody HomeworkResultDoubtConditionBo condition);


	/**
	 * 新增扫描结构每页处理结果
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResultDoubt/add")
	@ApiOperation(value = "新增扫描结构每页处理结果",httpMethod = "POST")
	AjaxResult addHomeworkResultDoubt(@Validated @RequestBody HomeworkResultDoubtBo homeworkResultDoubtBo);

	/**
	 * 修改扫描结构每页处理结果
	 * @param homeworkResultDoubtBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResultDoubt/update")
	@ApiOperation(value = "修改扫描结构每页处理结果",httpMethod = "POST")
	AjaxResult updateHomeworkResultDoubt(@Validated @RequestBody HomeworkResultDoubtBo homeworkResultDoubtBo);

	/**
	 * 查询扫描结构每页处理结果详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkResultDoubt/detail")
	@ApiOperation(value = "查询扫描结构每页处理结果详情",httpMethod = "GET")
	AjaxResult<HomeworkResultDoubtVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除扫描结构每页处理结果
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkResultDoubt/delete")
	@ApiOperation(value = "删除扫描结构每页处理结果",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

