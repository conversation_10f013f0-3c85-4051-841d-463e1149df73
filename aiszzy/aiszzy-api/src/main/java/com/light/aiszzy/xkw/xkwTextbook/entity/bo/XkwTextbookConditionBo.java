package com.light.aiszzy.xkw.xkwTextbook.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 教材
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwTextbookConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 	课程ID
	 */
	@ApiModelProperty("	课程ID")
	private Long courseId;

	/**
	 * 排序值
	 */
	@ApiModelProperty("排序值")
	private Long ordinal;

	/**
	 * 册别
	 */
	@ApiModelProperty("册别")
	private String volume;

	/**
	 * 教材版本ID
	 */
	@ApiModelProperty("教材版本ID")
	private Long versionId;

	/**
	 * 年级ID
	 */
	@ApiModelProperty("年级ID")
	private Long gradeId;

	/**
	 * 学期,可用值:LAST,NEXT,ALL
	 */
	@ApiModelProperty("学期,可用值:LAST,NEXT,ALL")
	private String term;

	/**
	 * 教材名称
	 */
	@ApiModelProperty("教材名称")
	private String name;

}
