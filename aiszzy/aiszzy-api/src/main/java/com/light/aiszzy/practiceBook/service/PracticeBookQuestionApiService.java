package com.light.aiszzy.practiceBook.service;


import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.practiceBook.api.PracticeBookQuestionApi;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionChangeBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 教辅题目表，用于存储题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "practiceBookQuestionApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = PracticeBookQuestionApiService.PracticeBookQuestionApiFallbackFactory.class)
@Component
public interface PracticeBookQuestionApiService  extends PracticeBookQuestionApi {

	@Component
	class PracticeBookQuestionApiFallbackFactory implements FallbackFactory<PracticeBookQuestionApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(PracticeBookQuestionApiService.PracticeBookQuestionApiFallbackFactory.class);
		@Override
		public PracticeBookQuestionApiService create(Throwable cause) {
			PracticeBookQuestionApiService.PracticeBookQuestionApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new PracticeBookQuestionApiService() {
				public AjaxResult getPracticeBookQuestionPageListByCondition(PracticeBookQuestionConditionBo condition){
					return AjaxResult.fail("教辅题目表，用于存储题目信息查询失败");
				}
				public AjaxResult getPracticeBookQuestionListByCondition(PracticeBookQuestionConditionBo condition){
					return AjaxResult.fail("教辅题目表，用于存储题目信息查询失败");
				}

				@Override
				public AjaxResult<List<PracticeBookQuestionVo>> getPracticeBookQuestionWithBodyListByCondition(PracticeBookQuestionConditionBo condition) {
					return AjaxResult.fail("教辅题目表，用于存储题目信息查询失败");
				}

				public AjaxResult saveDataByPosition(PracticeBookQuestionBo practiceBookQuestionBo){
					return AjaxResult.fail("教辅题目表，用于存储题目信息新增失败");
				}

				public AjaxResult updatePracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo){
					return AjaxResult.fail("教辅题目表，用于存储题目信息更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("教辅题目表，用于存储题目信息获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("教辅题目表，用于存储题目信息删除失败");
				}

				public AjaxResult markPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo){
					return AjaxResult.fail("完成标注失败");
				}

				public AjaxResult cancelPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo){
					return AjaxResult.fail("取消划题失败");
				}

				public AjaxResult querySimilarQuestion(PracticeBookQuestionChangeBo practiceBookQuestionChangeBo){
					return AjaxResult.fail("查询相似题失败");
				}

				public AjaxResult setSimilarQuestion(PracticeBookQuestionChangeBo practiceBookQuestionChangeBo){
					return AjaxResult.fail("设为相似题失败");
				}

				@Override
				public AjaxResult<PracticeBookQuestionVo> getOrInitQuestionInfoByOid(String oid, PracticeBookQuestionBo conditionBo) {
					return AjaxResult.fail("获取题目信息失败");
				}

				@Override
				public AjaxResult reFetchQuestionInfoByOid(PracticeBookQuestionBo bo) {
					return AjaxResult.fail("重新加载题目信息失败");
				}
			};
		}
	}
}