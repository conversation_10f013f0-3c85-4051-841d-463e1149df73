package com.light.aiszzy.xkw.xkwTextbookCatalog.service;


import com.light.aiszzy.xkw.xkwTextbookCatalog.api.XkwTextbookCatalogApi;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 教材目录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "xkwTextbookCatalogApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwTextbookCatalogApiService.XkwTextbookCatalogApiFallbackFactory.class)
@Component
public interface XkwTextbookCatalogApiService  extends XkwTextbookCatalogApi {

	@Component
	class XkwTextbookCatalogApiFallbackFactory implements FallbackFactory<XkwTextbookCatalogApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwTextbookCatalogApiService.XkwTextbookCatalogApiFallbackFactory.class);
		@Override
		public XkwTextbookCatalogApiService create(Throwable cause) {
			XkwTextbookCatalogApiService.XkwTextbookCatalogApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwTextbookCatalogApiService() {
				public AjaxResult getXkwTextbookCatalogPageListByCondition(XkwTextbookCatalogConditionBo condition){
					return AjaxResult.fail("教材目录查询失败");
				}
				public AjaxResult getXkwTextbookCatalogListByCondition(XkwTextbookCatalogConditionBo condition){
					return AjaxResult.fail("教材目录查询失败");
				}


			};
		}
	}
}