package com.light.aiszzy.statistics.service;


import com.light.aiszzy.statistics.api.SchoolGradeQuestionStatisticsApi;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业年级题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@FeignClient(contextId = "schoolGradeQuestionStatisticsApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = SchoolGradeQuestionStatisticsApiService.SchoolGradeQuestionStatisticsApiFallbackFactory.class)
@Component
public interface SchoolGradeQuestionStatisticsApiService  extends SchoolGradeQuestionStatisticsApi {

	@Component
	class SchoolGradeQuestionStatisticsApiFallbackFactory implements FallbackFactory<SchoolGradeQuestionStatisticsApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(SchoolGradeQuestionStatisticsApiFallbackFactory.class);
		@Override
		public SchoolGradeQuestionStatisticsApiService create(Throwable cause) {
			SchoolGradeQuestionStatisticsApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new SchoolGradeQuestionStatisticsApiService() {
				public AjaxResult getSchoolGradeQuestionStatisticsPageListByCondition(SchoolGradeQuestionStatisticsConditionBo condition){
					return AjaxResult.fail("作业年级题目正确率查询失败");
				}
				public AjaxResult getSchoolGradeQuestionStatisticsListByCondition(SchoolGradeQuestionStatisticsConditionBo condition){
					return AjaxResult.fail("作业年级题目正确率查询失败");
				}

				public AjaxResult addSchoolGradeQuestionStatistics(SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo){
					return AjaxResult.fail("作业年级题目正确率新增失败");
				}

				public AjaxResult updateSchoolGradeQuestionStatistics(SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo){
					return AjaxResult.fail("作业年级题目正确率更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业年级题目正确率获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业年级题目正确率删除失败");
				}
			};
		}
	}
}