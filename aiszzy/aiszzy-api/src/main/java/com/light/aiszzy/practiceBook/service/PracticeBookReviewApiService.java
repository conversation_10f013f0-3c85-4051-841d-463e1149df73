package com.light.aiszzy.practiceBook.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.practiceBook.api.PracticeBookReviewApi;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 教辅信息审核接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@FeignClient(contextId = "practiceBookReviewApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = PracticeBookReviewApiService.PracticeBookReviewApiFallbackFactory.class)
@Component
public interface PracticeBookReviewApiService  extends PracticeBookReviewApi {

	@Component
	class PracticeBookReviewApiFallbackFactory implements FallbackFactory<PracticeBookReviewApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(PracticeBookReviewApiService.PracticeBookReviewApiFallbackFactory.class);
		@Override
		public PracticeBookReviewApiService create(Throwable cause) {
			PracticeBookReviewApiService.PracticeBookReviewApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new PracticeBookReviewApiService() {
				public AjaxResult getPracticeBookReviewPageListByCondition(PracticeBookReviewConditionBo condition){
					return AjaxResult.fail("教辅信息审核查询失败");
				}
				public AjaxResult getPracticeBookReviewListByCondition(PracticeBookReviewConditionBo condition){
					return AjaxResult.fail("教辅信息审核查询失败");
				}

				public AjaxResult addPracticeBookReview(PracticeBookReviewBo practiceBookReviewBo){
					return AjaxResult.fail("教辅信息审核新增失败");
				}

				public AjaxResult updatePracticeBookReview(PracticeBookReviewBo practiceBookReviewBo){
					return AjaxResult.fail("教辅信息审核更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("教辅信息审核获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("教辅信息审核删除失败");
				}
			};
		}
	}
}