package com.light.aiszzy.userPaper.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 资源库试卷表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class UserPaperQuestionBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 资源库试卷表id
	 */
	@ApiModelProperty("资源库试卷表id")
	private Long userPaperQuestionId;
	
	/**
	 * 主键id
	 */
	@ApiModelProperty("主键id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 学校code
	 */
	@ApiModelProperty("学校code")
	private String orgCode;
	/**
	 * 上传试卷oid
	 */
	@ApiModelProperty("上传试卷oid")
	private String userPaperOid;
	/**
	 * 学生上传试卷page的oid
	 */
	@ApiModelProperty("学生上传试卷page的oid")
	private String userPaperPageOid;
	/**
	 * 校本资源题目oid
	 */
	@ApiModelProperty("校本资源题目oid")
	private String schoolResourceQuestionOid;
	/**
	 * 图片地址
	 */
	@ApiModelProperty("图片地址")
	private String imageUrl;
	/**
	 * 示例x_96,y_496,w_1100,h_275
	 */
	@ApiModelProperty("示例x_96,y_496,w_1100,h_275")
	private String position;
	/**
	 * 标注状态 0 未标注 1 已标注
	 */
	@ApiModelProperty("标注状态 0 未标注 1 已标注")
	private Integer markStatus;
	/**
	 * 页面
	 */
	@ApiModelProperty("页面")
	private Integer pageNum;
	/**
	 * 题目排序
	 */
	@ApiModelProperty("题目排序")
	private Long orderNum;
	/**
	 * 学科网xkw，好未来hwl等
	 */
	@ApiModelProperty("学科网xkw，好未来hwl等")
	private String thirdSourceType;
	/**
	 * 添加外部id
	 */
	@ApiModelProperty("添加外部id")
	private String thirdOutId;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;


	/**
	 * 下一个 题目 OID
	 */
	@ApiModelProperty("下一个 题目 OID")
	private String nextUserPaperQuestionOid;

}
