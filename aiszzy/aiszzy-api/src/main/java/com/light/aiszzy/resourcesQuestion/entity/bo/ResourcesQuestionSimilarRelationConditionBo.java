package com.light.aiszzy.resourcesQuestion.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 资源库题目相似题
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class ResourcesQuestionSimilarRelationConditionBo extends PageLimitBo{

	/**
	 * 主键id
	 */
	@ApiModelProperty("主键id")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * resource_question的oid
	 * resource_question的oid,原题oid
	 */
	@ApiModelProperty("resource_question的oid,原题oid")
	private String originalResourcesQuestionOid;

	/**
	 * 来源(学科网，好未来等)
	 * resource_question的oid,相似题oid
	 */
	@ApiModelProperty("resource_question的oid,相似题oid")
	private String similarResourcesQuestionOid;


	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
