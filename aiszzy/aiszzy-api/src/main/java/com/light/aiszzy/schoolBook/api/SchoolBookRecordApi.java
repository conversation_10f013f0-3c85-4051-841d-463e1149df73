package com.light.aiszzy.schoolBook.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookRecordVo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教辅或作业本开通记录详情表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface SchoolBookRecordApi  {

	/**
	 * 查询教辅或作业本开通记录详情表列表
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@PostMapping("/schoolBookRecord/pageList")
	@ApiOperation(value = "分页查询教辅或作业本开通记录详情表",httpMethod = "POST")
	AjaxResult<PageInfo<SchoolBookRecordVo>> getSchoolBookRecordPageListByCondition(@RequestBody SchoolBookRecordConditionBo condition);

	/**
	 * 查询所有教辅或作业本开通记录详情表列表
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@PostMapping("/schoolBookRecord/list")
	@ApiOperation(value = "查询所有教辅或作业本开通记录详情表",httpMethod = "POST")
	AjaxResult<List<SchoolBookRecordVo>> getSchoolBookRecordListByCondition(@RequestBody SchoolBookRecordConditionBo condition);


	/**
	 * 新增教辅或作业本开通记录详情表
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@PostMapping("/schoolBookRecord/add")
	@ApiOperation(value = "新增教辅或作业本开通记录详情表",httpMethod = "POST")
	AjaxResult addSchoolBookRecord(@Validated @RequestBody SchoolBookRecordBo schoolBookRecordBo);

	/**
	 * 修改教辅或作业本开通记录详情表
	 * @param schoolBookRecordBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@PostMapping("/schoolBookRecord/update")
	@ApiOperation(value = "修改教辅或作业本开通记录详情表",httpMethod = "POST")
	AjaxResult updateSchoolBookRecord(@Validated @RequestBody SchoolBookRecordBo schoolBookRecordBo);

	/**
	 * 查询教辅或作业本开通记录详情表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@GetMapping("/schoolBookRecord/detail")
	@ApiOperation(value = "查询教辅或作业本开通记录详情表详情",httpMethod = "GET")
	AjaxResult<SchoolBookRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除教辅或作业本开通记录详情表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@GetMapping("/schoolBookRecord/delete")
	@ApiOperation(value = "删除教辅或作业本开通记录详情表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

