package com.light.aiszzy.schoolBook.entity.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 批量开通教辅或作业本业务对象
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
public class BatchSchoolBookBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学校信息列表
     */
    @ApiModelProperty(value = "学校信息列表", required = true)
    @NotEmpty(message = "学校信息列表不能为空")
    @Valid
    private List<SchoolInfoBo> schoolInfoList;

    /**
     * 教辅信息列表
     */
    @ApiModelProperty(value = "教辅信息列表", required = true)
    @NotEmpty(message = "教辅信息列表不能为空")
    @Valid
    private List<BookInfoBo> bookInfoList;

    /**
     * 启用状态 1启用 2停用
     */
    @ApiModelProperty(value = "启用状态 1启用 2停用", required = true)
    @NotNull(message = "启用状态不能为空")
    private Integer status;

    /**
     * 开通开始日期
     */
    @ApiModelProperty(value = "开通开始日期", required = true)
    @NotNull(message = "开通开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 开通结束日期
     */
    @ApiModelProperty(value = "开通结束日期", required = true)
    @NotNull(message = "开通结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;
}
