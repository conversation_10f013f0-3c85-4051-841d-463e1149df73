package com.light.aiszzy.xkw.xkwTextbookCatalog.entity.vo;

import com.light.utils.TreeBean;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 教材目录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwTextbookCatalogVo implements Serializable, TreeBean<XkwTextbookCatalogVo> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 教材ID
     */
    private Long textbookId;

    /**
     * 排序值
     */
    private Long ordinal;

    /**
     * 父节点ID
     */
    private Long parentId;

    /**
     * 节点类型，分为实节点和虚节点（真实教材目录不存在的节点，例如：单元综合与测试）,可用值:VIRTUAL,REA
     */
    private String type;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    private List<XkwTextbookCatalogVo> children;

    @Override
    public String getTreeKey() {
        return id.toString();
    }

    @Override
    public String getParentTreeKey() {
        return parentId.toString();
    }

    @Override
    public List<XkwTextbookCatalogVo> getChildren() {
        return this.children;
    }

    @Override
    public void setChildren(List<XkwTextbookCatalogVo> children) {
        this.children = children;
    }
}
