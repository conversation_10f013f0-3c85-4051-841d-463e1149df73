package com.light.aiszzy.apiRequestLog.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 第三方请求日志表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class ApiRequestLogVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每一条目录记录
     */
    private Long id;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求方式 POST GET PUT
     */
    private String method;

    /**
     * 入参
     */
    private String params;

    /**
     * 响应
     */
    private String response;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
