package com.light.aiszzy.schoolBook.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 教辅或作业本开通记录详情表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@Data
public class SchoolBookRecordConditionBo extends PageLimitBo{

	/**
	 * 自增主键，唯一标识每一条开通记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条开通记录")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 关联开通记录oid
	 */
	@ApiModelProperty("关联开通记录oid")
	private String schoolBookOid;

	/**
	 * 学校CODE
	 */
	@ApiModelProperty("学校CODE")
	private String orgCode;

	/**
	 * 学校名称
	 */
	@ApiModelProperty("学校名称")
	private String orgName;

	/**
	 * 学校区域
	 */
	@ApiModelProperty("学校区域")
	private String orgAreaName;

	/**
	 * 学校区域CODE
	 */
	@ApiModelProperty("学校区域CODE")
	private String orgAreaCode;

	/**
	 * 学校市区
	 */
	@ApiModelProperty("学校市区")
	private String orgCityName;

	/**
	 * 学校市区CODE
	 */
	@ApiModelProperty("学校市区CODE")
	private String orgCityCode;

	/**
	 * 学校省
	 */
	@ApiModelProperty("学校省")
	private String orgProvinceName;

	/**
	 * 学校省CODE
	 */
	@ApiModelProperty("学校省CODE")
	private String orgProvinceCode;

	/**
	 * 教辅OID或作业本OID
	 */
	@ApiModelProperty("教辅OID或作业本OID")
	private String bookOid;

	/**
	 * 启用 1启用 2停用
	 */
	@ApiModelProperty("启用 1启用 2停用")
	private Integer status;

	/**
	 * 开通开始日期
	 */
	@ApiModelProperty("开通开始日期")
	private Date startDate;

	/**
	 * 开通结束日期
	 */
	@ApiModelProperty("开通结束日期")
	private Date endDate;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
