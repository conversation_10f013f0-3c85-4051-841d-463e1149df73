package com.light.aiszzy.practiceBook.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.practiceBook.api.PracticeBookPageApi;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 教辅目录每页图片记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "practiceBookPageApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = PracticeBookPageApiService.PracticeBookPageApiFallbackFactory.class)
@Component
public interface PracticeBookPageApiService  extends PracticeBookPageApi {

	@Component
	class PracticeBookPageApiFallbackFactory implements FallbackFactory<PracticeBookPageApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(PracticeBookPageApiService.PracticeBookPageApiFallbackFactory.class);
		@Override
		public PracticeBookPageApiService create(Throwable cause) {
			PracticeBookPageApiService.PracticeBookPageApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new PracticeBookPageApiService() {
				public AjaxResult getPracticeBookPagePageListByCondition(PracticeBookPageConditionBo condition){
					return AjaxResult.fail("教辅目录每页图片记录表查询失败");
				}
				public AjaxResult getPracticeBookPageListByCondition(PracticeBookPageConditionBo condition){
					return AjaxResult.fail("教辅目录每页图片记录表查询失败");
				}

				public AjaxResult addPracticeBookPage(PracticeBookPageBo practiceBookPageBo){
					return AjaxResult.fail("教辅目录每页图片记录表新增失败");
				}

				public AjaxResult updatePracticeBookPage(PracticeBookPageBo practiceBookPageBo){
					return AjaxResult.fail("教辅目录每页图片记录表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("教辅目录每页图片记录表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("教辅目录每页图片记录表删除失败");
				}

				@Override
				public AjaxResult saveBatchByPracticeBookOid(String practiceBookOid, List<PracticeBookPageBo> list) {
					return AjaxResult.fail("批量保存教辅图片数据");
				}

				@Override
				public AjaxResult<Void> deleteByPraticeBookOid(String practiceBookOid) {
					return AjaxResult.fail("删除失败");
				}
			};
		}
	}
}