package com.light.aiszzy.xkw.xkwTextbook.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwTextbook.entity.vo.XkwTextbookVo;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教材接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface XkwTextbookApi  {

	/**
	 * 查询教材列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwTextbook/pageList")
	@ApiOperation(value = "分页查询教材",httpMethod = "POST")
	AjaxResult<PageInfo<XkwTextbookVo>> getXkwTextbookPageListByCondition(@RequestBody XkwTextbookConditionBo condition);

	/**
	 * 查询所有教材列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwTextbook/list")
	@ApiOperation(value = "查询所有教材",httpMethod = "POST")
	AjaxResult<List<XkwTextbookVo>> getXkwTextbookListByCondition(@RequestBody XkwTextbookConditionBo condition);






}

