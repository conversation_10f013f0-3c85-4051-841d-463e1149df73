package com.light.aiszzy.schoolBook.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 学校信息业务对象
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
public class SchoolInfoBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学校CODE
     */
    @ApiModelProperty(value = "学校CODE", required = true)
    @NotBlank(message = "学校CODE不能为空")
    private String orgCode;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称", required = true)
    @NotBlank(message = "学校名称不能为空")
    private String orgName;

    /**
     * 学校区域
     */
    @ApiModelProperty("学校区域")
    private String orgAreaName;

    /**
     * 学校区域CODE
     */
    @ApiModelProperty("学校区域CODE")
    private String orgAreaCode;

    /**
     * 学校市区
     */
    @ApiModelProperty("学校市区")
    private String orgCityName;

    /**
     * 学校市区CODE
     */
    @ApiModelProperty("学校市区CODE")
    private String orgCityCode;

    /**
     * 学校省
     */
    @ApiModelProperty("学校省")
    private String orgProvinceName;

    /**
     * 学校省CODE
     */
    @ApiModelProperty("学校省CODE")
    private String orgProvinceCode;
}
