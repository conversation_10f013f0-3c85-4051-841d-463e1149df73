package com.light.aiszzy.userPaper.api;

import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 用户上传每页图片表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface UserPaperPageApi  {

	/**
	 * 查询用户上传每页图片表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/userPaperPage/pageList")
	@ApiOperation(value = "分页查询用户上传每页图片表",httpMethod = "POST")
	AjaxResult<PageInfo<UserPaperPageVo>> getUserPaperPagePageListByCondition(@RequestBody UserPaperPageConditionBo condition);

	/**
	 * 查询所有用户上传每页图片表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/userPaperPage/list")
	@ApiOperation(value = "查询所有用户上传每页图片表",httpMethod = "POST")
	AjaxResult<List<UserPaperPageVo>> getUserPaperPageListByCondition(@RequestBody UserPaperPageConditionBo condition);


	/**
	 * 新增用户上传每页图片表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/userPaperPage/add")
	@ApiOperation(value = "新增用户上传每页图片表",httpMethod = "POST")
	AjaxResult addUserPaperPage(@Validated @RequestBody UserPaperPageBo userPaperPageBo);

	/**
	 * 修改用户上传每页图片表
	 * @param userPaperPageBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/userPaperPage/update")
	@ApiOperation(value = "修改用户上传每页图片表",httpMethod = "POST")
	AjaxResult updateUserPaperPage(@Validated @RequestBody UserPaperPageBo userPaperPageBo);

	/**
	 * 查询用户上传每页图片表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/userPaperPage/detail")
	@ApiOperation(value = "查询用户上传每页图片表详情",httpMethod = "GET")
	AjaxResult<UserPaperPageVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除用户上传每页图片表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/userPaperPage/delete")
	@ApiOperation(value = "删除用户上传每页图片表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	/**
	 * 根据校本 删除数据
	 * @param userPaperOid the user paper oid 校本 OID
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/userPaperPage/deleteByUserPaperOid/{userPaperOid}")
    AjaxResult deleteByUserPaperOid(@PathVariable("userPaperOid") String userPaperOid);


	/**
	 * 批量保存 校本页面信息
	 * @param userPaperOid the user paper oid 校本 OID
	 * @param list the list
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/userPaperPage/saveBatchByUserPaperOid/{userPaperOid}")
	AjaxResult saveBatchByUserPaperOid(@PathVariable("userPaperOid") String userPaperOid, @RequestBody List<UserPaperPageBo> list);


	@GetMapping("/userPaperPage/queryByUserPaperOid/{userPaperOid}")
	AjaxResult<List<UserPaperPageVo>> queryByUserPaperOid(@PathVariable("userPaperOid") String userPaperOid);
}

