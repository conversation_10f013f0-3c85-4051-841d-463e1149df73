package com.light.aiszzy.resultUploadFile.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 扫描上传图片
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
@Data
public class ResultUploadFileVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每一条目录记录
     */
    private Long id;

    /**
     * 通过设备信息获取的学校CODE
     */
    private String orgCode;

    /**
     * 存放地址1
     */
    private String pathOne;

    /**
     * 存放地址2
     */
    private String pathTwo;

    /**
     * 设备号，硬件序列号
     */
    private String hardwareCode;

    /**
     * 是否识别处理 0未处理，1已经处理，2处理失败
     */
    private Integer isDeal;

    /**
     * 处理情况描述
     */
    private String dealDetail;

    /**
     * 处理结果，JSON存储处理过程中重要的信息
     */
    private String dealResult;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
