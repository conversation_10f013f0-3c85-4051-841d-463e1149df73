package com.light.aiszzy.xkw.xkwSubject.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwSubject.entity.vo.XkwSubjectVo;
import com.light.aiszzy.xkw.xkwSubject.entity.bo.XkwSubjectBo;
import com.light.aiszzy.xkw.xkwSubject.entity.bo.XkwSubjectConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 学科接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface XkwSubjectApi  {

	/**
	 * 查询学科列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwSubject/pageList")
	@ApiOperation(value = "分页查询学科",httpMethod = "POST")
	AjaxResult<PageInfo<XkwSubjectVo>> getXkwSubjectPageListByCondition(@RequestBody XkwSubjectConditionBo condition);

	/**
	 * 查询所有学科列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwSubject/list")
	@ApiOperation(value = "查询所有学科",httpMethod = "POST")
	AjaxResult<List<XkwSubjectVo>> getXkwSubjectListByCondition(@RequestBody XkwSubjectConditionBo condition);


	/**
	 * 新增学科
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwSubject/add")
	@ApiOperation(value = "新增学科",httpMethod = "POST")
	AjaxResult addXkwSubject(@Validated @RequestBody XkwSubjectBo xkwSubjectBo);

	/**
	 * 修改学科
	 * @param xkwSubjectBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwSubject/update")
	@ApiOperation(value = "修改学科",httpMethod = "POST")
	AjaxResult updateXkwSubject(@Validated @RequestBody XkwSubjectBo xkwSubjectBo);

	/**
	 * 查询学科详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/xkwSubject/detail")
	@ApiOperation(value = "查询学科详情",httpMethod = "GET")
	AjaxResult<XkwSubjectVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除学科
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/xkwSubject/delete")
	@ApiOperation(value = "删除学科",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

