package com.light.aiszzy.userPaper.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 资源库试卷表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface UserPaperQuestionApi  {

	/**
	 * 查询资源库试卷表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/userPaperQuestion/pageList")
	@ApiOperation(value = "分页查询资源库试卷表",httpMethod = "POST")
	AjaxResult<PageInfo<UserPaperQuestionVo>> getUserPaperQuestionPageListByCondition(@RequestBody UserPaperQuestionConditionBo condition);

	/**
	 * 查询所有资源库试卷表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/userPaperQuestion/list")
	@ApiOperation(value = "查询所有资源库试卷表",httpMethod = "POST")
	AjaxResult<List<UserPaperQuestionVo>> getUserPaperQuestionListByCondition(@RequestBody UserPaperQuestionConditionBo condition);

	/**
	 *  根据校本 OID 查询题目数据
	 * @param userPaperOid 校本 OID
	 * @return {@link AjaxResult }<{@link List }<{@link UserPaperQuestionVo }>>
	 */
	@GetMapping("/userPaperQuestion/queryByUserPaperOid")
	AjaxResult<List<UserPaperQuestionVo>> queryByUserPaperOid(@RequestParam("userPaperOid") String userPaperOid);

	/**
	 * 新增资源库试卷表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/userPaperQuestion/add")
	@ApiOperation(value = "新增资源库试卷表",httpMethod = "POST")
	AjaxResult addUserPaperQuestion(@Validated @RequestBody UserPaperQuestionBo userPaperQuestionBo);

	/**
	 * 修改资源库试卷表
	 * @param userPaperQuestionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/userPaperQuestion/update")
	@ApiOperation(value = "修改资源库试卷表",httpMethod = "POST")
	AjaxResult updateUserPaperQuestion(@Validated @RequestBody UserPaperQuestionBo userPaperQuestionBo);

	/**
	 * 查询资源库试卷表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/userPaperQuestion/detail")
	@ApiOperation(value = "查询资源库试卷表详情",httpMethod = "GET")
	AjaxResult<UserPaperQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除资源库试卷表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/userPaperQuestion/delete")
	@ApiOperation(value = "删除资源库试卷表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);


	/**
	 * 根据 OID 和
	 * @param bo
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/userPaperQuestion/getOrInitQuestionInfoByOid")
    AjaxResult<UserPaperQuestionVo> getOrInitQuestionInfoByOid(@RequestBody UserPaperQuestionBo bo);

	@PostMapping("/userPaperQuestion/addQuestionByPosition")
	AjaxResult addQuestionByPosition(@RequestBody UserPaperQuestionBo bo);

	/**
	 *  取消题目
	 * @param bo the user paper question 校本题目
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/userPaperQuestion/cancelUserQuestion")
	AjaxResult cancelUserQuestion(@RequestBody UserPaperQuestionBo bo);

	/**
	 * 标注校本题目
	 * @param oid 校本题目oid
	 * @return {@link AjaxResult}
	 */
	@PostMapping("/userPaperQuestion/markUserQuestion")
	@ApiOperation(value = "标注校本题目",httpMethod = "POST")
	AjaxResult markUserQuestion(@RequestParam("oid") String oid);


	/**
	 *  根据校本页码数据获取 题目列表
	 * @param userPaperPageOid the user paper page oid
	 * @return {@link AjaxResult }
	 */
	@GetMapping("/userPaperQuestion/queryByUserPaperPageOid")
	AjaxResult queryByUserPaperPageOid(@RequestParam("userPaperPageOid") String userPaperPageOid);

	@PostMapping("/userPaperQuestion/reFetchQuestionInfoByOid")
	AjaxResult reFetchQuestionInfoByOid(@RequestBody UserPaperQuestionBo bo);
}

