package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 校本作业题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkQuestionApi  {

	/**
	 * 查询校本作业题目信息列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkQuestion/pageList")
	@ApiOperation(value = "分页查询校本作业题目信息",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkQuestionVo>> getHomeworkQuestionPageListByCondition(@RequestBody HomeworkQuestionConditionBo condition);

	/**
	 * 查询所有校本作业题目信息列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkQuestion/list")
	@ApiOperation(value = "查询所有校本作业题目信息",httpMethod = "POST")
	AjaxResult<List<HomeworkQuestionVo>> getHomeworkQuestionListByCondition(@RequestBody HomeworkQuestionConditionBo condition);


	/**
	 * 新增校本作业题目信息
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkQuestion/add")
	@ApiOperation(value = "新增校本作业题目信息",httpMethod = "POST")
	AjaxResult addHomeworkQuestion(@Validated @RequestBody HomeworkQuestionBo homeworkQuestionBo);

	/**
	 * 修改校本作业题目信息
	 * @param homeworkQuestionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkQuestion/update")
	@ApiOperation(value = "修改校本作业题目信息",httpMethod = "POST")
	AjaxResult updateHomeworkQuestion(@Validated @RequestBody HomeworkQuestionBo homeworkQuestionBo);

	/**
	 * 查询校本作业题目信息详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkQuestion/detail")
	@ApiOperation(value = "查询校本作业题目信息详情",httpMethod = "GET")
	AjaxResult<HomeworkQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除校本作业题目信息
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkQuestion/delete")
	@ApiOperation(value = "删除校本作业题目信息",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	/**
	 * 作业换题
	 *
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/homeworkQuestion/changeQuestion")
	@ApiOperation(value = "作业换题", httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult changeQuestion(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid, @RequestParam("oldQuestionOid") String oldQuestionOid, @RequestParam("newQuestionOid") String newQuestionOid);


	/**
	 * 删除作业题目
	 *
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/homeworkQuestion/deleteQuestion")
	@ApiOperation(value = "删除作业题目", httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult deleteQuestion(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid, @RequestParam("questionOid") String questionOid);



}

