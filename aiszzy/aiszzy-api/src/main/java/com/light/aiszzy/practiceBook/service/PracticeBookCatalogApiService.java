package com.light.aiszzy.practiceBook.service;


import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogExcel;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.practiceBook.api.PracticeBookCatalogApi;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 教辅目录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "practiceBookCatalogApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = PracticeBookCatalogApiService.PracticeBookCatalogApiFallbackFactory.class)
@Component
public interface PracticeBookCatalogApiService  extends PracticeBookCatalogApi {

	@Component
	class PracticeBookCatalogApiFallbackFactory implements FallbackFactory<PracticeBookCatalogApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(PracticeBookCatalogApiService.PracticeBookCatalogApiFallbackFactory.class);
		@Override
		public PracticeBookCatalogApiService create(Throwable cause) {
			PracticeBookCatalogApiService.PracticeBookCatalogApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new PracticeBookCatalogApiService() {
				public AjaxResult getPracticeBookCatalogPageListByCondition(PracticeBookCatalogConditionBo condition){
					return AjaxResult.fail("教辅目录表查询失败");
				}
				public AjaxResult getPracticeBookCatalogListByCondition(PracticeBookCatalogConditionBo condition){
					return AjaxResult.fail("教辅目录表查询失败");
				}

				public AjaxResult addPracticeBookCatalog(PracticeBookCatalogBo practiceBookCatalogBo){
					return AjaxResult.fail("教辅目录表新增失败");
				}

				public AjaxResult updatePracticeBookCatalog(PracticeBookCatalogBo practiceBookCatalogBo){
					return AjaxResult.fail("教辅目录表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("教辅目录表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("教辅目录表删除失败");
				}

				@Override
				public AjaxResult<Void> saveExcelDataByPracticeBookOid(String practiceBookOid, List<PracticeBookCatalogExcel> catalogExcels) {
					return AjaxResult.fail("教辅目录导入失败");
				}

				@Override
				public AjaxResult<Void> resetOrderNumByParentOid(PracticeBookCatalogBo practiceBookCatalogBo) {
					return AjaxResult.fail("教辅目录排序失败");
				}

				@Override
				public AjaxResult<List<PracticeBookCatalogVo>> queryTreeByPracticeBookOid(String practiceBookOid) {
					return AjaxResult.fail("教辅目录获取失败");
				}
			};
		}
	}
}