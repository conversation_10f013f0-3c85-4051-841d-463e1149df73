package com.light.aiszzy.homework.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 作业本目录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class HomeworkBookCatalogVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 作业本id
     */
    private String homeworkBookOid;

    /**
     * 父节点
     */
    private String parentOid;

    /**
     * 目录名称
     */
    private String name;

    /**
     * 排序
     */
    private Long orderNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 子目录个数
     */
    private Integer hasChildCount;

    private List<HomeworkVo> homeworkList;
}
