package com.light.aiszzy.practiceBook.entity.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.google.common.base.Strings;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 教辅开通列表导出对象
 */
@Data
@ApiModel("教辅开通列表导出对象")
public class PracticeBookExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "教辅ID", width = 15)
    @ApiModelProperty("教辅ID")
    private String id;

    @Excel(name = "封面", width = 20)
    @ApiModelProperty("封面")
    private String coverImage;

    @Excel(name = "教辅名称", width = 25)
    @ApiModelProperty("教辅名称")
    private String name;

    @Excel(name = "出版社", width = 20)
    @ApiModelProperty("出版社")
    private String publisher;

    @Excel(name = "学科", width = 15)
    @ApiModelProperty("学科")
    private String subject;

    @Excel(name = "年级", width = 15)
    @ApiModelProperty("年级")
    private String grade;

    @Excel(name = "来源", width = 15)
    @ApiModelProperty("来源")
    private String sourceTypeName;

    @Excel(name = "开通学校", width = 18)
    @ApiModelProperty("开通学校")
    private Integer totalSchoolCount;

    @Excel(name = "使用中学校", width = 20)
    @ApiModelProperty("使用中学校")
    private Integer activeSchoolCount;

    /**
     * 从 PracticeBookWithSchoolCountVo 转换为导出对象
     */
    public static PracticeBookExportVo fromPracticeBookWithSchoolCountVo(PracticeBookWithSchoolCountVo vo) {
        if (vo == null) {
            return null;
        }

        PracticeBookExportVo exportVo = new PracticeBookExportVo();
        exportVo.setId(Objects.toString(vo.getId(), ""));
        exportVo.setCoverImage(Strings.nullToEmpty(vo.getCoverImage()));
        exportVo.setName(Strings.nullToEmpty(vo.getName()));
        exportVo.setPublisher(Strings.nullToEmpty(vo.getPublisher()));
        exportVo.setSubject(Objects.toString(vo.getSubject(), ""));
        exportVo.setGrade(Objects.toString(vo.getGrade(), ""));
        exportVo.setSourceTypeName("");
        exportVo.setTotalSchoolCount(vo.getTotalSchoolCount());
        exportVo.setActiveSchoolCount(vo.getActiveSchoolCount());

        return exportVo;
    }
}