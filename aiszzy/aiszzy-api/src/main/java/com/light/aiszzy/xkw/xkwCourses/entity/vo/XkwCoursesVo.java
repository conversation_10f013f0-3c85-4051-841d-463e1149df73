package com.light.aiszzy.xkw.xkwCourses.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 课程
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class XkwCoursesVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 课程名称
     */
    private String name;

    /**
     * 学科ID
     */
    private Integer subjectId;

    /**
     * 学段ID
     */
    private Long stageId;

    /**
     * 排序值
     */
    private Long ordinal;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
