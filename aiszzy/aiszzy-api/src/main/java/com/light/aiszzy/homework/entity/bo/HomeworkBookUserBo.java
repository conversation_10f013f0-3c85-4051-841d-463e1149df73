package com.light.aiszzy.homework.entity.bo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 老师使用作业本
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-18 14:20:30
 */
@Data
public class HomeworkBookUserBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 老师使用作业本id
	 */
	@ApiModelProperty("老师使用作业本id")
	private Long homeworkBookUserId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 作业本oid
	 */
	@ApiModelProperty("作业本oid")
	private String homeworkBookOid;
	private String homeworkBookOids;

	private String orgCode;
	/**
	 * 是否是最后一条记录，1是，2不是
	 */
	@ApiModelProperty("是否是最后一条记录，1是，2不是")
	private Long isCurrent;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@ApiModelProperty("是否删除（1：正常 2：删除）")
	private Integer isDelete;

}
