package  com.light.aiszzy.schoolResourcesQuestion.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 资源库题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface SchoolResourcesQuestionApi  {

	/**
	 * 查询资源库题目表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/schoolResourcesQuestion/pageList")
	@ApiOperation(value = "分页查询资源库题目表",httpMethod = "POST")
	AjaxResult<PageInfo<SchoolResourcesQuestionVo>> getSchoolResourcesQuestionPageListByCondition(@RequestBody SchoolResourcesQuestionConditionBo condition);

	/**
	 * 查询所有资源库题目表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/schoolResourcesQuestion/list")
	@ApiOperation(value = "查询所有资源库题目表",httpMethod = "POST")
	AjaxResult<List<SchoolResourcesQuestionVo>> getSchoolResourcesQuestionListByCondition(@RequestBody SchoolResourcesQuestionConditionBo condition);


	/**
	 * 新增资源库题目表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/schoolResourcesQuestion/add")
	@ApiOperation(value = "新增资源库题目表",httpMethod = "POST")
	AjaxResult addSchoolResourcesQuestion(@Validated @RequestBody SchoolResourcesQuestionBo schoolResourcesQuestionBo);

	/**
	 * 修改资源库题目表
	 * @param schoolResourcesQuestionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/schoolResourcesQuestion/update")
	@ApiOperation(value = "修改资源库题目表",httpMethod = "POST")
	AjaxResult updateSchoolResourcesQuestion(@Validated @RequestBody SchoolResourcesQuestionBo schoolResourcesQuestionBo);

	/**
	 * 查询资源库题目表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/schoolResourcesQuestion/detail")
	@ApiOperation(value = "查询资源库题目表详情",httpMethod = "GET")
	AjaxResult<SchoolResourcesQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除资源库题目表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/schoolResourcesQuestion/delete")
	@ApiOperation(value = "删除资源库题目表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

