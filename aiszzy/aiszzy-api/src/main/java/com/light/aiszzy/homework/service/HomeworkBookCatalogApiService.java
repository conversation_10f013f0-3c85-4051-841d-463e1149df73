package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkBookCatalogApi;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业本目录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "homeworkBookCatalogApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkBookCatalogApiService.HomeworkBookCatalogApiFallbackFactory.class)
@Component
public interface HomeworkBookCatalogApiService  extends HomeworkBookCatalogApi {

	@Component
	class HomeworkBookCatalogApiFallbackFactory implements FallbackFactory<HomeworkBookCatalogApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkBookCatalogApiService.HomeworkBookCatalogApiFallbackFactory.class);
		@Override
		public HomeworkBookCatalogApiService create(Throwable cause) {
			HomeworkBookCatalogApiService.HomeworkBookCatalogApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkBookCatalogApiService() {
				public AjaxResult getHomeworkBookCatalogPageListByCondition(HomeworkBookCatalogConditionBo condition){
					return AjaxResult.fail("作业本目录查询失败");
				}
				public AjaxResult getHomeworkBookCatalogListByCondition(HomeworkBookCatalogConditionBo condition){
					return AjaxResult.fail("作业本目录查询失败");
				}

				@Override
				public AjaxResult listAllChild(HomeworkBookCatalogConditionBo condition) {
					return AjaxResult.fail("查询作业本目录子目录和作业");
				}

				@Override
				public AjaxResult listAllChildByBook(HomeworkBookCatalogConditionBo condition) {
					return AjaxResult.fail("根据作业本查所有目录和作业失败");

				}

				@Override
				public AjaxResult sortHomeworkBookCatalog(HomeworkBookCatalogBo condition) {
					return AjaxResult.fail("排序目录失败");
				}

				public AjaxResult addHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo){
					return AjaxResult.fail("作业本目录新增失败");
				}

				public AjaxResult updateHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo){
					return AjaxResult.fail("作业本目录更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业本目录获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业本目录删除失败");
				}

				@Override
				public AjaxResult deleteByBookOid(String oid) {
					return AjaxResult.fail("作业本目录删除失败");
				}
			};
		}
	}
}