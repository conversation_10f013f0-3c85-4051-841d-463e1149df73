package com.light.aiszzy.homework.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 作业每页题目坐标信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@Data
public class HomeworkPageConditionBo extends PageLimitBo{

	/**
	 * 题目id
	 */
	@ApiModelProperty("题目id")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@ApiModelProperty("作业id")
	private String homeworkOid;

	/**
	 * 二维码使用，8位，字母加数字，尝试10次，重复返回报错
	 */
	@ApiModelProperty("二维码使用，8位，字母加数字，尝试10次，重复返回报错")
	private String homeworkCode;

	/**
	 * 页码
	 */
	@ApiModelProperty("页码")
	private Long pageNoSearch;

	/**
	 * 题目信息，题号，坐标等
	 */
	@ApiModelProperty("题目信息，题号，坐标等")
	private String questionJson;

	/**
	 * 单页图片地址
	 */
	@ApiModelProperty("单页图片地址")
	private String pageUrl;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
