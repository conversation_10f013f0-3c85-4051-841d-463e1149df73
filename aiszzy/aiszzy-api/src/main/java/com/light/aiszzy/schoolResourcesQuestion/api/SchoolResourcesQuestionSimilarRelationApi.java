package com.light.aiszzy.schoolResourcesQuestion.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionSimilarRelationVo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 资源库题目相似题接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface SchoolResourcesQuestionSimilarRelationApi  {

	/**
	 * 查询资源库题目相似题列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/schoolResourcesQuestionSimilarRelation/pageList")
	@ApiOperation(value = "分页查询资源库题目相似题",httpMethod = "POST")
	AjaxResult<PageInfo<SchoolResourcesQuestionSimilarRelationVo>> getSchoolResourcesQuestionSimilarRelationPageListByCondition(@RequestBody SchoolResourcesQuestionSimilarRelationConditionBo condition);

	/**
	 * 查询所有资源库题目相似题列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/schoolResourcesQuestionSimilarRelation/list")
	@ApiOperation(value = "查询所有资源库题目相似题",httpMethod = "POST")
	AjaxResult<List<SchoolResourcesQuestionSimilarRelationVo>> getSchoolResourcesQuestionSimilarRelationListByCondition(@RequestBody SchoolResourcesQuestionSimilarRelationConditionBo condition);


	/**
	 * 新增资源库题目相似题
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/schoolResourcesQuestionSimilarRelation/add")
	@ApiOperation(value = "新增资源库题目相似题",httpMethod = "POST")
	AjaxResult addSchoolResourcesQuestionSimilarRelation(@Validated @RequestBody SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo);

	/**
	 * 修改资源库题目相似题
	 * @param schoolResourcesQuestionSimilarRelationBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/schoolResourcesQuestionSimilarRelation/update")
	@ApiOperation(value = "修改资源库题目相似题",httpMethod = "POST")
	AjaxResult updateSchoolResourcesQuestionSimilarRelation(@Validated @RequestBody SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo);

	/**
	 * 查询资源库题目相似题详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/schoolResourcesQuestionSimilarRelation/detail")
	@ApiOperation(value = "查询资源库题目相似题详情",httpMethod = "GET")
	AjaxResult<SchoolResourcesQuestionSimilarRelationVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除资源库题目相似题
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/schoolResourcesQuestionSimilarRelation/delete")
	@ApiOperation(value = "删除资源库题目相似题",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

