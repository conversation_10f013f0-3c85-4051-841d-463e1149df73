package com.light.aiszzy.userPaper.service;


import com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.userPaper.api.UserPaperQuestionApi;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 资源库试卷表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@FeignClient(contextId = "userPaperQuestionApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = UserPaperQuestionApiService.UserPaperQuestionApiFallbackFactory.class)
@Component
public interface UserPaperQuestionApiService  extends UserPaperQuestionApi {

	@Component
	class UserPaperQuestionApiFallbackFactory implements FallbackFactory<UserPaperQuestionApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(UserPaperQuestionApiService.UserPaperQuestionApiFallbackFactory.class);
		@Override
		public UserPaperQuestionApiService create(Throwable cause) {
			UserPaperQuestionApiService.UserPaperQuestionApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new UserPaperQuestionApiService() {
				public AjaxResult getUserPaperQuestionPageListByCondition(UserPaperQuestionConditionBo condition){
					return AjaxResult.fail("资源库试卷表查询失败");
				}
				public AjaxResult getUserPaperQuestionListByCondition(UserPaperQuestionConditionBo condition){
					return AjaxResult.fail("资源库试卷表查询失败");
				}

				@Override
				public AjaxResult<List<UserPaperQuestionVo>> queryByUserPaperOid(String userPaperOid) {
					return AjaxResult.fail("查询失败");
				}

				public AjaxResult addUserPaperQuestion(UserPaperQuestionBo userPaperQuestionBo){
					return AjaxResult.fail("资源库试卷表新增失败");
				}

				public AjaxResult updateUserPaperQuestion(UserPaperQuestionBo userPaperQuestionBo){
					return AjaxResult.fail("资源库试卷表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("资源库试卷表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("资源库试卷表删除失败");
				}

				@Override
				public AjaxResult<UserPaperQuestionVo> getOrInitQuestionInfoByOid(UserPaperQuestionBo bo) {
					return AjaxResult.fail("数据获取失败");
				}

				@Override
				public AjaxResult addQuestionByPosition(UserPaperQuestionBo bo) {
					return AjaxResult.fail("根据坐标添加题目信息失败");
				}

				@Override
				public AjaxResult cancelUserQuestion(UserPaperQuestionBo bo) {
					return AjaxResult.fail("取消失败");
				}

				@Override
				public AjaxResult markUserQuestion(String oid) {
					return  AjaxResult.fail("标注失败");
				}

				@Override
				public AjaxResult queryByUserPaperPageOid(String userPaperPageOid) {
					return  AjaxResult.fail("获取失败");
				}

				@Override
				public AjaxResult reFetchQuestionInfoByOid(UserPaperQuestionBo bo) {
					return  AjaxResult.fail("获取失败");
				}
			};
		}
	}
}