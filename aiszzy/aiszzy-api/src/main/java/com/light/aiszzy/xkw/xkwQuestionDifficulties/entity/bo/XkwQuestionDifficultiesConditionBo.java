package com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 试题难度等级
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwQuestionDifficultiesConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 难度名称
	 */
	@ApiModelProperty("难度名称")
	private String name;

	/**
	 * 难度档上限值
	 */
	@ApiModelProperty("难度档上限值")
	private String ceiling;

	/**
	 * 难度档下限值
	 */
	@ApiModelProperty("难度档下限值")
	private String flooring;

}
