package com.light.aiszzy.xkw.xkwTextbookVersions.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsConditionBo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教材版本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface XkwTextbookVersionsApi  {

	/**
	 * 查询教材版本列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwTextbookVersions/pageList")
	@ApiOperation(value = "分页查询教材版本",httpMethod = "POST")
	AjaxResult<PageInfo<XkwTextbookVersionsVo>> getXkwTextbookVersionsPageListByCondition(@RequestBody XkwTextbookVersionsConditionBo condition);

	/**
	 * 查询所有教材版本列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwTextbookVersions/list")
	@ApiOperation(value = "查询所有教材版本",httpMethod = "POST")
	AjaxResult<List<XkwTextbookVersionsVo>> getXkwTextbookVersionsListByCondition(@RequestBody XkwTextbookVersionsConditionBo condition);

	@GetMapping("/xkwTextBookVersions/getByStageAndSubject")
	AjaxResult<List<XkwTextbookVersionsVo>> getByStageAndSubject(@RequestParam("stage") Integer stage, @RequestParam("subject") String subject);
}

