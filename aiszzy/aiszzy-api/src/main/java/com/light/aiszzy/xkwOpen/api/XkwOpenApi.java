package com.light.aiszzy.xkwOpen.api;

import com.light.beans.OcrBo;
import com.light.beans.SimilarBo;
import com.light.beans.TikuBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 学科网开放平台接口
 *
 * <AUTHOR>
 * @date 2025/3/28 14:15
 */
public interface XkwOpenApi {

    /**
     * 题库搜索-海量版
     *
     * @param tikuBo 搜索参数
     * @return 搜索结果
     */
    @PostMapping("/xkw-open/tiku-search")
    @ApiOperation(value = "题库搜索-海量版", httpMethod = "POST")
    @ApiImplicitParam(name = "tikuBo", value = "搜索参数", required = true, dataType = "TikuBo")
    AjaxResult tikuSearch(@Validated @RequestBody TikuBo tikuBo);

    /**
     * 相似题推荐
     *
     * @return 推荐结果
     */
    @PostMapping("/xkw-open/similar-recommend")
    @ApiOperation(value = "相似题推荐", httpMethod = "POST")
    AjaxResult similarRecommend( @RequestBody SimilarBo similarBo);


    @PostMapping("/xkw-open/ocr")
    AjaxResult ocr(@RequestBody OcrBo ocrBo);
}
