package com.light.aiszzy.xkw.xkwPaperInfo.api;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.vo.XkwPaperInfoVo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface XkwPaperInfoApi  {

	/**
	 * 查询题目信息列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwPaperInfo/pageList")
	@ApiOperation(value = "分页查询题目信息",httpMethod = "POST")
	AjaxResult<PageInfo<XkwPaperInfoVo>> getXkwPaperInfoPageListByCondition(@RequestBody XkwPaperInfoConditionBo condition);

	/**
	 * 查询所有题目信息列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwPaperInfo/list")
	@ApiOperation(value = "查询所有题目信息",httpMethod = "POST")
	AjaxResult<List<XkwPaperInfoVo>> getXkwPaperInfoListByCondition(@RequestBody XkwPaperInfoConditionBo condition);


	/**
	 * 新增题目信息
	 *
	 * <AUTHOR>
	 * @date 2025-03-20 13:42:45
	 */
	@PostMapping("/xkwPaperInfo/add")
	@ApiOperation(value = "新增题目信息", httpMethod = "POST")
	AjaxResult addXkwPaperInfo(@RequestBody QuestionBo bo);


	/**
	 * 新增题目信息
	 *
	 * <AUTHOR>
	 * @date 2025-03-20 13:42:45
	 */
	@PostMapping("/xkwPaperInfo/edit")
	@ApiOperation(value = "新增题目信息", httpMethod = "POST")
	AjaxResult editXkwPaperInfo(@RequestBody QuestionBo bo);


	/**
	 * 修改题目信息
	 * @param xkwPaperInfoBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwPaperInfo/update")
	@ApiOperation(value = "修改题目信息",httpMethod = "POST")
	AjaxResult updateXkwPaperInfo(@Validated @RequestBody XkwPaperInfoBo xkwPaperInfoBo);

	/**
	 * 查询题目信息详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/xkwPaperInfo/detail")
	@ApiOperation(value = "查询题目信息详情",httpMethod = "GET")
	AjaxResult<XkwPaperInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除题目信息
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/xkwPaperInfo/delete")
	@ApiOperation(value = "删除题目信息",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

