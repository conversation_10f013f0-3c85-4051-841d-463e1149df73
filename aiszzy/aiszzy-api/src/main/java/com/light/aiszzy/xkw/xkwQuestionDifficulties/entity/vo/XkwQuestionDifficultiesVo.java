package com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 试题难度等级
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwQuestionDifficultiesVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 难度名称
     */
    private String name;

    /**
     * 难度档上限值
     */
    private String ceiling;

    /**
     * 难度档下限值
     */
    private String flooring;

}
