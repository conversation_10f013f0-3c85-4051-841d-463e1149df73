package com.light.aiszzy.resourcesQuestion.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.resourcesQuestion.api.ResourcesQuestionApi;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 资源库题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "resourcesQuestionApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = ResourcesQuestionApiService.ResourcesQuestionApiFallbackFactory.class)
@Component
public interface ResourcesQuestionApiService  extends ResourcesQuestionApi {

	@Component
	class ResourcesQuestionApiFallbackFactory implements FallbackFactory<ResourcesQuestionApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(ResourcesQuestionApiService.ResourcesQuestionApiFallbackFactory.class);
		@Override
		public ResourcesQuestionApiService create(Throwable cause) {
			ResourcesQuestionApiService.ResourcesQuestionApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new ResourcesQuestionApiService() {
				public AjaxResult getResourcesQuestionPageListByCondition(ResourcesQuestionConditionBo condition){
					return AjaxResult.fail("资源库题目表查询失败");
				}
				public AjaxResult getResourcesQuestionListByCondition(ResourcesQuestionConditionBo condition){
					return AjaxResult.fail("资源库题目表查询失败");
				}

				public AjaxResult addResourcesQuestion(ResourcesQuestionBo resourcesQuestionBo){
					return AjaxResult.fail("资源库题目表新增失败");
				}

				public AjaxResult updateResourcesQuestion(ResourcesQuestionBo resourcesQuestionBo){
					return AjaxResult.fail("资源库题目表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("资源库题目表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("资源库题目表删除失败");
				}
			};
		}
	}
}