package com.light.aiszzy.device.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 设备表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@Data
public class DeviceBindRecordBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备表id
	 */
	@ApiModelProperty("设备表id")
	private Long deviceBindRecordId;
	
	/**
	 * 主键ID
	 */
	@ApiModelProperty("主键ID")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 设备oid
	 */
	@ApiModelProperty("设备oid")
	private String deviceOid;
	/**
	 * 当前绑定学校
	 */
	@ApiModelProperty("当前绑定学校")
	private String orgCode;
	/**
	 * 绑定学校所在地区
	 */
	@ApiModelProperty("绑定学校所在地区")
	private String orgName;
	/**
	 * 学校区域
	 */
	@ApiModelProperty("学校区域")
	private String orgAreaName;
	/**
	 * 学校区域CODE
	 */
	@ApiModelProperty("学校区域CODE")
	private String orgAreaCode;
	/**
	 * 学校市区
	 */
	@ApiModelProperty("学校市区")
	private String orgCityName;
	/**
	 * 学校市区CODE
	 */
	@ApiModelProperty("学校市区CODE")
	private String orgCityCode;
	/**
	 * 学校省
	 */
	@ApiModelProperty("学校省")
	private String orgProvinceName;
	/**
	 * 学校省CODE
	 */
	@ApiModelProperty("学校省CODE")
	private String orgProvinceCode;
	/**
	 * 设备状态  0禁用 1闲置 2正常
	 */
	@ApiModelProperty("设备状态  0禁用 1闲置 2正常")
	private Integer status;
	/**
	 * 设备MAC地址，设备绑定的时候更新
	 */
	@ApiModelProperty("设备MAC地址")
	private String deviceMacAddress;
	/**
	 * 客户端版本
	 */
	@ApiModelProperty("客户端版本")
	private String clientVersion;
	/**
	 * 扩展信息，备用
	 */
	@ApiModelProperty("扩展信息")
	private String extendInfo;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 创建者真实姓名
	 */
	@ApiModelProperty("创建者真实姓名")
	private String createByRealName;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
