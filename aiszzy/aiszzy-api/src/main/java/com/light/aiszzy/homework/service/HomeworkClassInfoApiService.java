package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkClassInfoApi;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业班级信息，包含疑问项汇总接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@FeignClient(contextId = "homeworkClassInfoApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkClassInfoApiService.HomeworkClassInfoApiFallbackFactory.class)
@Component
public interface HomeworkClassInfoApiService  extends HomeworkClassInfoApi {

	@Component
	class HomeworkClassInfoApiFallbackFactory implements FallbackFactory<HomeworkClassInfoApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkClassInfoApiFallbackFactory.class);
		@Override
		public HomeworkClassInfoApiService create(Throwable cause) {
			HomeworkClassInfoApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkClassInfoApiService() {
				public AjaxResult getHomeworkClassInfoPageListByCondition(HomeworkClassInfoConditionBo condition){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总查询失败");
				}
				public AjaxResult getHomeworkClassInfoListByCondition(HomeworkClassInfoConditionBo condition){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总查询失败");
				}

				public AjaxResult addHomeworkClassInfo(HomeworkClassInfoBo homeworkClassInfoBo){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总新增失败");
				}

				public AjaxResult updateHomeworkClassInfo(HomeworkClassInfoBo homeworkClassInfoBo){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业班级信息，包含疑问项汇总删除失败");
				}
			};
		}
	}
}