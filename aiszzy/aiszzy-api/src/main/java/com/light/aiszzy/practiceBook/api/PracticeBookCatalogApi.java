package com.light.aiszzy.practiceBook.api;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogExcel;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogConditionBo;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教辅目录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface PracticeBookCatalogApi  {

	/**
	 * 查询教辅目录表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookCatalog/pageList")
	@ApiOperation(value = "分页查询教辅目录表",httpMethod = "POST")
	AjaxResult<PageInfo<PracticeBookCatalogVo>> getPracticeBookCatalogPageListByCondition(@RequestBody PracticeBookCatalogConditionBo condition);

	/**
	 * 查询所有教辅目录表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookCatalog/list")
	@ApiOperation(value = "查询所有教辅目录表",httpMethod = "POST")
	AjaxResult<List<PracticeBookCatalogVo>> getPracticeBookCatalogListByCondition(@RequestBody PracticeBookCatalogConditionBo condition);


	/**
	 * 新增教辅目录表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookCatalog/add")
	@ApiOperation(value = "新增教辅目录表",httpMethod = "POST")
	AjaxResult addPracticeBookCatalog(@Validated @RequestBody PracticeBookCatalogBo practiceBookCatalogBo);

	/**
	 * 修改教辅目录表
	 * @param practiceBookCatalogBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookCatalog/update")
	@ApiOperation(value = "修改教辅目录表",httpMethod = "POST")
	AjaxResult updatePracticeBookCatalog(@Validated @RequestBody PracticeBookCatalogBo practiceBookCatalogBo);

	/**
	 * 查询教辅目录表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/practiceBookCatalog/detail")
	@ApiOperation(value = "查询教辅目录表详情",httpMethod = "GET")
	AjaxResult<PracticeBookCatalogVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除教辅目录表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/practiceBookCatalog/delete")
	AjaxResult delete( @RequestParam("oid") String oid);


	/**
	 *  保存 excel 目录数据
	 * @param practiceBookOid the practice book oid 教辅 OID
	 * @param catalogExcels the catalog excel data list 目录 excel 集合
	 * @return {@link AjaxResult }<{@link Void }>
	 */
	@PostMapping("/practiceBookCatalog/saveExcelDataByPracticeBookOid/{practiceBookOid}")
    AjaxResult<Void> saveExcelDataByPracticeBookOid(@PathVariable("practiceBookOid") String practiceBookOid, @RequestBody List<PracticeBookCatalogExcel> catalogExcels);

	/**
	 * 根据父级 OID 重置 教辅目录, 同级别按照集合顺序 重置 order num
	 * @param practiceBookCatalogBo the practice book catalog bo {parentOid 父级 OID,catalogList 目录集合,practiceBookOid 教辅 OID}
	 * @return {@link AjaxResult }<{@link Void }>
	 */
	@PostMapping("/practiceBookCatalog/resetOrderNumByParentOid")
	AjaxResult<Void> resetOrderNumByParentOid(@RequestBody PracticeBookCatalogBo practiceBookCatalogBo);


	/**
	 * 根据教辅 OID 获取教辅目录
	 * @param practiceBookOid the practice book oid
	 * @return {@link AjaxResult }<{@link List }<{@link PracticeBookCatalogVo }>>
	 */
	@GetMapping("/practiceBookCatalog/queryTreeByPracticeBookOid/{practiceBookOid}")
	AjaxResult<List<PracticeBookCatalogVo>> queryTreeByPracticeBookOid(@PathVariable("practiceBookOid") String practiceBookOid);
}

