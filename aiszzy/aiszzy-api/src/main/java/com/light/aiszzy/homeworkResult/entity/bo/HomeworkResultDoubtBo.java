package com.light.aiszzy.homeworkResult.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 扫描结构每页处理结果
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class HomeworkResultDoubtBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 扫描结构每页处理结果id
	 */
	@ApiModelProperty("扫描结构每页处理结果id")
	private Long homeworkResultDoubtId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 作业id
	 */
	@ApiModelProperty("作业id")
	private String homeworkOid;
	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private String orgCode;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;
	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classId;
	/**
	 * 学生oid
	 */
	@ApiModelProperty("学生oid")
	private String stuOid;
	/**
	 * 学生作答存储地址
	 */
	@ApiModelProperty("学生作答存储地址")
	private String stuAnswerUrl;
	/**
	 * 是否有疑问 0：无，1：存在
	 */
	@ApiModelProperty("是否有疑问 0：无，1：存在")
	private Integer isDoubt;
	/**
	 * 1学号没有，2重复学号，3作业页数不全
	 */
	@ApiModelProperty("1学号没有，2重复学号，3作业页数不全")
	private Integer doubtType;
	/**
	 * 学生作答时间
	 */
	@ApiModelProperty("学生作答时间")
	private Long studentAnswerTime;
	/**
	 * 重复id（记录与哪张卷子重复）
	 */
	@ApiModelProperty("重复id（记录与哪张卷子重复）")
	private String repeatId;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
