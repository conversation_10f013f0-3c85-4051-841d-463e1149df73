package com.light.aiszzy.statistics.entity.bo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 作业班级题目正确率
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@Data
public class HomeworkClassQuestionStatisticsBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 作业班级题目正确率id
	 */
	@ApiModelProperty("作业班级题目正确率id")
	private Long homeworkClassQuestionStatisticsId;
	
	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条目录记录")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 学校oid
	 */
	@ApiModelProperty("学校oid")
	private String orgCode;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;
	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private String classId;
	/**
	 * 作业oid
	 */
	@ApiModelProperty("作业oid")
	private String homeworkOid;
	/**
	 * 关联question题目oid
	 */
	@ApiModelProperty("关联question题目oid")
	private String questionOid;
	/**
	 * 题型id
	 */
	@ApiModelProperty("题型id")
	private String questionTypeId;
	/**
	 * 题型名称
	 */
	@ApiModelProperty("题型名称")
	private String questionTypeName;
	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Long subject;
	/**
	 * 知识点,多个逗号分割
	 */
	@ApiModelProperty("知识点,多个逗号分割")
	private String knowledgePointsId;
	/**
	 * 章节ID
	 */
	@ApiModelProperty("章节ID")
	private String chapterId;
	/**
	 * 节ID
	 */
	@ApiModelProperty("节ID")
	private String sectionId;
	/**
	 * 题目排序
	 */
	@ApiModelProperty("题目排序")
	private Long quesOrderNum;
	/**
	 * 班级正确个数
	 */
	@ApiModelProperty("班级正确个数")
	private Long rightNum;
	/**
	 * 班级错误个数
	 */
	@ApiModelProperty("班级错误个数")
	private Long wrongNum;
	/**
	 * 班级提交个数
	 */
	@ApiModelProperty("班级提交个数")
	private Long totalNum;
	/**
	 * 班级作业题目正确率小数*100
	 */
	@ApiModelProperty("班级作业题目正确率小数*100")
	private Long accuracyRate;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
