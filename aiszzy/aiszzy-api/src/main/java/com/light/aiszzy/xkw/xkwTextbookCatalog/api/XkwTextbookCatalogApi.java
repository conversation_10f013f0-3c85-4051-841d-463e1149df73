package com.light.aiszzy.xkw.xkwTextbookCatalog.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.vo.XkwTextbookCatalogVo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogBo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教材目录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface XkwTextbookCatalogApi  {

	/**
	 * 查询教材目录列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwTextbookCatalog/pageList")
	@ApiOperation(value = "分页查询教材目录",httpMethod = "POST")
	AjaxResult<PageInfo<XkwTextbookCatalogVo>> getXkwTextbookCatalogPageListByCondition(@RequestBody XkwTextbookCatalogConditionBo condition);

	/**
	 * 查询所有教材目录列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwTextbookCatalog/list")
	@ApiOperation(value = "查询所有教材目录",httpMethod = "POST")
	AjaxResult<List<XkwTextbookCatalogVo>> getXkwTextbookCatalogListByCondition(@RequestBody XkwTextbookCatalogConditionBo condition);






}

