package com.light.aiszzy.xkw.xkwKnowledgePoints.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsBo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 知识树接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface XkwKnowledgePointsApi  {

	/**
	 * 查询知识树列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwKnowledgePoints/pageList")
	@ApiOperation(value = "分页查询知识树",httpMethod = "POST")
	AjaxResult<PageInfo<XkwKnowledgePointsVo>> getXkwKnowledgePointsPageListByCondition(@RequestBody XkwKnowledgePointsConditionBo condition);

	/**
	 * 查询所有知识树列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwKnowledgePoints/list")
	@ApiOperation(value = "查询所有知识树",httpMethod = "POST")
	AjaxResult<List<XkwKnowledgePointsVo>> getXkwKnowledgePointsListByCondition(@RequestBody XkwKnowledgePointsConditionBo condition);


}

