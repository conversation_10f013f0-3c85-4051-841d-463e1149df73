package com.light.aiszzy.userPaper.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户上传试卷
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class UserPaperBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户上传试卷id
	 */
	@ApiModelProperty("用户上传试卷id")
	private Long userPaperId;
	
	/**
	 * 主键id
	 */
	@ApiModelProperty("主键id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 试卷名称
	 */
	@ApiModelProperty("试卷名称")
	private String paperName;

	/**
	 * 学校oid
	 */
	@ApiModelProperty("学校oid")
	private String orgCode;
	/**
	 * 年份
	 */
	@ApiModelProperty("年份")
	private String year;
	/**
	 * 学期  1:上学期  2：下学期
	 */
	@ApiModelProperty("学期  1:上学期  2：下学期")
	private Integer term;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;
	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Integer subject;

	@ApiModelProperty("类型")
	private String category;

	/**
	 * 版本，存储教辅的版本信息
	 */
	@ApiModelProperty("版本，存储教辅的版本信息")
	private Long textBookVersionId;

	/**
	 * 教材 ID
	 */
	@ApiModelProperty("教材 ID")
	private Long textBookId;

	/**
	 * 用户id（创建者）
	 */
	@ApiModelProperty("用户id（创建者）")
	private String userOid;
	/**
	 * 试卷 img 版 url (会有多个)
	 */
	@ApiModelProperty("试卷 img 版 url (会有多个)")
	private String paperImgUrls;
	/**
	 * 题目数量
	 */
	@ApiModelProperty("题目数量")
	private Long questionNum;
	/**
	 * 已完成框题个数
	 */
	private Long finishQuestionNum;
	/**
	 * 上传文件地址只是pdf
	 */
	@ApiModelProperty("上传文件地址只是pdf")
	private String fileUrl;
	/**
	 * 状态，1上传后等待划题 2划题中，3划题结束 4已经生成作业
	 */
	@ApiModelProperty("状态，1上传后等待划题 2划题中，3划题结束 4已经生成作业")
	private Integer status;

	/**
	 * 是否发布 0 否 1 是
	 */
	@ApiModelProperty("是否发布 0 否 1 是")
	private Integer isPublish;

	/**
	 * 发布时间
	 */
	@ApiModelProperty("发布时间")
	private Date publishTime;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
