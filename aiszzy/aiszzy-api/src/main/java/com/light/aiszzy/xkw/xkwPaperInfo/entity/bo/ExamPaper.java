package com.light.aiszzy.xkw.xkwPaperInfo.entity.bo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ExamPaper {

    private Head head;
    private int course_id;
    private List<Body> body;

    @Data
    public static class Head {
        private String sub_title;
        private String test_info;
        private String student_input;
        private String main_title;
        private String notice;
    }

    @Data
    public static class Body {
        private PartHead part_head;
        private List<PartBody> part_body;
    }

    @Data
    public static class PartHead {
        private String note;
        private String name;
    }

    @Data
    public static class PartBody {
        private Type type;
        private List<Question> questions;
    }

    @Data
    public static class Type {
        private String note;
        private String name;
    }

    @Data
    public static class Question {
        private String difficulty_level;
        private List<String> kpoint_ids;
        private String type_id;
        private String source;
        private String explanation;
        private List<String> catalog_ids;
        private String update_date;
        private String difficulty;
        private List<Score> score;
        private List<String> area_ids;
        private String answer;
        private List<String> grade_ids;
        private Long id;
        private int status;
        private String stem;

        // 课程ID
        private Integer course_id;

        // 标签ID列表
        private List<String> tag_ids;
        // 知识点列表
        private List<String> kpoints;

        // 试卷类型ID列表
        private List<String> paper_type_ids;

        // 试题是否包含媒体内容（0 没有，1 有音频 2 有解题视频 3 音频和解题视频都有）
        private Integer media;

        // 解题视频封面（一个解题视频对应一个封面，如果没有则为空；一般只有一个解题视频）
        private List<String> exp_video_posters;

        // 试题类型
        private IdNamePair<String> type;

        // 试题出现在试卷中的年份，可能多个
        private List<String> years;

        // 标签列表
        private List<String> tags;

        // 单词列表
        private List<String> en_words;

        // 教材目录列表
        private List<String> catalogs;

        // 课程
        private IdNamePair<Integer> course;

        // 在线作答（0 不支持，1 支持）
        private Integer answer_scoreable;

        // 试题入库日期
        private Date create_date;

        // 单词ID列表
        private List<String> en_word_ids;

        // 推题方式，0=算法推荐，1=人工推荐
        private Integer recommend_mode;


    }

    // IdNamePair 泛型类
    @Data
    public static class IdNamePair<T> {
        public T id;
        public String name;
    }

    @Data
    public static class Score {
        private int score;
        private int index;
    }

    @Data
    public static class YunhenQuestion {
        private String questionId;       // 问题ID
        private String stem;             // 题干
        private String explanation;      // 解析
        private String answer;           // 答案
        private String difficulty;       // 难度
        private String questionTypeId;// 类型ID
        private String questionTypeName;        // 类型名称
        private Integer quesorderNum;        // 类型名称
    }
}
