package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkBookOptionRecordVo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业本映射印送记录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface HomeworkBookOptionRecordApi  {

	/**
	 * 查询作业本映射印送记录列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/homeworkBookOptionRecord/pageList")
	@ApiOperation(value = "分页查询作业本映射印送记录",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkBookOptionRecordVo>> getHomeworkBookOptionRecordPageListByCondition(@RequestBody HomeworkBookOptionRecordConditionBo condition);

	/**
	 * 查询所有作业本映射印送记录列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/homeworkBookOptionRecord/list")
	@ApiOperation(value = "查询所有作业本映射印送记录",httpMethod = "POST")
	AjaxResult<List<HomeworkBookOptionRecordVo>> getHomeworkBookOptionRecordListByCondition(@RequestBody HomeworkBookOptionRecordConditionBo condition);

	/**
	 * 查询作业本映射印送记录列表
	 * <AUTHOR>
	 * @date 2025-04-01 14:24:49
	 */
	@PostMapping("/homeworkBookOptionRecord/count")
	@ApiOperation(value = "作业本映射印送记录个数",httpMethod = "POST")
	AjaxResult getHomeworkBookOptionRecordCountByCondition(@RequestBody HomeworkBookOptionRecordConditionBo condition);



	/**
	 * 新增作业本映射印送记录
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/homeworkBookOptionRecord/add")
	@ApiOperation(value = "新增作业本映射印送记录",httpMethod = "POST")
	AjaxResult addHomeworkBookOptionRecord(@Validated @RequestBody HomeworkBookOptionRecordBo homeworkBookOptionRecordBo);

	/**
	 * 修改作业本映射印送记录
	 * @param homeworkBookOptionRecordBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/homeworkBookOptionRecord/update")
	@ApiOperation(value = "修改作业本映射印送记录",httpMethod = "POST")
	AjaxResult updateHomeworkBookOptionRecord(@Validated @RequestBody HomeworkBookOptionRecordBo homeworkBookOptionRecordBo);

	/**
	 * 查询作业本映射印送记录详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/homeworkBookOptionRecord/detail")
	@ApiOperation(value = "查询作业本映射印送记录详情",httpMethod = "GET")
	AjaxResult<HomeworkBookOptionRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业本映射印送记录
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/homeworkBookOptionRecord/delete")
	@ApiOperation(value = "删除作业本映射印送记录",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

