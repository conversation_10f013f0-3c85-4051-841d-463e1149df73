package com.light.aiszzy.schoolResourcesQuestion.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.schoolResourcesQuestion.api.SchoolResourcesQuestionSimilarRelationApi;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 资源库题目相似题接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "schoolResourcesQuestionSimilarRelationApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = SchoolResourcesQuestionSimilarRelationApiService.SchoolResourcesQuestionSimilarRelationApiFallbackFactory.class)
@Component
public interface SchoolResourcesQuestionSimilarRelationApiService  extends SchoolResourcesQuestionSimilarRelationApi {

	@Component
	class SchoolResourcesQuestionSimilarRelationApiFallbackFactory implements FallbackFactory<SchoolResourcesQuestionSimilarRelationApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(SchoolResourcesQuestionSimilarRelationApiService.SchoolResourcesQuestionSimilarRelationApiFallbackFactory.class);
		@Override
		public SchoolResourcesQuestionSimilarRelationApiService create(Throwable cause) {
			SchoolResourcesQuestionSimilarRelationApiService.SchoolResourcesQuestionSimilarRelationApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new SchoolResourcesQuestionSimilarRelationApiService() {
				public AjaxResult getSchoolResourcesQuestionSimilarRelationPageListByCondition(SchoolResourcesQuestionSimilarRelationConditionBo condition){
					return AjaxResult.fail("资源库题目相似题查询失败");
				}
				public AjaxResult getSchoolResourcesQuestionSimilarRelationListByCondition(SchoolResourcesQuestionSimilarRelationConditionBo condition){
					return AjaxResult.fail("资源库题目相似题查询失败");
				}

				public AjaxResult addSchoolResourcesQuestionSimilarRelation(SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo){
					return AjaxResult.fail("资源库题目相似题新增失败");
				}

				public AjaxResult updateSchoolResourcesQuestionSimilarRelation(SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo){
					return AjaxResult.fail("资源库题目相似题更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("资源库题目相似题获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("资源库题目相似题删除失败");
				}
			};
		}
	}
}