package com.light.aiszzy.homework.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 作业班级信息，包含疑问项汇总
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@Data
public class HomeworkClassInfoVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 作业名称
     */
    private String homeworkOid;

    /**
     * 学校id
     */
    private String orgCode;

    /**
     * 班级classId
     */
    private Long classId;

    /**
     * 学科code
     */
    private Long subject;

    /**
     * 学期  1:上学期  2：下学期
     */
    private Long term;

    /**
     * 剩余疑问项数量
     */
    private Long remainderDoubtCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
