package com.light.aiszzy.practiceBook.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 教辅题目表，用于存储题目信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class PracticeBookQuestionBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 教辅题目表，用于存储题目信息id
	 */
	@ApiModelProperty("教辅题目表，用于存储题目信息id")
	private Long practiceBookQuestionId;
	
	/**
	 * 自增主键，唯一标识每一条题目记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条题目记录")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 关联的教辅OID
	 */
	@ApiModelProperty("关联的教辅OID")
	private String practiceBookOid;
	/**
	 * 关联的教辅目录OID
	 */
	@ApiModelProperty("关联的教辅目录OID")
	private String practiceBookCatalogOid;
	/**
	 * 关联的页码图片OID
	 */
	@ApiModelProperty("关联的页码图片OID")
	private String practiceBookPageOid;
	/**
	 * 资源题目oid
	 */
	@ApiModelProperty("资源题目oid")
	private String questionOid;
	/**
	 * 学科网xkw，好未来hwl等
	 */
	@ApiModelProperty("学科网xkw，好未来hwl等")
	private String thirdSourceType;
	/**
	 * 添加外部id
	 */
	@ApiModelProperty("添加外部id")
	private String thirdOutId;
	/**
	 * 图片地址
	 */
	@ApiModelProperty("图片地址")
	private String imageUrl;

	/**
	 * 题目页码，跨页记录起始页码
	 */
	@ApiModelProperty("题目页码，跨页记录起始页码")
	private Integer pageNo;

	/**
	 * 每页题目排序
	 */
	@ApiModelProperty("每页题目排序")
	private Long pageNoOrderNum;
	/**
	 * 划题是否完成  0：未完成 1：完成
	 */
	@ApiModelProperty("划题是否完成  0：未完成 1：完成")
	private Integer markStatus;

	/**
	 * 题目题干是否编辑  0 否 1 是
	 */
	@ApiModelProperty("题目题干是否编辑  0 否 1 是")
	private Integer isQuestionModify;

	/**
	 * 是否是跨页题，0：否，1：是
	 */
	@ApiModelProperty("是否是跨页题，0：否，1：是")
	private Integer isCrossPage;
	/**
	 * 题目内容json
	 */
	@ApiModelProperty("题目内容json")
	private String questionContentJson;
	/**
	 * 外部关系类型（原题，近似题，未查到）
	 */
	@ApiModelProperty("外部关系类型（原题，近似题，未查到）")
	private String outQuestionRelationType;
	/**
	 * 外部关系类型是否确认 0未确认 1确认
	 */
	@ApiModelProperty("外部关系类型是否确认 0未确认 1确认")
	private Integer outQuestionRelationTypeStatus;
	/**
	 * orc识别内容（第三方识别，在高拍情况下比对题目查找页码）
	 */
	@ApiModelProperty("orc识别内容（第三方识别，在高拍情况下比对题目查找页码）")
	private String ocrContent;
	/**
	 * 跟原题相似度
	 */
	@ApiModelProperty("跟原题相似度")
	private String similarity;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;


	@ApiModelProperty
	private String position;


	@ApiModelProperty("下一个题目 OID")
	private String nextPracticeBookQuestionOid;

}
