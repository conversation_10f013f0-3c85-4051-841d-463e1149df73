package com.light.aiszzy.xkw.xkwSubject.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 学科
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwSubjectVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每一条目录记录
     */
    private Long id;

    /**
     * 入参
     */
    private String subject;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除（1：正常 2：删除）
     */
    private Integer isDelete;

}
