package com.light.aiszzy.device.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.device.entity.vo.DeviceVo;
import com.light.aiszzy.device.entity.bo.DeviceBo;
import com.light.aiszzy.device.entity.bo.DeviceConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface DeviceApi {

    /**
     * 查询设备表列表
     * 
     * <AUTHOR>
     * @date 2025-07-11 15:21:33
     */
    @PostMapping("/device/pageList")
    @ApiOperation(value = "分页查询设备表", httpMethod = "POST")
    AjaxResult<PageInfo<DeviceVo>> getDevicePageListByCondition(@RequestBody DeviceConditionBo condition);

    /**
     * 查询所有设备表列表
     * 
     * <AUTHOR>
     * @date 2025-07-11 15:21:33
     */
    @PostMapping("/device/list")
    @ApiOperation(value = "查询所有设备表", httpMethod = "POST")
    AjaxResult<List<DeviceVo>> getDeviceListByCondition(@RequestBody DeviceConditionBo condition);

    /**
     * 新增设备表
     * 
     * <AUTHOR>
     * @date 2025-07-11 15:21:33
     */
    @PostMapping("/device/add")
    @ApiOperation(value = "新增设备表", httpMethod = "POST")
    AjaxResult addDevice(@Validated @RequestBody DeviceBo deviceBo);

    /**
     * 修改设备表
     * 
     * @param deviceBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-11 15:21:33
     */
    @PostMapping("/device/update")
    @ApiOperation(value = "修改设备表", httpMethod = "POST")
    AjaxResult updateDevice(@Validated @RequestBody DeviceBo deviceBo);

    /**
     * 查询设备表详情
     * 
     * @param oid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-11 15:21:33
     */
    @GetMapping("/device/detail")
    @ApiOperation(value = "查询设备表详情", httpMethod = "GET")
    AjaxResult<DeviceVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

    /**
     * 根据硬件序列号查询设备表详情
     * 
     * @param hardwareCode
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-11 15:21:33
     */
    @GetMapping("/device/detailByHardwareCode")
    @ApiOperation(value = "查询设备表详情", httpMethod = "GET")
    AjaxResult<DeviceVo>
        getDetailByHardwareCode(@NotNull(message = "请选择数据") @RequestParam("hardwareCode") String hardwareCode);

    /**
     * 删除设备表
     * 
     * @param oid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-11 15:21:33
     */
    @GetMapping("/device/delete")
    @ApiOperation(value = "删除设备表", httpMethod = "GET")
    @ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
    AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

    /**
     * 根据软件激活码和硬件序列号查询设备表详情
     *
     * @param activationCode
     * @param hardwareCode
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-16
     */
    @GetMapping("/device/detailByActivationCodeAndHardwareCode")
    @ApiOperation(value = "根据软件激活码查询设备表详情", httpMethod = "GET")
    AjaxResult<DeviceVo> getDetailByActivationCodeAndHardwareCode(
        @RequestParam(value = "activationCode", required = false) String activationCode,
        @RequestParam(value = "hardwareCode", required = false) String hardwareCode);

    /**
     * 根据软件激活码和硬件序列号查询已激活的设备表详情
     *
     * @param activationCode
     * @param hardwareCode
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-16
     */
    @GetMapping("/device/detailByActivationCodeAndHardwareCodeForActivated")
    @ApiOperation(value = "根据软件激活码查询已激活的设备表详情", httpMethod = "GET")
    AjaxResult<DeviceVo> getDetailByActivationCodeAndHardwareCodeForActivated(
        @RequestParam(value = "activationCode", required = false) String activationCode,
        @RequestParam(value = "hardwareCode", required = false) String hardwareCode);

    /**
     * 设备激活功能
     * 
     * @param deviceBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-16
     */
    @PostMapping("/device/activate")
    @ApiOperation(value = "设备激活", httpMethod = "POST")
    AjaxResult activateDevice(@Validated @RequestBody DeviceBo deviceBo);

}
