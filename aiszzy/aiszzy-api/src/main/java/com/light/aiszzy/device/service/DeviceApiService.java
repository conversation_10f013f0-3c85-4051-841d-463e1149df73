package com.light.aiszzy.device.service;

import com.light.aiszzy.device.entity.vo.DeviceVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.device.api.DeviceApi;
import com.light.aiszzy.device.entity.bo.DeviceBo;
import com.light.aiszzy.device.entity.bo.DeviceConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 设备表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@FeignClient(contextId = "deviceApiService", value = "light-aiszzy", configuration = FeignClientInterceptor.class,
    fallbackFactory = DeviceApiService.DeviceApiFallbackFactory.class)
@Component
public interface DeviceApiService extends DeviceApi {

    @Component
    class DeviceApiFallbackFactory implements FallbackFactory<DeviceApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(DeviceApiFallbackFactory.class);

        @Override
        public DeviceApiService create(Throwable cause) {
            DeviceApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
            return new DeviceApiService() {
                public AjaxResult getDevicePageListByCondition(DeviceConditionBo condition) {
                    return AjaxResult.fail("设备表查询失败");
                }

                public AjaxResult getDeviceListByCondition(DeviceConditionBo condition) {
                    return AjaxResult.fail("设备表查询失败");
                }

                public AjaxResult addDevice(DeviceBo deviceBo) {
                    return AjaxResult.fail("设备表新增失败");
                }

                public AjaxResult updateDevice(DeviceBo deviceBo) {
                    return AjaxResult.fail("设备表更新失败");
                }

                public AjaxResult getDetail(String oid) {
                    return AjaxResult.fail("设备表获取详情失败");
                }

                @Override
                public AjaxResult<DeviceVo> getDetailByHardwareCode(String hardwareCode) {
                    return AjaxResult.fail("设备表获取详情失败");
                }

                public AjaxResult delete(String oid) {
                    return AjaxResult.fail("设备表删除失败");
                }

                @Override
                public AjaxResult<DeviceVo> getDetailByActivationCodeAndHardwareCode(String activationCode,
                    String hardwareCode) {
                    return AjaxResult.fail("查询设备信息失败");
                }

                @Override
                public AjaxResult<DeviceVo> getDetailByActivationCodeAndHardwareCodeForActivated(String activationCode,
                    String hardwareCode) {
                    return AjaxResult.fail("查询已激活设备信息失败");
                }

                @Override
                public AjaxResult activateDevice(DeviceBo deviceBo) {
                    return AjaxResult.fail("设备激活失败");
                }
            };
        }
    }
}