package com.light.aiszzy.homeworkResult.api;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 扫描结构每页处理结果
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
public interface HomeworkPageResultApi  {

	/**
	 * 查询扫描结构每页处理结果列表
	 * <AUTHOR>
	 * @date 2025-07-30 15:52:36
	 */
	@PostMapping("/homeworkPageResult/pageList")
	@ApiOperation(value = "分页查询扫描结构每页处理结果",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkPageResultVo>> getHomeworkPageResultPageListByCondition(@RequestBody HomeworkPageResultConditionBo condition);

	/**
	 * 查询所有扫描结构每页处理结果列表
	 * <AUTHOR>
	 * @date 2025-07-30 15:52:36
	 */
	@PostMapping("/homeworkPageResult/list")
	@ApiOperation(value = "查询所有扫描结构每页处理结果",httpMethod = "POST")
	AjaxResult<List<HomeworkPageResultVo>> getHomeworkPageResultListByCondition(@RequestBody HomeworkPageResultConditionBo condition);

	@PostMapping("/doubt/dealNoStudent")
	@ApiOperation(value = "处理学生未匹配", httpMethod = "POST")
	AjaxResult dealNoStudent(@RequestBody HomeworkPageResultBo bo);

	@PostMapping("/doubt/dealRepeat")
	@ApiOperation(value = "处理学生重复", httpMethod = "POST")
	AjaxResult dealRepeat(@RequestBody HomeworkPageResultBo bo);

	/**
	 * 新增扫描结构每页处理结果
	 * <AUTHOR>
	 * @date 2025-07-30 15:52:36
	 */
	@PostMapping("/homeworkPageResult/add")
	@ApiOperation(value = "新增扫描结构每页处理结果",httpMethod = "POST")
	AjaxResult addHomeworkPageResult(@Validated @RequestBody HomeworkPageResultBo homeworkPageResultBo);

	/**
	 * 修改扫描结构每页处理结果
	 * @param homeworkPageResultBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 15:52:36
	 */
	@PostMapping("/homeworkPageResult/update")
	@ApiOperation(value = "修改扫描结构每页处理结果",httpMethod = "POST")
	AjaxResult updateHomeworkPageResult(@Validated @RequestBody HomeworkPageResultBo homeworkPageResultBo);

	/**
	 * 查询扫描结构每页处理结果详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 15:52:36
	 */
	@GetMapping("/homeworkPageResult/detail")
	@ApiOperation(value = "查询扫描结构每页处理结果详情",httpMethod = "GET")
	AjaxResult<HomeworkPageResultVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除扫描结构每页处理结果
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 15:52:36
	 */
	@GetMapping("/homeworkPageResult/delete")
	@ApiOperation(value = "删除扫描结构每页处理结果",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

