package com.light.aiszzy.homework.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 作业本映射印送记录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class HomeworkBookOptionRecordConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 作业本id
	 */
	@ApiModelProperty("作业本id")
	private String bookOid;

	/**
	 * 记录描述
	 */
	@ApiModelProperty("记录描述")
	private String comment;

	/**
	 * 状态 去1待印刷，2印刷中，3已经配送
	 */
	@ApiModelProperty("状态 去1待印刷，2印刷中，3已经配送")
	private Integer status;

	/**
	 * 配送时间
	 */
	@ApiModelProperty("配送时间")
	private Date deliveryTime;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
