package com.light.aiszzy.xkw.xkwPaperInfo.service;


import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkw.xkwPaperInfo.api.XkwPaperInfoApi;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@FeignClient(contextId = "xkwPaperInfoApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwPaperInfoApiService.XkwPaperInfoApiFallbackFactory.class)
@Component
public interface XkwPaperInfoApiService  extends XkwPaperInfoApi {

	@Component
	class XkwPaperInfoApiFallbackFactory implements FallbackFactory<XkwPaperInfoApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwPaperInfoApiService.XkwPaperInfoApiFallbackFactory.class);
		@Override
		public XkwPaperInfoApiService create(Throwable cause) {
			XkwPaperInfoApiService.XkwPaperInfoApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwPaperInfoApiService() {
				public AjaxResult getXkwPaperInfoPageListByCondition(XkwPaperInfoConditionBo condition){
					return AjaxResult.fail("题目信息查询失败");
				}
				public AjaxResult getXkwPaperInfoListByCondition(XkwPaperInfoConditionBo condition){
					return AjaxResult.fail("题目信息查询失败");
				}

				public AjaxResult addXkwPaperInfo(QuestionBo bo) {
					return AjaxResult.fail("题目信息新增失败");
				}

				@Override
				public AjaxResult editXkwPaperInfo(QuestionBo bo) {
					return AjaxResult.fail("题目信息更新失败");
				}

				public AjaxResult updateXkwPaperInfo(XkwPaperInfoBo xkwPaperInfoBo){
					return AjaxResult.fail("题目信息更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("题目信息获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("题目信息删除失败");
				}
			};
		}
	}
}