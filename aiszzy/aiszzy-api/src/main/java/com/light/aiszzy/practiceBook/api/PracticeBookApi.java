package  com.light.aiszzy.practiceBook.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教辅信息表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface PracticeBookApi  {

	/**
	 * 查询教辅信息表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBook/pageList")
	@ApiOperation(value = "分页查询教辅信息表",httpMethod = "POST")
	AjaxResult<PageInfo<PracticeBookVo>> getPracticeBookPageListByCondition(@RequestBody PracticeBookConditionBo condition);

	/**
	 * 查询所有教辅信息表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBook/list")
	@ApiOperation(value = "查询所有教辅信息表",httpMethod = "POST")
	AjaxResult<List<PracticeBookVo>> getPracticeBookListByCondition(@RequestBody PracticeBookConditionBo condition);


	/**
	 * 新增教辅信息表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBook/add")
	@ApiOperation(value = "新增教辅信息表",httpMethod = "POST")
	AjaxResult addPracticeBook(@Validated @RequestBody PracticeBookBo practiceBookBo);

	/**
	 * 修改教辅信息表
	 * @param practiceBookBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBook/update")
	@ApiOperation(value = "修改教辅信息表",httpMethod = "POST")
	AjaxResult updatePracticeBook(@Validated @RequestBody PracticeBookBo practiceBookBo);

	/**
	 * 查询教辅信息表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/practiceBook/detail")
	@ApiOperation(value = "查询教辅信息表详情",httpMethod = "GET")
	AjaxResult<PracticeBookVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除教辅信息表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/practiceBook/delete")
	@ApiOperation(value = "删除教辅信息表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);


	/**
	 * 根据 oid 更新目录文件地址
	 * @param practiceBookBo the practice book 教辅 { catalogPath, oid}
 	 * @return {@link AjaxResult }
	 */
	@PostMapping("/practiceBook/updateCatalogPathByOid")
    AjaxResult updateCatalogPathByOid(@RequestBody PracticeBookBo practiceBookBo);


	/**
	 * 根据 oid 更新教辅文件信息
	 * @param practiceBookBo the practice book 教辅 {fileType, filePath, oid}
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/practiceBook/updateFileInfoByOid")
	AjaxResult<Void> updateFileInfoByOid(@RequestBody PracticeBookBo practiceBookBo);

	/**
	 *  更新教辅题目数量信息 {totalQuestionNum, finishQuestionNum,oid}
	 * @param practiceBookBo {totalQuestionNum, finishQuestionNum,oid}
	 * @return {@link AjaxResult }<{@link Void }>
	 */
	@PostMapping("/practiceBook/updateQuestionNumInfoByOid")
	AjaxResult<Void> updateQuestionNumInfoByOid(@RequestBody PracticeBookBo practiceBookBo);

	/**
	 *  更改是否支持高拍
	 * @param practiceBookBo {IsHighShots,oid}
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/practiceBook/updateIsHighShotsByOid")
	AjaxResult updateIsHighShotsByOid(@RequestBody PracticeBookBo practiceBookBo);


	/**
	 *  提交
	 * @param practiceBookBo the practice book {oid, updateBy}
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/practiceBook/commit")
	AjaxResult commit(@RequestBody PracticeBookBo practiceBookBo);

	/**
	 * 审核
	 *
	 * @param practiceBookBo  the practice book {oid, reviewStatus, reviewComment}
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/practiceBook/review")
	AjaxResult review(@RequestBody PracticeBookBo practiceBookBo);

	/**
	 *  更改状态
	 * @param practiceBookBo the practice book {oid, status}
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/practiceBook/changeStatus")
	AjaxResult changeStatus(@RequestBody PracticeBookBo practiceBookBo);
}

