package com.light.aiszzy.hwlOpen.service;

import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.hwlOpen.api.HwlOpenApi;
import com.light.beans.ImageRequestBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 好未来开放平台接口服务
 *
 * <AUTHOR>
 * @date 2025/3/17 17:07
 */
@FeignClient(contextId = "hwlOpenApiService", value = "light-aiszzy", configuration = FeignClientInterceptor.class, fallbackFactory = HwlOpenApiService.HwlOpenApiFallbackFactory.class)
@Component
public interface HwlOpenApiService extends HwlOpenApi {

    @Component
    class HwlOpenApiFallbackFactory implements FallbackFactory<HwlOpenApiService> {
        private static final Logger log = LoggerFactory.getLogger(HwlOpenApiFallbackFactory.class);

        @Override
        public HwlOpenApiService create(Throwable cause) {
            log.error("HwlOpenApiService调用失败", cause);
            return new HwlOpenApiService() {
                @Override
                public AjaxResult automaticBox(ImageRequestBo imageRequestBo) {
                    return AjaxResult.fail("好未来题目框选服务暂时不可用");
                }

                @Override
                public AjaxResult educationOcr(ImageRequestBo imageRequestBo) {
                    return AjaxResult.fail("好未来教育通用OCR服务暂时不可用");
                }

                @Override
                public AjaxResult homeworkCorrection(ImageRequestBo imageRequestBo) {
                    return AjaxResult.fail("好未来作业批改服务暂时不可用");
                }
            };
        }
    }
}
