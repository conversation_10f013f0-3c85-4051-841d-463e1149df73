package com.light.aiszzy.homework.entity.vo;

import com.light.redis.annotations.DictMark;
import com.light.redis.entity.Dict;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 作业本
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:34
 */
@Data
public class HomeworkBookVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    private Integer isCurrent;
    /**
     * 作业本名称
     */
    private String name;

    /**
     * 学校CODE
     */
    private String orgCode;

    /**
     * 学科code
     */
    private Integer subject;

    @DictMark(dictType = "subject")
    private Dict subjectCodeDic;

    /**
     * 年级code
     */
    private Integer grade;

    @DictMark(dictType = "book_grade")
    private Dict gradeCodeDic;

    /**
     * 启用年份
     */
    private String year;

    /**
     * 学期，1上学期，2下学期
     */
    private Integer term;

    @DictMark(dictType = "student_term")
    private Dict termDic;

    /**
     * 教材版本
     */
    private Long textBookVersionId;


    private String textBookVersionName;

    @DictMark(dictType = "text_book_version")
    private Dict textBookVersionDic;

    /**
     * 教材
     */
    private Long textBookId;

    /**
     * 1基础2
巩固3
提高
     */
    private Integer exerciseType;

    @DictMark(dictType = "exercise_type")
    private Dict exerciseTypeDic;

    /**
     * 封面
     */
    private String coverUrl;

    /**
     * 作业数量
     */
    private Integer num;

    /**
     * 状态0未发布，1已经发布
     */
    private Integer status;

    /**
     * 是否有未审核作业1是，0否
     */
    private Integer hasNoConfirm;

    /**
     * 是否完结1是，0否
     */
    private Integer isCompleted;

    /**
     * 来源（1、自建；2、引用教辅；3引用作业本）
     */
    private Integer sourceType;

    /**
     * 教辅的oid或作业本oid
     */
    private String sourceOid;

    /**
     * 运营创建和老师创建
     */
    private String createSource;

    /**
     * 所有作业pdf合并生成pdf下载地址
     */
    private String homeworkPdfUrl;

    /**
     * 所有作业答案解析合并生成word下载地址
     */
    private String homeworkAnswerPdfUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;


    //是否有作业
    private Boolean hasHomework;
    //是否有审核作业
    private Boolean hasCheckHomework;
    //是否有目录
    private Boolean hasCatalog;

}
