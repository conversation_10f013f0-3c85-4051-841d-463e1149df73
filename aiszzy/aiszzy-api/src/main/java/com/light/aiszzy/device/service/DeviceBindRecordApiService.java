package com.light.aiszzy.device.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.device.api.DeviceBindRecordApi;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordBo;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 设备表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@FeignClient(contextId = "deviceBindRecordApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = DeviceBindRecordApiService.DeviceBindRecordApiFallbackFactory.class)
@Component
public interface DeviceBindRecordApiService  extends DeviceBindRecordApi {

	@Component
	class DeviceBindRecordApiFallbackFactory implements FallbackFactory<DeviceBindRecordApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(DeviceBindRecordApiFallbackFactory.class);
		@Override
		public DeviceBindRecordApiService create(Throwable cause) {
			DeviceBindRecordApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new DeviceBindRecordApiService() {
				public AjaxResult getDeviceBindRecordPageListByCondition(DeviceBindRecordConditionBo condition){
					return AjaxResult.fail("设备表查询失败");
				}
				public AjaxResult getDeviceBindRecordListByCondition(DeviceBindRecordConditionBo condition){
					return AjaxResult.fail("设备表查询失败");
				}

				public AjaxResult addDeviceBindRecord(DeviceBindRecordBo deviceBindRecordBo){
					return AjaxResult.fail("设备表新增失败");
				}

				public AjaxResult updateDeviceBindRecord(DeviceBindRecordBo deviceBindRecordBo){
					return AjaxResult.fail("设备表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("设备表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("设备表删除失败");
				}
			};
		}
	}
}