package com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 试题难度等级
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwQuestionDifficultiesBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 试题难度等级id
	 */
	@ApiModelProperty("试题难度等级id")
	private Long xkwQuestionDifficultiesId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * 难度名称
	 */
	@ApiModelProperty("难度名称")
	private String name;
	/**
	 * 难度档上限值
	 */
	@ApiModelProperty("难度档上限值")
	private String ceiling;
	/**
	 * 难度档下限值
	 */
	@ApiModelProperty("难度档下限值")
	private String flooring;

}
