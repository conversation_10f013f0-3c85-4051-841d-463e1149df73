package com.light.aiszzy.xkw.xkwPaperInfo.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 题目信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class XkwPaperInfoConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 试卷id
	 */
	@ApiModelProperty("试卷id")
	private String paperId;

	/**
	 * 副标题
	 */
	@ApiModelProperty("副标题")
	private String headSubTitle;

	/**
	 * 试卷信息
	 */
	@ApiModelProperty("试卷信息")
	private String headTestInfo;

	/**
	 * 学生填写部分
	 */
	@ApiModelProperty("学生填写部分")
	private String headStudentInput;

	/**
	 * 试卷标题
	 */
	@ApiModelProperty("试卷标题")
	private String headMainTitle;

	/**
	 * 提示
	 */
	@ApiModelProperty("提示")
	private String headNotice;

	/**
	 * 课程ID
	 */
	@ApiModelProperty("课程ID")
	private Long courseId;

	/**
	 * 试卷题目
	 */
	@ApiModelProperty("试卷题目")
	private String bodyQuestions;

	/**
	 * 作业oid
	 */
	@ApiModelProperty("作业oid")
	private String homeworkOid;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@ApiModelProperty("是否删除（1：正常 2：删除）")
	private Integer isDelete;

}
