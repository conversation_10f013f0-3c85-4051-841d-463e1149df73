package com.light.aiszzy.homeworkResult.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 校本作业结果表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@Data
public class HomeworkResultConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@ApiModelProperty("作业id")
	private String homeworkOid;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private String orgCode;

	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;

	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private String classId;

	/**
	 * 学生oid
	 */
	@ApiModelProperty("学生oid")
	private String stuOid;

	/**
	 * 学生姓名
	 */
	@ApiModelProperty("学生姓名")
	private String stuName;

	/**
	 * 学号
	 */
	@ApiModelProperty("学号")
	private String stuNo;

	/**
	 * 页码个数合集
	 */
	@ApiModelProperty("页码个数合集")
	private String pageNos;

	/**
	 * 学生作答存储地址
	 */
	@ApiModelProperty("学生作答存储地址")
	private String stuAnswerUrls;

	/**
	 * 学生作答图片信息json存储
	 */
	@ApiModelProperty("学生作答图片信息json存储")
	private String stuAnswerPageInfoJson;

	/**
	 * 是否完整，所有页都扫描，0：否，1：是
	 */
	@ApiModelProperty("是否完整，所有页都扫描，0：否，1：是")
	private Integer isComplete;

	/**
	 * 作业正确题目个数
	 */
	@ApiModelProperty("作业正确题目个数")
	private Integer rightNum;

	/**
	 * 作业错误题目个数
	 */
	@ApiModelProperty("作业错误题目个数")
	private Integer wrongNum;
	/**
	 * 本页（含正反页）未知是否正确题目个数
	 */
	@ApiModelProperty("作业未知是否正确题目个数")
	private Integer unknownNum;
	/**
	 * 本页（含正反页）未知是否正确题目个数
	 */
	@ApiModelProperty("作业题目总个数")
	private Integer totalNum;
	/**
	 * 作业题目正确率，万分之，页面展示需要除以100
	 */
	@ApiModelProperty("作业题目正确率，万分之，页面展示需要除以100")
	private Integer accuracyRate;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
