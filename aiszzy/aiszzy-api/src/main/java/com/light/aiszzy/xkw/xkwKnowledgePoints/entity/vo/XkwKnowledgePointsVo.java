package com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo;

import com.light.utils.TreeBean;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 知识树
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class XkwKnowledgePointsVo implements Serializable, TreeBean<XkwKnowledgePointsVo> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 知识点名称
     */
    private String name;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 节点深度，一级节点的深度为1，二级节点的深度为2，以此类推。
     */
    private String depth;

    /**
     * root节点的ID
     */
    private String rootId;

    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 适用于精简版
     */
    private String forLite;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 一级知识点的标签，仅当节点为一级知识点时有效，分为：STANDARD|课标,CONTEST|竞赛,PREPARE|学段衔接,NEXT-GEN|新一代
     */
    private String tag;

    /**
     * 	节点类型，可用值：NODE、KNOWLEDGE_POINT、TESTING_POINT，分别代表普通节点、知识点、考点
     */
    private String type;

    /**
     * 排序值
     */
    private Long ordinal;

    private List<XkwKnowledgePointsVo> children;


    @Override
    public String getTreeKey() {
        return id.toString();
    }

    @Override
    public String getParentTreeKey() {
        return parentId;
    }

    @Override
    public List<XkwKnowledgePointsVo> getChildren() {
        return this.children;
    }

    @Override
    public void setChildren(List<XkwKnowledgePointsVo> children) {
        this.children = children;
    }
}
