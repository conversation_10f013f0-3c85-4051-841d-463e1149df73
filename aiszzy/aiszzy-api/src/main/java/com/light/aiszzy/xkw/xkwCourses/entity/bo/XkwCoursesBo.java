package com.light.aiszzy.xkw.xkwCourses.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课程
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class XkwCoursesBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 课程id
	 */
	@ApiModelProperty("课程id")
	private Long xkwCoursesId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * 课程名称
	 */
	@ApiModelProperty("课程名称")
	private String name;
	/**
	 * 学科ID
	 */
	@ApiModelProperty("学科ID")
	private Integer subjectId;
	/**
	 * 学段ID
	 */
	@ApiModelProperty("学段ID")
	private Long stageId;
	/**
	 * 排序值
	 */
	@ApiModelProperty("排序值")
	private Long ordinal;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
