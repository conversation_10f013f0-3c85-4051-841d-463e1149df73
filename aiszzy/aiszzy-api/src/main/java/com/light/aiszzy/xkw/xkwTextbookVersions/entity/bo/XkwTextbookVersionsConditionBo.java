package com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 教材版本
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwTextbookVersionsConditionBo extends PageLimitBo{

	/**
	 * 教材版本ID
	 */
	@ApiModelProperty("教材版本ID")
	private Long id;

	/**
	 * 	课程ID
	 */
	@ApiModelProperty("	课程ID")
	private Long courseId;

	/**
	 * 启用年份
	 */
	@ApiModelProperty("启用年份")
	private Long year;

	/**
	 * 排序值
	 */
	@ApiModelProperty("排序值")
	private Long ordinal;

	/**
	 * 教材版本名称
	 */
	@ApiModelProperty("教材版本名称")
	private String name;

}
