package com.light.aiszzy.practiceBook.entity.bo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 教辅题目换题请求BO，用于换题功能
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PracticeBookQuestionChangeBo extends PracticeBookQuestionBo {

    private static final long serialVersionUID = 1L;

    /**
     * 难度列表（学科网难度等级ID集合：17 容易，18 较易，19 一般，20 较难，21 困难）
     */
    @ApiModelProperty("难度列表（学科网难度等级ID集合：17 容易，18 较易，19 一般，20 较难，21 困难）")
    private List<Integer> difficultyLevels;

    /**
     * 查询题目个数，默认为5
     */
    @ApiModelProperty("查询题目个数，默认为5")
    private Integer questionCount = 5;

    /**
     * 学科网课程ID（可选，用于精确匹配）
     */
    @ApiModelProperty("学科网课程ID（可选，用于精确匹配）")
    private Integer xkwCourseId;

    /**
     * 题干文本（可选，如果不提供则从题目内容中提取）
     */
    @ApiModelProperty("题干文本（可选，如果不提供则从题目内容中提取）")
    private String questionText;



    /**
     * 新替换题目的JSON格式数据（用于设为相似题功能）
     */
    @ApiModelProperty("新替换题目的JSON格式数据（用于设为相似题功能）")
    private String similarQuestionJson;
    /**
     * 新替换题目的难度
     */
    @ApiModelProperty("新替换题目的难度")
    private Integer similarDifficultyLevel;
    /**
     * 被替换的相似的题目的第三方id(目前是学科网题目的id)
     */
    @ApiModelProperty("被替换的相似的题目的第三方id(目前是学科网题目的id)")
    private String similarThirdOutId;
}
