package com.light.aiszzy.xkw.xkwTextbook.service;


import com.light.aiszzy.xkw.xkwTextbook.api.XkwTextbookApi;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 教材接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "xkwTextbookApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwTextbookApiService.XkwTextbookApiFallbackFactory.class)
@Component
public interface XkwTextbookApiService  extends XkwTextbookApi {

	@Component
	class XkwTextbookApiFallbackFactory implements FallbackFactory<XkwTextbookApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwTextbookApiService.XkwTextbookApiFallbackFactory.class);
		@Override
		public XkwTextbookApiService create(Throwable cause) {
			XkwTextbookApiService.XkwTextbookApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwTextbookApiService() {
				public AjaxResult getXkwTextbookPageListByCondition(XkwTextbookConditionBo condition){
					return AjaxResult.fail("教材查询失败");
				}
				public AjaxResult getXkwTextbookListByCondition(XkwTextbookConditionBo condition){
					return AjaxResult.fail("教材查询失败");
				}

			};
		}
	}
}