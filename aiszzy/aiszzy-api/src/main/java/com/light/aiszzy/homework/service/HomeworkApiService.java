package com.light.aiszzy.homework.service;


import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkApi;
import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import com.light.aiszzy.homework.entity.bo.HomeworkConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "homeworkApiService", value = "light-aiszzy", configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkApiService.HomeworkApiFallbackFactory.class)
@Component
public interface HomeworkApiService extends HomeworkApi {

    @Component
    class HomeworkApiFallbackFactory implements FallbackFactory<HomeworkApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkApiService.HomeworkApiFallbackFactory.class);

        @Override
        public HomeworkApiService create(Throwable cause) {
            HomeworkApiService.HomeworkApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
            return new HomeworkApiService() {
                public AjaxResult getHomeworkPageListByCondition(HomeworkConditionBo condition) {
                    return AjaxResult.fail("作业表查询失败");
                }

                public AjaxResult getHomeworkListByCondition(HomeworkConditionBo condition) {
                    return AjaxResult.fail("作业表查询失败");
                }

                @Override
                public AjaxResult listForTeacher(HomeworkConditionBo condition) {
                    return AjaxResult.fail("作业表查询失败");
                }

                @Override
                public AjaxResult<PageInfo<HomeworkVo>> getHomeworkResultPageListByCondition(HomeworkConditionBo condition) {
                    return AjaxResult.fail("作业表查询失败");
                }

                @Override
                public AjaxResult listForBind(HomeworkConditionBo condition) {
                    return AjaxResult.fail("作业表查询失败");
                }

                @Override
                public AjaxResult listBindAndNoBind(HomeworkConditionBo condition) {
                    return AjaxResult.fail("查所有绑定和未绑定作业失败");
                }

                @Override
                public String generateCode() {
                    return null;
                }

                public AjaxResult addHomework(HomeworkBo homeworkBo) {
                    return AjaxResult.fail("作业表新增失败");
                }

                @Override
                public AjaxResult batchUpdateStatus(HomeworkBo homeworkBo) {
                    return AjaxResult.fail("批量修改作业状态失败");
                }

                public AjaxResult updateHomework(HomeworkBo homeworkBo) {
                    return AjaxResult.fail("作业表更新失败");
                }

                public AjaxResult getDetail(String oid) {
                    return AjaxResult.fail("作业表获取详情失败");
                }

                public AjaxResult delete(String oid) {
                    return AjaxResult.fail("作业表删除失败");
                }

                @Override
                public AjaxResult<HomeworkVo> cartHomework(ResourcesUserAddToCartBo bo) {
                    return AjaxResult.fail("试题篮作业接口失败");
                }

                @Override
                public AjaxResult<HomeworkVo> copyHomework(String oid) {
                    return AjaxResult.fail("复制作业失败");
                }

                @Override
                public AjaxResult originalHomework(HomeworkBo homeworkBo) {
                    return AjaxResult.fail("生成原题作业失败");
                }

                @Override
                public AjaxResult layeredHomework(HomeworkBo homeworkBo) {
                    return AjaxResult.fail("生成分层作业失败");
                }

                @Override
                public AjaxResult schoolHomework(HomeworkBo homeworkBo) {
                    return AjaxResult.fail("生成校本作业失败");
                }

            };
        }
    }
}