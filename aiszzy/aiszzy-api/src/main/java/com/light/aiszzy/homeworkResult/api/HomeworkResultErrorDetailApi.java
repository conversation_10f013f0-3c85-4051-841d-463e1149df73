package com.light.aiszzy.homeworkResult.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultErrorDetailVo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 校本作业结果详情表(错误试题)接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface HomeworkResultErrorDetailApi  {

	/**
	 * 查询校本作业结果详情表(错误试题)列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/homeworkResultErrorDetail/pageList")
	@ApiOperation(value = "分页查询校本作业结果详情表(错误试题)",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkResultErrorDetailVo>> getHomeworkResultErrorDetailPageListByCondition(@RequestBody HomeworkResultErrorDetailConditionBo condition);

	/**
	 * 查询所有校本作业结果详情表(错误试题)列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/homeworkResultErrorDetail/list")
	@ApiOperation(value = "查询所有校本作业结果详情表(错误试题)",httpMethod = "POST")
	AjaxResult<List<HomeworkResultErrorDetailVo>> getHomeworkResultErrorDetailListByCondition(@RequestBody HomeworkResultErrorDetailConditionBo condition);


	/**
	 * 新增校本作业结果详情表(错误试题)
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/homeworkResultErrorDetail/add")
	@ApiOperation(value = "新增校本作业结果详情表(错误试题)",httpMethod = "POST")
	AjaxResult addHomeworkResultErrorDetail(@Validated @RequestBody HomeworkResultErrorDetailBo homeworkResultErrorDetailBo);

	/**
	 * 修改校本作业结果详情表(错误试题)
	 * @param homeworkResultErrorDetailBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/homeworkResultErrorDetail/update")
	@ApiOperation(value = "修改校本作业结果详情表(错误试题)",httpMethod = "POST")
	AjaxResult updateHomeworkResultErrorDetail(@Validated @RequestBody HomeworkResultErrorDetailBo homeworkResultErrorDetailBo);

	/**
	 * 查询校本作业结果详情表(错误试题)详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/homeworkResultErrorDetail/detail")
	@ApiOperation(value = "查询校本作业结果详情表(错误试题)详情",httpMethod = "GET")
	AjaxResult<HomeworkResultErrorDetailVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除校本作业结果详情表(错误试题)
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/homeworkResultErrorDetail/delete")
	@ApiOperation(value = "删除校本作业结果详情表(错误试题)",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

