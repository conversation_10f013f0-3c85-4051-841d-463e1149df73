package com.light.aiszzy.question.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.question.api.QuestionApi;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.question.entity.bo.QuestionConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "questionApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = QuestionApiService.QuestionApiFallbackFactory.class)
@Component
public interface QuestionApiService  extends QuestionApi {

	@Component
	class QuestionApiFallbackFactory implements FallbackFactory<QuestionApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(QuestionApiService.QuestionApiFallbackFactory.class);
		@Override
		public QuestionApiService create(Throwable cause) {
			QuestionApiService.QuestionApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new QuestionApiService() {
				public AjaxResult getQuestionPageListByCondition(QuestionConditionBo condition){
					return AjaxResult.fail("题目表查询失败");
				}
				public AjaxResult getQuestionListByCondition(QuestionConditionBo condition){
					return AjaxResult.fail("题目表查询失败");
				}

				public AjaxResult addQuestion(QuestionBo questionBo){
					return AjaxResult.fail("题目表新增失败");
				}

				public AjaxResult updateQuestion(QuestionBo questionBo){
					return AjaxResult.fail("题目表更新失败");
				}

				public AjaxResult getDetail(String oid, String practiceBookOid){
					return AjaxResult.fail("题目表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("题目表删除失败");
				}
			};
		}
	}
}