package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkClassCommentQuestionVo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 班级作业讲评题目接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface HomeworkClassCommentQuestionApi  {

	/**
	 * 查询班级作业讲评题目列表
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassCommentQuestion/pageList")
	@ApiOperation(value = "分页查询班级作业讲评题目",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkClassCommentQuestionVo>> getHomeworkClassCommentQuestionPageListByCondition(@RequestBody HomeworkClassCommentQuestionConditionBo condition);

	/**
	 * 查询所有班级作业讲评题目列表
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassCommentQuestion/list")
	@ApiOperation(value = "查询所有班级作业讲评题目",httpMethod = "POST")
	AjaxResult<List<HomeworkClassCommentQuestionVo>> getHomeworkClassCommentQuestionListByCondition(@RequestBody HomeworkClassCommentQuestionConditionBo condition);


	/**
	 * 新增班级作业讲评题目
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassCommentQuestion/add")
	@ApiOperation(value = "新增班级作业讲评题目",httpMethod = "POST")
	AjaxResult addHomeworkClassCommentQuestion(@Validated @RequestBody HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo);

	/**
	 * 修改班级作业讲评题目
	 * @param homeworkClassCommentQuestionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassCommentQuestion/update")
	@ApiOperation(value = "修改班级作业讲评题目",httpMethod = "POST")
	AjaxResult updateHomeworkClassCommentQuestion(@Validated @RequestBody HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo);

	/**
	 * 查询班级作业讲评题目详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@GetMapping("/homeworkClassCommentQuestion/detail")
	@ApiOperation(value = "查询班级作业讲评题目详情",httpMethod = "GET")
	AjaxResult<HomeworkClassCommentQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除班级作业讲评题目
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@GetMapping("/homeworkClassCommentQuestion/delete")
	@ApiOperation(value = "删除班级作业讲评题目",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

