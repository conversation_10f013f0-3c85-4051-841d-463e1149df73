package com.light.aiszzy.statistics.api;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.SchoolGradeQuestionStatisticsVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业年级题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface SchoolGradeQuestionStatisticsApi  {

	/**
	 * 查询作业年级题目正确率列表
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/schoolGradeQuestionStatistics/pageList")
	@ApiOperation(value = "分页查询作业年级题目正确率",httpMethod = "POST")
	AjaxResult<PageInfo<SchoolGradeQuestionStatisticsVo>> getSchoolGradeQuestionStatisticsPageListByCondition(@RequestBody SchoolGradeQuestionStatisticsConditionBo condition);

	/**
	 * 查询所有作业年级题目正确率列表
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/schoolGradeQuestionStatistics/list")
	@ApiOperation(value = "查询所有作业年级题目正确率",httpMethod = "POST")
	AjaxResult<List<SchoolGradeQuestionStatisticsVo>> getSchoolGradeQuestionStatisticsListByCondition(@RequestBody SchoolGradeQuestionStatisticsConditionBo condition);


	/**
	 * 新增作业年级题目正确率
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/schoolGradeQuestionStatistics/add")
	@ApiOperation(value = "新增作业年级题目正确率",httpMethod = "POST")
	AjaxResult addSchoolGradeQuestionStatistics(@Validated @RequestBody SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo);

	/**
	 * 修改作业年级题目正确率
	 * @param schoolGradeQuestionStatisticsBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/schoolGradeQuestionStatistics/update")
	@ApiOperation(value = "修改作业年级题目正确率",httpMethod = "POST")
	AjaxResult updateSchoolGradeQuestionStatistics(@Validated @RequestBody SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo);

	/**
	 * 查询作业年级题目正确率详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@GetMapping("/schoolGradeQuestionStatistics/detail")
	@ApiOperation(value = "查询作业年级题目正确率详情",httpMethod = "GET")
	AjaxResult<SchoolGradeQuestionStatisticsVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业年级题目正确率
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@GetMapping("/schoolGradeQuestionStatistics/delete")
	@ApiOperation(value = "删除作业年级题目正确率",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

