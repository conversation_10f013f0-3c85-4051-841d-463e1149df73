package com.light.aiszzy.xkwOpen.service;

import com.light.beans.OcrBo;
import com.light.beans.SimilarBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkwOpen.api.XkwOpenApi;
import com.light.beans.TikuBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 学科网开放平台接口服务
 *
 * <AUTHOR>
 * @date 2025/3/28 14:15
 */
@FeignClient(contextId = "xkwOpenApiService", value = "light-aiszzy", configuration = FeignClientInterceptor.class, fallbackFactory = XkwOpenApiService.XkwOpenApiFallbackFactory.class)
@Component
public interface XkwOpenApiService extends XkwOpenApi {

    @Component
    class XkwOpenApiFallbackFactory implements FallbackFactory<XkwOpenApiService> {
        private static final Logger log = LoggerFactory.getLogger(XkwOpenApiFallbackFactory.class);

        @Override
        public XkwOpenApiService create(Throwable cause) {
            log.error("XkwOpenApiService调用失败", cause);
            return new XkwOpenApiService() {
                @Override
                public AjaxResult tikuSearch(TikuBo tikuBo) {
                    return AjaxResult.fail("学科网题库搜索服务暂时不可用");
                }

                @Override
                public AjaxResult similarRecommend(@RequestBody SimilarBo similarBo) {
                    return AjaxResult.fail("学科网相似题推荐服务暂时不可用");
                }

                @Override
                public AjaxResult ocr(OcrBo ocrBo) {
                    return AjaxResult.fail("ocr识别失败");
                }
            };
        }
    }
}
