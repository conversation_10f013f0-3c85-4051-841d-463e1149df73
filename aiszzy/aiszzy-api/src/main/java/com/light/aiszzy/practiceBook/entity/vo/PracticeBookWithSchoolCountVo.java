package com.light.aiszzy.practiceBook.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教辅信息扩展Vo - 包含学校开通数量信息
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PracticeBookWithSchoolCountVo extends PracticeBookVo {

    private static final long serialVersionUID = 1L;

    /**
     * 开通学校总数量
     */
    @ApiModelProperty("开通学校总数量")
    private Integer totalSchoolCount;

    /**
     * 使用中（有效期内）的学校数量
     */
    @ApiModelProperty("使用中（有效期内）的学校数量")
    private Integer activeSchoolCount;

    /**
     * 来源{@link com.light.aiszzy.practiceBook.enums.PracticeBookSourceType}
     */
    @ApiModelProperty("来源：1学科网教辅，2凤凰教辅")
    private Integer practiceBookSourceType;
}