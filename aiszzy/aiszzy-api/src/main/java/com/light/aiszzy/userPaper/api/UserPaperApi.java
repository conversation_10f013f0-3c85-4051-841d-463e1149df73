package  com.light.aiszzy.userPaper.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.userPaper.entity.vo.UserPaperVo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 用户上传试卷接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface UserPaperApi  {

	/**
	 * 查询用户上传试卷列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/userPaper/pageList")
	@ApiOperation(value = "分页查询用户上传试卷",httpMethod = "POST")
	AjaxResult<PageInfo<UserPaperVo>> getUserPaperPageListByCondition(@RequestBody UserPaperConditionBo condition);

	/**
	 * 查询所有用户上传试卷列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/userPaper/list")
	@ApiOperation(value = "查询所有用户上传试卷",httpMethod = "POST")
	AjaxResult<List<UserPaperVo>> getUserPaperListByCondition(@RequestBody UserPaperConditionBo condition);


	/**
	 * 新增用户上传试卷
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/userPaper/add")
	@ApiOperation(value = "新增用户上传试卷",httpMethod = "POST")
	AjaxResult addUserPaper(@Validated @RequestBody UserPaperBo userPaperBo);

	/**
	 * 修改用户上传试卷
	 * @param userPaperBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/userPaper/update")
	@ApiOperation(value = "修改用户上传试卷",httpMethod = "POST")
	AjaxResult updateUserPaper(@Validated @RequestBody UserPaperBo userPaperBo);

	/**
	 * 查询用户上传试卷详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/userPaper/detail")
	@ApiOperation(value = "查询用户上传试卷详情",httpMethod = "GET")
	AjaxResult<UserPaperVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除用户上传试卷
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/userPaper/delete")
	@ApiOperation(value = "删除用户上传试卷",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);


	@GetMapping("/userPaper/updatePublishByOid")
	AjaxResult updatePublishByOid(@RequestParam("oid") String oid,@RequestParam("isPublish") Integer isPublish);


	@PostMapping("/userPaper/deleteByOidAndUserOid/{oid}/{userOid}")
	AjaxResult deleteByOidAndUserOid(@PathVariable("oid") String oid,@PathVariable("userOid") String userOid);
}

