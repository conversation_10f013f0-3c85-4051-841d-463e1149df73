package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkPageVo;
import com.light.aiszzy.homework.entity.bo.HomeworkPageBo;
import com.light.aiszzy.homework.entity.bo.HomeworkPageConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业每页题目坐标信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
public interface HomeworkPageApi  {

	/**
	 * 查询作业每页题目坐标信息列表
	 * <AUTHOR>
	 * @date 2025-07-17 19:40:04
	 */
	@PostMapping("/homeworkPage/pageList")
	@ApiOperation(value = "分页查询作业每页题目坐标信息",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkPageVo>> getHomeworkPagePageListByCondition(@RequestBody HomeworkPageConditionBo condition);

	/**
	 * 查询所有作业每页题目坐标信息列表
	 * <AUTHOR>
	 * @date 2025-07-17 19:40:04
	 */
	@PostMapping("/homeworkPage/list")
	@ApiOperation(value = "查询所有作业每页题目坐标信息",httpMethod = "POST")
	AjaxResult<List<HomeworkPageVo>> getHomeworkPageListByCondition(@RequestBody HomeworkPageConditionBo condition);


	/**
	 * 新增作业每页题目坐标信息
	 * <AUTHOR>
	 * @date 2025-07-17 19:40:04
	 */
	@PostMapping("/homeworkPage/add")
	@ApiOperation(value = "新增作业每页题目坐标信息",httpMethod = "POST")
	AjaxResult addHomeworkPage(@Validated @RequestBody HomeworkPageBo homeworkPageBo);

	/**
	 * 修改作业每页题目坐标信息
	 * @param homeworkPageBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-17 19:40:04
	 */
	@PostMapping("/homeworkPage/update")
	@ApiOperation(value = "修改作业每页题目坐标信息",httpMethod = "POST")
	AjaxResult updateHomeworkPage(@Validated @RequestBody HomeworkPageBo homeworkPageBo);

	/**
	 * 查询作业每页题目坐标信息详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-17 19:40:04
	 */
	@GetMapping("/homeworkPage/detail")
	@ApiOperation(value = "查询作业每页题目坐标信息详情",httpMethod = "GET")
	AjaxResult<HomeworkPageVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业每页题目坐标信息
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-17 19:40:04
	 */
	@GetMapping("/homeworkPage/delete")
	@ApiOperation(value = "删除作业每页题目坐标信息",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

