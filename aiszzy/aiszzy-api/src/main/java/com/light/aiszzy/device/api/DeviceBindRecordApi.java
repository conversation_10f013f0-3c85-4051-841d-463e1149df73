package com.light.aiszzy.device.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.device.entity.vo.DeviceBindRecordVo;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordBo;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 设备表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface DeviceBindRecordApi  {

	/**
	 * 查询设备表列表
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@PostMapping("/deviceBindRecord/pageList")
	@ApiOperation(value = "分页查询设备表",httpMethod = "POST")
	AjaxResult<PageInfo<DeviceBindRecordVo>> getDeviceBindRecordPageListByCondition(@RequestBody DeviceBindRecordConditionBo condition);

	/**
	 * 查询所有设备表列表
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@PostMapping("/deviceBindRecord/list")
	@ApiOperation(value = "查询所有设备表",httpMethod = "POST")
	AjaxResult<List<DeviceBindRecordVo>> getDeviceBindRecordListByCondition(@RequestBody DeviceBindRecordConditionBo condition);


	/**
	 * 新增设备表
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@PostMapping("/deviceBindRecord/add")
	@ApiOperation(value = "新增设备表",httpMethod = "POST")
	AjaxResult addDeviceBindRecord(@Validated @RequestBody DeviceBindRecordBo deviceBindRecordBo);

	/**
	 * 修改设备表
	 * @param deviceBindRecordBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@PostMapping("/deviceBindRecord/update")
	@ApiOperation(value = "修改设备表",httpMethod = "POST")
	AjaxResult updateDeviceBindRecord(@Validated @RequestBody DeviceBindRecordBo deviceBindRecordBo);

	/**
	 * 查询设备表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@GetMapping("/deviceBindRecord/detail")
	@ApiOperation(value = "查询设备表详情",httpMethod = "GET")
	AjaxResult<DeviceBindRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除设备表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-11 15:21:33
	 */
	@GetMapping("/deviceBindRecord/delete")
	@ApiOperation(value = "删除设备表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

