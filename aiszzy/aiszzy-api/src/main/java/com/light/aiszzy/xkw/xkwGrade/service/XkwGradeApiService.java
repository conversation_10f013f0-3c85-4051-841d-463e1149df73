package com.light.aiszzy.xkw.xkwGrade.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkw.xkwGrade.api.XkwGradeApi;
import com.light.aiszzy.xkw.xkwGrade.entity.bo.XkwGradeBo;
import com.light.aiszzy.xkw.xkwGrade.entity.bo.XkwGradeConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 年级接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@FeignClient(contextId = "xkwGradeApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwGradeApiService.XkwGradeApiFallbackFactory.class)
@Component
public interface XkwGradeApiService  extends XkwGradeApi {

	@Component
	class XkwGradeApiFallbackFactory implements FallbackFactory<XkwGradeApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwGradeApiService.XkwGradeApiFallbackFactory.class);
		@Override
		public XkwGradeApiService create(Throwable cause) {
			XkwGradeApiService.XkwGradeApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwGradeApiService() {
				public AjaxResult getXkwGradePageListByCondition(XkwGradeConditionBo condition){
					return AjaxResult.fail("年级查询失败");
				}
				public AjaxResult getXkwGradeListByCondition(XkwGradeConditionBo condition){
					return AjaxResult.fail("年级查询失败");
				}

				public AjaxResult addXkwGrade(XkwGradeBo xkwGradeBo){
					return AjaxResult.fail("年级新增失败");
				}

				public AjaxResult updateXkwGrade(XkwGradeBo xkwGradeBo){
					return AjaxResult.fail("年级更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("年级获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("年级删除失败");
				}
			};
		}
	}
}