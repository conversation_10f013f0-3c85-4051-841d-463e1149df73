package com.light.aiszzy.schoolBook.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookVo;
import com.light.aiszzy.schoolBook.entity.vo.PracticeBookWithSchoolVo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.BatchSchoolBookBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookWithSchoolCountVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookExportVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 教辅或作业本开通记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface SchoolBookApi {

    /**
     * 查询教辅或作业本开通记录表列表
     * 
     * <AUTHOR>
     * @date 2025-07-07 17:24:35
     */
    @PostMapping("/schoolBook/pageList")
    @ApiOperation(value = "分页查询教辅或作业本开通记录表", httpMethod = "POST")
    AjaxResult<PageInfo<SchoolBookVo>> getSchoolBookPageListByCondition(@RequestBody SchoolBookConditionBo condition);

    /**
     * 查询所有教辅或作业本开通记录表列表
     * 
     * <AUTHOR>
     * @date 2025-07-07 17:24:35
     */
    @PostMapping("/schoolBook/list")
    @ApiOperation(value = "查询所有教辅或作业本开通记录表", httpMethod = "POST")
    AjaxResult<List<SchoolBookVo>> getSchoolBookListByCondition(@RequestBody SchoolBookConditionBo condition);

    /**
     * 新增教辅或作业本开通记录表
     *
     * <AUTHOR>
     * @date 2025-07-07 17:24:35
     */
    @PostMapping("/schoolBook/add")
    @ApiOperation(value = "新增教辅或作业本开通记录表", httpMethod = "POST")
    AjaxResult addSchoolBook(@Validated @RequestBody SchoolBookBo schoolBookBo);

    /**
     * 批量开通教辅或作业本
     *
     * @param batchSchoolBookBo 批量开通请求对象
     * @return 批量开通结果
     * <AUTHOR>
     * @date 2025-07-18
     */
    @PostMapping("/schoolBook/batchAdd")
    @ApiOperation(value = "批量开通教辅或作业本", httpMethod = "POST")
    AjaxResult batchAddSchoolBook(@Validated @RequestBody BatchSchoolBookBo batchSchoolBookBo);

    /**
     * 修改教辅或作业本开通记录表
     * 
     * @param schoolBookBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-07 17:24:35
     */
    @PostMapping("/schoolBook/update")
    @ApiOperation(value = "修改教辅或作业本开通记录表", httpMethod = "POST")
    AjaxResult updateSchoolBook(@Validated @RequestBody SchoolBookBo schoolBookBo);

    /**
     * 查询教辅或作业本开通记录表详情
     * 
     * @param oid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-07 17:24:35
     */
    @GetMapping("/schoolBook/detail")
    @ApiOperation(value = "查询教辅或作业本开通记录表详情", httpMethod = "GET")
    AjaxResult<SchoolBookVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

    /**
     * 删除教辅或作业本开通记录表
     * 
     * @param oid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-07 17:24:35
     */
    @GetMapping("/schoolBook/delete")
    @ApiOperation(value = "删除教辅或作业本开通记录表", httpMethod = "GET")
    @ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
    AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

    /**
     * 教辅或作业开通列表：查询教辅列表并聚合学校开通数量信息
     *
     * @param condition 教辅查询条件
     * @return 包含学校开通数量信息的教辅分页列表
     * <AUTHOR>
     * @date 2025-07-12
     */
    @PostMapping("/schoolBook/practice-book-with-school-count")
    @ApiOperation(value = "教辅或作业开通列表", httpMethod = "POST")
    AjaxResult<PageInfo<PracticeBookWithSchoolCountVo>>
        getPracticeBookPageListWithSchoolCount(@RequestBody PracticeBookConditionBo condition);

    /**
     * 查询教辅列表与学校开通信息关联
     *
     * @param condition 查询条件，支持org_code和book_oid等条件
     * @return 教辅与学校关联信息分页列表
     * <AUTHOR>
     * @date 2025-07-19
     */
    @PostMapping("/schoolBook/practice-book-with-school-list")
    @ApiOperation(value = "查询教辅列表与学校开通信息关联", httpMethod = "POST")
    AjaxResult<PageInfo<PracticeBookWithSchoolVo>>
        getPracticeBookWithSchoolPageListByCondition(@RequestBody SchoolBookConditionBo condition);

    /**
     * 导出教辅开通列表
     *
     * @param condition 查询条件
     * @return 教辅开通列表导出数据
     * <AUTHOR>
     * @date 2025-07-24
     */
    @PostMapping("/schoolBook/export-practice-book-with-school-count")
    @ApiOperation(value = "导出教辅开通列表", httpMethod = "POST")
    AjaxResult<List<PracticeBookExportVo>>
        exportPracticeBookWithSchoolCount(@RequestBody PracticeBookConditionBo condition);

}
