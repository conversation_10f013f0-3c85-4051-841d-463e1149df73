package com.light.aiszzy.homework.api;


import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;


/**
 * 老师使用作业本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-18 14:20:30
 */
public interface HomeworkBookUserApi  {

	/**
	 * 查询老师使用作业本列表
	 * <AUTHOR>
	 * @date 2025-04-18 14:20:30
	 */
	@PostMapping("/homeworkBookUser/list")
	@ApiOperation(value = "分页查询老师使用作业本",httpMethod = "POST")
	AjaxResult getHomeworkBookUserListByCondition(@RequestBody HomeworkBookUserConditionBo condition);


	/**
	 * 新增老师使用作业本
	 * <AUTHOR>
	 * @date 2025-04-18 14:20:30
	 */
	@PostMapping("/homeworkBookUser/add")
	@ApiOperation(value = "新增老师使用作业本",httpMethod = "POST")
	AjaxResult addHomeworkBookUser(@Validated @RequestBody HomeworkBookUserBo homeworkBookUserBo);

	/**
	 * 修改老师使用作业本
	 * @param homeworkBookUserBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-04-18 14:20:30
	 */
	@PostMapping("/homeworkBookUser/update")
	@ApiOperation(value = "修改老师使用作业本",httpMethod = "POST")
	AjaxResult updateHomeworkBookUser(@Validated @RequestBody HomeworkBookUserBo homeworkBookUserBo);

	/**
	 * 查询老师使用作业本详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-04-18 14:20:30
	 */
	@GetMapping("/homeworkBookUser/detail")
	@ApiOperation(value = "查询老师使用作业本详情",httpMethod = "GET")
	AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除老师使用作业本
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-04-18 14:20:30
	 */
	@GetMapping("/homeworkBookUser/delete")
	@ApiOperation(value = "删除老师使用作业本",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	@GetMapping("/homeworkBookUser/deleteUse")
	@ApiOperation(value = "删除老师使用作业本",httpMethod = "GET")
	AjaxResult deleteUse(@NotNull(message = "请选择需要删除的数据") @RequestParam("homeworkBookOid") String homeworkBookOid,@RequestParam("userCode")String userCode,@RequestParam("orgCode")String orgCode);

}

