package com.light.aiszzy.homework.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 老师使用作业本
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-18 14:20:30
 */
@Data
public class HomeworkBookUserVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 作业本oid
     */
    private String homeworkBookOid;

    private String orgCode;

    /**
     * 是否是最后一条记录，1是，2不是
     */
    private Long isCurrent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除（1：正常 2：删除）
     */
    private Integer isDelete;

}
