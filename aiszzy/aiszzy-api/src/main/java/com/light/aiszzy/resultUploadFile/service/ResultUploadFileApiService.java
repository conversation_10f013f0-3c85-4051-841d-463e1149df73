package com.light.aiszzy.resultUploadFile.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.resultUploadFile.api.ResultUploadFileApi;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileBo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 扫描上传图片接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
@FeignClient(contextId = "resultUploadFileApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = ResultUploadFileApiService.ResultUploadFileApiFallbackFactory.class)
@Component
public interface ResultUploadFileApiService  extends ResultUploadFileApi {

	@Component
	class ResultUploadFileApiFallbackFactory implements FallbackFactory<ResultUploadFileApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(ResultUploadFileApiFallbackFactory.class);
		@Override
		public ResultUploadFileApiService create(Throwable cause) {
			ResultUploadFileApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new ResultUploadFileApiService() {
				public AjaxResult getResultUploadFilePageListByCondition(ResultUploadFileConditionBo condition){
					return AjaxResult.fail("扫描上传图片查询失败");
				}
				public AjaxResult getResultUploadFileListByCondition(ResultUploadFileConditionBo condition){
					return AjaxResult.fail("扫描上传图片查询失败");
				}

				public AjaxResult addResultUploadFile(ResultUploadFileBo resultUploadFileBo){
					return AjaxResult.fail("扫描上传图片新增失败");
				}

				public AjaxResult updateResultUploadFile(ResultUploadFileBo resultUploadFileBo){
					return AjaxResult.fail("扫描上传图片更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("扫描上传图片获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("扫描上传图片删除失败");
				}
			};
		}
	}
}