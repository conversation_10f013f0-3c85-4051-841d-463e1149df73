package com.light.aiszzy.xkw.xkwTextbook.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 教材
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwTextbookVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 	课程ID
     */
    private Long courseId;

    /**
     * 排序值
     */
    private Long ordinal;

    /**
     * 册别
     */
    private String volume;

    /**
     * 教材版本ID
     */
    private Long versionId;

    /**
     * 年级ID
     */
    private Long gradeId;

    /**
     * 学期,可用值:LAST,NEXT,ALL
     */
    private String term;

    /**
     * 教材名称
     */
    private String name;

}
