package com.light.aiszzy.userPaper.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.userPaper.api.UserPaperApi;
import com.light.aiszzy.userPaper.entity.bo.UserPaperBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 用户上传试卷接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "userPaperApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = UserPaperApiService.UserPaperApiFallbackFactory.class)
@Component
public interface UserPaperApiService  extends UserPaperApi {

	@Component
	class UserPaperApiFallbackFactory implements FallbackFactory<UserPaperApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(UserPaperApiService.UserPaperApiFallbackFactory.class);
		@Override
		public UserPaperApiService create(Throwable cause) {
			UserPaperApiService.UserPaperApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new UserPaperApiService() {
				public AjaxResult getUserPaperPageListByCondition(UserPaperConditionBo condition){
					return AjaxResult.fail("用户上传试卷查询失败");
				}
				public AjaxResult getUserPaperListByCondition(UserPaperConditionBo condition){
					return AjaxResult.fail("用户上传试卷查询失败");
				}

				public AjaxResult addUserPaper(UserPaperBo userPaperBo){
					return AjaxResult.fail("用户上传试卷新增失败");
				}

				public AjaxResult updateUserPaper(UserPaperBo userPaperBo){
					return AjaxResult.fail("用户上传试卷更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("用户上传试卷获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("用户上传试卷删除失败");
				}

				@Override
				public AjaxResult updatePublishByOid(String oid, Integer isPublish) {
					return AjaxResult.fail("更新发布状态");
				}

				@Override
				public AjaxResult deleteByOidAndUserOid(String oid, String userOid) {
					return AjaxResult.fail("删除失败");
				}
			};
		}
	}
}