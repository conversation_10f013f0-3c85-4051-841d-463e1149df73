package com.light.aiszzy.practiceBook.entity.vo;

import com.light.utils.TreeBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 教辅目录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class PracticeBookCatalogVo implements Serializable, TreeBean<PracticeBookCatalogVo> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每一条目录记录
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 父目录OID，表示层级关系，NULL表示根目录
     */
    private String parentOid;

    /**
     * 祖籍 OID 集合 多个逗号分割
     */
    private String superiorsOids;

    /**
     * 目录名称
     */
    private String name;

    /**
     * 关联的教辅OID
     */
    private String practiceBookOid;

    /**
     * 起始页
     */
    private Long pageStart;

    /**
     * 结束页
     */
    private Long pageEnd;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 子集
     */
    private List<PracticeBookCatalogVo> children;


    @Override
    public String getTreeKey() {
        return oid;
    }

    @Override
    public String getParentTreeKey() {
        return parentOid;
    }

    @Override
    public List<PracticeBookCatalogVo> getChildren() {
        return this.children;
    }

    @Override
    public void setChildren(List<PracticeBookCatalogVo> children) {
        this.children = children;
    }
}
