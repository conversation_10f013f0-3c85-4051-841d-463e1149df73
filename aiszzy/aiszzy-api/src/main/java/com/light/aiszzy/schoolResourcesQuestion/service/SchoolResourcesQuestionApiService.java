package com.light.aiszzy.schoolResourcesQuestion.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.schoolResourcesQuestion.api.SchoolResourcesQuestionApi;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 资源库题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "schoolResourcesQuestionApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = SchoolResourcesQuestionApiService.SchoolResourcesQuestionApiFallbackFactory.class)
@Component
public interface SchoolResourcesQuestionApiService  extends SchoolResourcesQuestionApi {

	@Component
	class SchoolResourcesQuestionApiFallbackFactory implements FallbackFactory<SchoolResourcesQuestionApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(SchoolResourcesQuestionApiService.SchoolResourcesQuestionApiFallbackFactory.class);
		@Override
		public SchoolResourcesQuestionApiService create(Throwable cause) {
			SchoolResourcesQuestionApiService.SchoolResourcesQuestionApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new SchoolResourcesQuestionApiService() {
				public AjaxResult getSchoolResourcesQuestionPageListByCondition(SchoolResourcesQuestionConditionBo condition){
					return AjaxResult.fail("资源库题目表查询失败");
				}
				public AjaxResult getSchoolResourcesQuestionListByCondition(SchoolResourcesQuestionConditionBo condition){
					return AjaxResult.fail("资源库题目表查询失败");
				}

				public AjaxResult addSchoolResourcesQuestion(SchoolResourcesQuestionBo schoolResourcesQuestionBo){
					return AjaxResult.fail("资源库题目表新增失败");
				}

				public AjaxResult updateSchoolResourcesQuestion(SchoolResourcesQuestionBo schoolResourcesQuestionBo){
					return AjaxResult.fail("资源库题目表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("资源库题目表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("资源库题目表删除失败");
				}
			};
		}
	}
}