package com.light.aiszzy.practiceBook.api;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionChangeBo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教辅题目表，用于存储题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface PracticeBookQuestionApi  {

	/**
	 * 查询教辅题目表，用于存储题目信息列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/pageList")
	@ApiOperation(value = "分页查询教辅题目表，用于存储题目信息",httpMethod = "POST")
	AjaxResult<PageInfo<PracticeBookQuestionVo>> getPracticeBookQuestionPageListByCondition(@RequestBody PracticeBookQuestionConditionBo condition);

	/**
	 * 查询所有教辅题目表，用于存储题目信息列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/list")
	@ApiOperation(value = "查询所有教辅题目表，用于存储题目信息",httpMethod = "POST")
	AjaxResult<List<PracticeBookQuestionVo>> getPracticeBookQuestionListByCondition(@RequestBody PracticeBookQuestionConditionBo condition);

	/**
	 * 查询所有教辅题目表，用于存储题目信息列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/listWithBody")
	@ApiOperation(value = "查询所有教辅题目表，用于存储题目信息",httpMethod = "POST")
	AjaxResult<List<PracticeBookQuestionVo>> getPracticeBookQuestionWithBodyListByCondition(@RequestBody PracticeBookQuestionConditionBo condition);


	/**
	 * 新增教辅题目表，用于存储题目信息
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/saveDataByPosition")
	@ApiOperation(value = "新增教辅题目表，用于存储题目信息",httpMethod = "POST")
	AjaxResult saveDataByPosition(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo);

	/**
	 * 修改教辅题目表，用于存储题目信息
	 * @param practiceBookQuestionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/update")
	@ApiOperation(value = "修改教辅题目表，用于存储题目信息",httpMethod = "POST")
	AjaxResult updatePracticeBookQuestion(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo);

	/**
	 * 查询教辅题目表，用于存储题目信息详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/practiceBookQuestion/detail")
	@ApiOperation(value = "查询教辅题目表，用于存储题目信息详情",httpMethod = "GET")
	AjaxResult<PracticeBookQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除教辅题目表，用于存储题目信息
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/practiceBookQuestion/delete")
	@ApiOperation(value = "删除教辅题目表，用于存储题目信息",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	/**
	 * 完成标注功能
	 * @param practiceBookQuestionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-09 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/mark-done")
	@ApiOperation(value = "完成标注",httpMethod = "POST")
	AjaxResult markPracticeBookQuestion(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo);

	/**
	 * 取消划题功能
	 * @param practiceBookQuestionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-11 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/cancel")
	@ApiOperation(value = "取消划题",httpMethod = "POST")
	AjaxResult cancelPracticeBookQuestion(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo);

	/**
	 * 查询相似题：根据题目oid、难度，查询出N个指定的学科网题目，实际是查询学科网相似题
	 * @param practiceBookQuestionChangeBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-11 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/query-similar-question")
	@ApiOperation(value = "查询相似题",httpMethod = "POST")
	AjaxResult querySimilarQuestion(@Validated @RequestBody PracticeBookQuestionChangeBo practiceBookQuestionChangeBo);

	/**
	 * 设为相似题：使用指定的JSON格式题目替换当前题目
	 * @param practiceBookQuestionChangeBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-12 17:24:33
	 */
	@PostMapping("/practiceBookQuestion/set-similar-question")
	@ApiOperation(value = "设为相似题",httpMethod = "POST")
	AjaxResult setSimilarQuestion(@Validated @RequestBody PracticeBookQuestionChangeBo practiceBookQuestionChangeBo);


	/**
	 * 根据 OID 获取题目信息，如没有题目信息，进行（学科网|好未来）查询 题目数据进行  question_content_json 数据保存,并在 resource_question表中新增
	 *
	 * @param oid the practice book question oid 教辅题目 OID
	 * @param bo the practice book question bo 初始化的相关参数:{ oid, position, positionImgBase64}
	 * @return {@link AjaxResult }<{@link PracticeBookQuestionVo }>
	 */
	@PostMapping("/practiceBookQuestion/getOrInitQuestionInfoByOid/{oid}")
	AjaxResult<PracticeBookQuestionVo> getOrInitQuestionInfoByOid(@PathVariable("oid") String oid, @RequestBody PracticeBookQuestionBo bo);


	/**
	 *
	 * @param bo
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/practiceBook/reFetchQuestionInfoByOid")
	AjaxResult reFetchQuestionInfoByOid(@RequestBody PracticeBookQuestionBo bo);
}

