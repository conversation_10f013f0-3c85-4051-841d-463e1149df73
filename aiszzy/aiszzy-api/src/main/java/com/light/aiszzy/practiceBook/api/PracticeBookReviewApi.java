package com.light.aiszzy.practiceBook.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookReviewVo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教辅信息审核接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface PracticeBookReviewApi  {

	/**
	 * 查询教辅信息审核列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/practiceBookReview/pageList")
	@ApiOperation(value = "分页查询教辅信息审核",httpMethod = "POST")
	AjaxResult<PageInfo<PracticeBookReviewVo>> getPracticeBookReviewPageListByCondition(@RequestBody PracticeBookReviewConditionBo condition);

	/**
	 * 查询所有教辅信息审核列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/practiceBookReview/list")
	@ApiOperation(value = "查询所有教辅信息审核",httpMethod = "POST")
	AjaxResult<List<PracticeBookReviewVo>> getPracticeBookReviewListByCondition(@RequestBody PracticeBookReviewConditionBo condition);


	/**
	 * 新增教辅信息审核
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/practiceBookReview/add")
	@ApiOperation(value = "新增教辅信息审核",httpMethod = "POST")
	AjaxResult addPracticeBookReview(@Validated @RequestBody PracticeBookReviewBo practiceBookReviewBo);

	/**
	 * 修改教辅信息审核
	 * @param practiceBookReviewBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/practiceBookReview/update")
	@ApiOperation(value = "修改教辅信息审核",httpMethod = "POST")
	AjaxResult updatePracticeBookReview(@Validated @RequestBody PracticeBookReviewBo practiceBookReviewBo);

	/**
	 * 查询教辅信息审核详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/practiceBookReview/detail")
	@ApiOperation(value = "查询教辅信息审核详情",httpMethod = "GET")
	AjaxResult<PracticeBookReviewVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除教辅信息审核
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/practiceBookReview/delete")
	@ApiOperation(value = "删除教辅信息审核",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

