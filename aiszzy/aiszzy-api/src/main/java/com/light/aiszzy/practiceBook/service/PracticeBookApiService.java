package com.light.aiszzy.practiceBook.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.practiceBook.api.PracticeBookApi;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 教辅信息表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "practiceBookApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = PracticeBookApiService.PracticeBookApiFallbackFactory.class)
@Component
public interface PracticeBookApiService  extends PracticeBookApi {

	@Component
	class PracticeBookApiFallbackFactory implements FallbackFactory<PracticeBookApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(PracticeBookApiService.PracticeBookApiFallbackFactory.class);
		@Override
		public PracticeBookApiService create(Throwable cause) {
			PracticeBookApiService.PracticeBookApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new PracticeBookApiService() {
				public AjaxResult getPracticeBookPageListByCondition(PracticeBookConditionBo condition){
					return AjaxResult.fail("教辅信息表查询失败");
				}
				public AjaxResult getPracticeBookListByCondition(PracticeBookConditionBo condition){
					return AjaxResult.fail("教辅信息表查询失败");
				}

				public AjaxResult addPracticeBook(PracticeBookBo practiceBookBo){
					return AjaxResult.fail("教辅信息表新增失败");
				}

				public AjaxResult updatePracticeBook(PracticeBookBo practiceBookBo){
					return AjaxResult.fail("教辅信息表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("教辅信息表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("教辅信息表删除失败");
				}

				@Override
				public AjaxResult updateCatalogPathByOid(PracticeBookBo practiceBookBo) {
					return AjaxResult.fail("更新失败");
				}

				@Override
				public AjaxResult<Void> updateFileInfoByOid(PracticeBookBo practiceBookBo) {
					return AjaxResult.fail("更新失败");
				}

				@Override
				public AjaxResult<Void> updateQuestionNumInfoByOid(PracticeBookBo practiceBookBo) {
					return AjaxResult.fail("更新失败");
				}

				@Override
				public AjaxResult updateIsHighShotsByOid(PracticeBookBo practiceBookBo) {
					return AjaxResult.fail("更新失败");
				}

				@Override
				public AjaxResult commit(PracticeBookBo practiceBookBo) {
					return AjaxResult.fail("提交失败");
				}

				@Override
				public AjaxResult review(PracticeBookBo practiceBookBo) {
					return AjaxResult.fail("审核失败");
				}

				@Override
				public AjaxResult changeStatus(PracticeBookBo practiceBookBo) {
					return AjaxResult.fail("更改状态");
				}
			};
		}
	}
}