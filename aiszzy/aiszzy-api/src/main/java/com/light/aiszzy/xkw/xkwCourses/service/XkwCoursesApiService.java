package com.light.aiszzy.xkw.xkwCourses.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkw.xkwCourses.api.XkwCoursesApi;
import com.light.aiszzy.xkw.xkwCourses.entity.bo.XkwCoursesBo;
import com.light.aiszzy.xkw.xkwCourses.entity.bo.XkwCoursesConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@FeignClient(contextId = "xkwCoursesApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwCoursesApiService.XkwCoursesApiFallbackFactory.class)
@Component
public interface XkwCoursesApiService  extends XkwCoursesApi {

	@Component
	class XkwCoursesApiFallbackFactory implements FallbackFactory<XkwCoursesApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwCoursesApiService.XkwCoursesApiFallbackFactory.class);
		@Override
		public XkwCoursesApiService create(Throwable cause) {
			XkwCoursesApiService.XkwCoursesApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwCoursesApiService() {
				public AjaxResult getXkwCoursesPageListByCondition(XkwCoursesConditionBo condition){
					return AjaxResult.fail("课程查询失败");
				}
				public AjaxResult getXkwCoursesListByCondition(XkwCoursesConditionBo condition){
					return AjaxResult.fail("课程查询失败");
				}

				public AjaxResult addXkwCourses(XkwCoursesBo xkwCoursesBo){
					return AjaxResult.fail("课程新增失败");
				}

				public AjaxResult updateXkwCourses(XkwCoursesBo xkwCoursesBo){
					return AjaxResult.fail("课程更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("课程获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("课程删除失败");
				}
			};
		}
	}
}