package com.light.aiszzy.homework.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 作业本映射印送记录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class HomeworkBookOptionRecordVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 作业本id
     */
    private String bookOid;

    /**
     * 记录描述
     */
    private String comment;

    /**
     * 状态 去1待印刷，2印刷中，3已经配送
     */
    private Integer status;

    /**
     * 配送时间
     */
    private Date deliveryTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
