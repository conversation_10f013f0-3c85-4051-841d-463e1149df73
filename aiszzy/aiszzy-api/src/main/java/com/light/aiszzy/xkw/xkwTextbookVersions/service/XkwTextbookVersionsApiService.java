package com.light.aiszzy.xkw.xkwTextbookVersions.service;


import com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkw.xkwTextbookVersions.api.XkwTextbookVersionsApi;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsBo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 教材版本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "xkwTextbookVersionsApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwTextbookVersionsApiService.XkwTextbookVersionsApiFallbackFactory.class)
@Component
public interface XkwTextbookVersionsApiService  extends XkwTextbookVersionsApi {

	@Component
	class XkwTextbookVersionsApiFallbackFactory implements FallbackFactory<XkwTextbookVersionsApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwTextbookVersionsApiService.XkwTextbookVersionsApiFallbackFactory.class);
		@Override
		public XkwTextbookVersionsApiService create(Throwable cause) {
			XkwTextbookVersionsApiService.XkwTextbookVersionsApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwTextbookVersionsApiService() {
				public AjaxResult getXkwTextbookVersionsPageListByCondition(XkwTextbookVersionsConditionBo condition){
					return AjaxResult.fail("教材版本查询失败");
				}
				public AjaxResult getXkwTextbookVersionsListByCondition(XkwTextbookVersionsConditionBo condition){
					return AjaxResult.fail("教材版本查询失败");
				}

				@Override
				public AjaxResult<List<XkwTextbookVersionsVo>> getByStageAndSubject(Integer stage, String subject) {
					return AjaxResult.fail("查询失败");
				}
			};
		}
	}
}