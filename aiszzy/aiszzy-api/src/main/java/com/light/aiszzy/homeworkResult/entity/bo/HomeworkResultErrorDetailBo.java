package com.light.aiszzy.homeworkResult.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 校本作业结果详情表(错误试题)
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class HomeworkResultErrorDetailBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 校本作业结果详情表(错误试题)id
	 */
	@ApiModelProperty("校本作业结果详情表(错误试题)id")
	private Long homeworkResultErrorDetailId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 作业id
	 */
	@ApiModelProperty("作业id")
	private String homeworkOid;
	/**
	 * 作业结果id
	 */
	@ApiModelProperty("作业结果id")
	private String homeworkResultOid;
	/**
	 * 学校code
	 */
	@ApiModelProperty("学校code")
	private String orgCode;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;
	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classId;
	/**
	 * 学生oid
	 */
	@ApiModelProperty("学生oid")
	private String stuOid;
	/**
	 * 学生错题url
	 */
	@ApiModelProperty("学生错题url")
	private String stuUrl;
	/**
	 * 页码
	 */
	@ApiModelProperty("页码")
	private Long stuPageNo;
	/**
	 * 关联题目oid
	 */
	@ApiModelProperty("关联题目oid")
	private String questionOid;
	/**
	 * 题号
	 */
	@ApiModelProperty("题号")
	private String questionNum;
	/**
	 * 大题号
	 */
	@ApiModelProperty("大题号")
	private String bigNum;
	/**
	 * 小题号
	 */
	@ApiModelProperty("小题号")
	private String smallNum;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
