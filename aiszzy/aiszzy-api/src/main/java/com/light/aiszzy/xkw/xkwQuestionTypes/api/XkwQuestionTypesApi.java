package com.light.aiszzy.xkw.xkwQuestionTypes.api;

import com.light.aiszzy.xkw.xkwQuestionTypes.entity.vo.XkwQuestionTypesVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * 试题类型接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface XkwQuestionTypesApi  {


	/**
	 * 根据年级学科获取题型
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/xkwQuestionTypes/getByGradeAndSubject")
	@ApiOperation(value = "根据年级学科获取题型")
	AjaxResult<List<XkwQuestionTypesVo>> getByGradeAndSubject(@RequestParam("grade") Integer grade,@RequestParam("subject")  String subject);

	@GetMapping("/xkwQuestionTypes/getByStageAndSubject")
	AjaxResult<List<XkwQuestionTypesVo>> getByStageAndSubject(@RequestParam("stage") Integer stage, @RequestParam("subject") String subject);
}

