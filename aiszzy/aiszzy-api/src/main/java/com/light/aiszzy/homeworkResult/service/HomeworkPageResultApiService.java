package com.light.aiszzy.homeworkResult.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homeworkResult.api.HomeworkPageResultApi;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 扫描结构每页处理结果
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@FeignClient(contextId = "homeworkPageResultApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkPageResultApiService.HomeworkPageResultApiFallbackFactory.class)
@Component
public interface HomeworkPageResultApiService  extends HomeworkPageResultApi {

	@Component
	class HomeworkPageResultApiFallbackFactory implements FallbackFactory<HomeworkPageResultApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkPageResultApiFallbackFactory.class);
		@Override
		public HomeworkPageResultApiService create(Throwable cause) {
			HomeworkPageResultApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkPageResultApiService() {
				public AjaxResult getHomeworkPageResultPageListByCondition(HomeworkPageResultConditionBo condition){
					return AjaxResult.fail("扫描结构每页处理结果查询失败");
				}
				public AjaxResult getHomeworkPageResultListByCondition(HomeworkPageResultConditionBo condition){
					return AjaxResult.fail("扫描结构每页处理结果查询失败");
				}

				@Override
				public AjaxResult dealNoStudent(HomeworkPageResultBo bo) {
					return AjaxResult.fail("处理学生未匹配失败");
				}

				@Override
				public AjaxResult dealRepeat(HomeworkPageResultBo bo) {
					return AjaxResult.fail("处理学生重复失败");
				}

				public AjaxResult addHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo){
					return AjaxResult.fail("扫描结构每页处理结果新增失败");
				}

				public AjaxResult updateHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo){
					return AjaxResult.fail("扫描结构每页处理结果更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("扫描结构每页处理结果获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("扫描结构每页处理结果删除失败");
				}
			};
		}
	}
}