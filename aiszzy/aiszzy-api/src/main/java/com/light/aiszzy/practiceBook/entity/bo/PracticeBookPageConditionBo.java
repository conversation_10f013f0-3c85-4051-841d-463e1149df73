package com.light.aiszzy.practiceBook.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 教辅目录每页图片记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class PracticeBookPageConditionBo extends PageLimitBo{

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条目录记录")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	private String practiceBookCode;

	/**
	 * 教辅OID
	 */
	@ApiModelProperty("教辅OID")
	private String practiceBookOid;

	/**
	 * 页码
	 */
	@ApiModelProperty("页码")
	private Long pageNoSearch;

	/**
	 * 图片地址
	 */
	@ApiModelProperty("图片地址")
	private String imageUrl;

	/**
	 * 修改后框题个数（实际）
	 */
	@ApiModelProperty("修改后框题个数（实际）")
	private Long questionNum;

	/**
	 * 解析框题个数
	 */
	@ApiModelProperty("解析框题个数")
	private Long analysisQuestionNum;

	/**
	 * 解析结果
	 */
	@ApiModelProperty("解析结果")
	private String analysisJson;

	/**
	 * 题目信息，题号，题目oid，坐标等
	 */
	@ApiModelProperty("题目信息，题号，题目oid，坐标等")
	private String questionJson;
	/**
	 * 整页标注是否完成  0：未完成 1：完成
	 */
	@ApiModelProperty("整页标注是否完成  0：未完成 1：完成")
	private Long status;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
