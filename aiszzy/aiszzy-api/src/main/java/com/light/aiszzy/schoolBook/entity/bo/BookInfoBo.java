package com.light.aiszzy.schoolBook.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 教辅信息业务对象
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
public class BookInfoBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 教辅OID或作业本OID
     */
    @ApiModelProperty(value = "教辅OID或作业本OID", required = true)
    @NotBlank(message = "教辅OID不能为空")
    private String bookOid;

    /**
     * 教辅类型，暂时没有，后面可能有
     */
    @ApiModelProperty(value = "教辅或作业本类型")
    private String bookType;
}
