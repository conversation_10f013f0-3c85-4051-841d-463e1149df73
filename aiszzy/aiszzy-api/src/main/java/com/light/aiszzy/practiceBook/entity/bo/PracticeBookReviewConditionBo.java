package com.light.aiszzy.practiceBook.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 教辅信息审核
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class PracticeBookReviewConditionBo extends PageLimitBo{

	/**
	 * 自增主键，唯一标识每一条记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条记录")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 关联的教辅OID
	 */
	@ApiModelProperty("关联的教辅OID")
	private String practiceBookOid;

	/**
	 * 审核意见
	 */
	@ApiModelProperty("审核意见")
	private String comment;

	/**
	 * 审核状态，1未提交，2审核中 3审核成功 4打回
	 */
	@ApiModelProperty("审核状态，1未提交，2审核中 3审核成功 4打回")
	private Long status;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
