package com.light.aiszzy.homeworkResult.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homeworkResult.api.HomeworkResultErrorDetailApi;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本作业结果详情表(错误试题)接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "homeworkResultErrorDetailApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkResultErrorDetailApiService.HomeworkResultErrorDetailApiFallbackFactory.class)
@Component
public interface HomeworkResultErrorDetailApiService  extends HomeworkResultErrorDetailApi {

	@Component
	class HomeworkResultErrorDetailApiFallbackFactory implements FallbackFactory<HomeworkResultErrorDetailApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkResultErrorDetailApiService.HomeworkResultErrorDetailApiFallbackFactory.class);
		@Override
		public HomeworkResultErrorDetailApiService create(Throwable cause) {
			HomeworkResultErrorDetailApiService.HomeworkResultErrorDetailApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkResultErrorDetailApiService() {
				public AjaxResult getHomeworkResultErrorDetailPageListByCondition(HomeworkResultErrorDetailConditionBo condition){
					return AjaxResult.fail("校本作业结果详情表(错误试题)查询失败");
				}
				public AjaxResult getHomeworkResultErrorDetailListByCondition(HomeworkResultErrorDetailConditionBo condition){
					return AjaxResult.fail("校本作业结果详情表(错误试题)查询失败");
				}

				public AjaxResult addHomeworkResultErrorDetail(HomeworkResultErrorDetailBo homeworkResultErrorDetailBo){
					return AjaxResult.fail("校本作业结果详情表(错误试题)新增失败");
				}

				public AjaxResult updateHomeworkResultErrorDetail(HomeworkResultErrorDetailBo homeworkResultErrorDetailBo){
					return AjaxResult.fail("校本作业结果详情表(错误试题)更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("校本作业结果详情表(错误试题)获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("校本作业结果详情表(错误试题)删除失败");
				}
			};
		}
	}
}