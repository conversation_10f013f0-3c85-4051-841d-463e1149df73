package com.light.aiszzy.xkw.xkwQuestionDifficulties.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.vo.XkwQuestionDifficultiesVo;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.bo.XkwQuestionDifficultiesBo;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.bo.XkwQuestionDifficultiesConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 试题难度等级接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface XkwQuestionDifficultiesApi  {


	@GetMapping("/xkwQuestionDifficulties/findAll")
    AjaxResult<List<XkwQuestionDifficultiesVo>> findAll();
}

