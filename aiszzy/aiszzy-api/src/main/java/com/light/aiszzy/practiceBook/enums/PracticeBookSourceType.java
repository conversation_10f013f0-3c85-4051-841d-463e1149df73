package com.light.aiszzy.practiceBook.enums;

/**
 * 教辅来源类型枚举
 */
public enum PracticeBookSourceType {
    
    /**
     * 学科网教辅
     */
    XKW(1, "学科网教辅"),
    
    /**
     * 凤凰教辅
     */
    FENGHUANG(2, "凤凰教辅");
    
    private final Integer code;
    private final String name;
    
    PracticeBookSourceType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据学科网ID判断教辅来源类型
     * 
     * @param xkwZsId 学科网智书ID
     * @return 教辅来源类型
     */
    public static PracticeBookSourceType getByXkwZsId(String xkwZsId) {
        if (xkwZsId == null || xkwZsId.trim().isEmpty()) {
            return FENGHUANG;
        } else {
            return XKW;
        }
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举对象
     */
    public static PracticeBookSourceType getByCode(Integer code) {
        for (PracticeBookSourceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}