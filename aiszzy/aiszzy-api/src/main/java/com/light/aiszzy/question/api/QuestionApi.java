package  com.light.aiszzy.question.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.question.entity.bo.QuestionConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface QuestionApi  {

	/**
	 * 查询题目表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/question/pageList")
	@ApiOperation(value = "分页查询题目表",httpMethod = "POST")
	AjaxResult<PageInfo<QuestionVo>> getQuestionPageListByCondition(@RequestBody QuestionConditionBo condition);

	/**
	 * 查询所有题目表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/question/list")
	@ApiOperation(value = "查询所有题目表",httpMethod = "POST")
	AjaxResult<List<QuestionVo>> getQuestionListByCondition(@RequestBody QuestionConditionBo condition);


	/**
	 * 新增题目表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/question/add")
	@ApiOperation(value = "新增题目表",httpMethod = "POST")
	AjaxResult addQuestion(@Validated @RequestBody QuestionBo questionBo);

	/**
	 * 修改题目表
	 * @param questionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/question/update")
	@ApiOperation(value = "修改题目表",httpMethod = "POST")
	AjaxResult updateQuestion(@Validated @RequestBody QuestionBo questionBo);

	/**
	 * 查询题目表详情
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/question/detail")
	@ApiOperation(value = "查询题目表详情",httpMethod = "GET")
	AjaxResult<QuestionVo> getDetail(@RequestParam("questionOid") String questionOid,@RequestParam(name = "practiceBookOid",required = false) String practiceBookOid);

	/**
	 * 删除题目表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/question/delete")
	@ApiOperation(value = "删除题目表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

