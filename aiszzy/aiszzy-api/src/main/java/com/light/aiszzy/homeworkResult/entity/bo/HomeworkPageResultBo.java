package com.light.aiszzy.homeworkResult.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 扫描结构每页处理结果
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@Data
public class HomeworkPageResultBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 扫描结构每页处理结果id
	 */
	@ApiModelProperty("扫描结构每页处理结果id")
	private Long homeworkPageResultId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 作业id
	 */
	@ApiModelProperty("作业id")
	private String homeworkOid;
	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private String orgCode;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;
	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private String classId;
	/**
	 * 学生oid
	 */
	@ApiModelProperty("学生oid")
	private String stuOid;
	/**
	 * 学生名字
	 */
	@ApiModelProperty("学生名字")
	private String ocrStuName;
	/**
	 * 学生学号
	 */
	@ApiModelProperty("学生学号")
	private String ocrStuNo;
	/**
	 * 页码,正面二维码的页码
	 */
	@ApiModelProperty("页码,正面二维码的页码")
	private Integer pageNo;
	/**
	 * 正面图片地址
	 */
	@ApiModelProperty("正面图片地址")
	private String stuAnswerUrlOne;
	/**
	 * 反面图片地址
	 */
	@ApiModelProperty("反面图片地址")
	private String stuAnswerUrlTwo;
	/**
	 * 作业答案表oid，错题时使用
	 */
	@ApiModelProperty("作业答案表oid，错题时使用")
	private String homeworkResultOid;
	/**
	 * 1学号没有，2重复学号，3作业页数不全，4题目疑问
	 */
	@ApiModelProperty("1学号没有，2重复学号，3作业页数不全，4题目疑问")
	private Integer doubtType;
	/**
	 * 是否有疑问 0：无，1：存在 2已经处理
	 */
	@ApiModelProperty("是否有疑问 0：无，1：存在 2已经处理")
	private Integer isDoubt;
	/**
	 * 智批结果打印合并学生作答和智批结果的地址
	 */
	@ApiModelProperty("智批结果打印合并学生作答和智批结果的地址")
	private String stuAnswerCorrectResult;
	/**
	 * 智批结果（白底红色对错图片地址）用于打印
	 */
	@ApiModelProperty("智批结果（白底红色对错图片地址）用于打印")
	private String stuAnswerCorrectResultPrint;
	/**
	 * 学生作答图片信息json存储
	 */
	@ApiModelProperty("学生作答图片信息json存储")
	private String stuAnswerPageInfoJson;
	/**
	 * 本页（含正反页）答对题目个数
	 */
	@ApiModelProperty("本页（含正反页）答对题目个数")
	private Integer rightNum;
	/**
	 * 本页（含正反页）答错题目个数
	 */
	@ApiModelProperty("本页（含正反页）答错题目个数")
	private Integer wrongNum;
	/**
	 * 本页（含正反页）未知是否正确题目个数
	 */
	@ApiModelProperty("本页（含正反页）未知是否正确题目个数")
	private Integer unknownNum;
	/**
	 * 本页（含正反页）题目总个数
	 */
	@ApiModelProperty("本页（含正反页）题目总个数")
	private Integer totalNum;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
