package com.light.aiszzy.xkw.xkwQuestionTypes.service;


import com.light.aiszzy.xkw.xkwQuestionTypes.entity.vo.XkwQuestionTypesVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkw.xkwQuestionTypes.api.XkwQuestionTypesApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 试题类型接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "xkwQuestionTypesApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwQuestionTypesApiService.XkwQuestionTypesApiFallbackFactory.class)
@Component
public interface XkwQuestionTypesApiService  extends XkwQuestionTypesApi {

	@Component
	class XkwQuestionTypesApiFallbackFactory implements FallbackFactory<XkwQuestionTypesApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwQuestionTypesApiService.XkwQuestionTypesApiFallbackFactory.class);
		@Override
		public XkwQuestionTypesApiService create(Throwable cause) {
			XkwQuestionTypesApiService.XkwQuestionTypesApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwQuestionTypesApiService() {
				@Override
				public AjaxResult<List<XkwQuestionTypesVo>> getByGradeAndSubject(Integer grade, String subject) {
					return AjaxResult.fail("获取失败");
				}

				@Override
				public AjaxResult<List<XkwQuestionTypesVo>> getByStageAndSubject(Integer stage, String subject) {
					return AjaxResult.fail("获取失败");
				}
			};
		}
	}
}