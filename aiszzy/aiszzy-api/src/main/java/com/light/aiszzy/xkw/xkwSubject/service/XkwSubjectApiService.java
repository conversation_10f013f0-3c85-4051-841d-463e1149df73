package com.light.aiszzy.xkw.xkwSubject.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkw.xkwSubject.api.XkwSubjectApi;
import com.light.aiszzy.xkw.xkwSubject.entity.bo.XkwSubjectBo;
import com.light.aiszzy.xkw.xkwSubject.entity.bo.XkwSubjectConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 学科接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "xkwSubjectApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwSubjectApiService.XkwSubjectApiFallbackFactory.class)
@Component
public interface XkwSubjectApiService  extends XkwSubjectApi {

	@Component
	class XkwSubjectApiFallbackFactory implements FallbackFactory<XkwSubjectApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwSubjectApiService.XkwSubjectApiFallbackFactory.class);
		@Override
		public XkwSubjectApiService create(Throwable cause) {
			XkwSubjectApiService.XkwSubjectApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwSubjectApiService() {
				public AjaxResult getXkwSubjectPageListByCondition(XkwSubjectConditionBo condition){
					return AjaxResult.fail("学科查询失败");
				}
				public AjaxResult getXkwSubjectListByCondition(XkwSubjectConditionBo condition){
					return AjaxResult.fail("学科查询失败");
				}

				public AjaxResult addXkwSubject(XkwSubjectBo xkwSubjectBo){
					return AjaxResult.fail("学科新增失败");
				}

				public AjaxResult updateXkwSubject(XkwSubjectBo xkwSubjectBo){
					return AjaxResult.fail("学科更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("学科获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("学科删除失败");
				}
			};
		}
	}
}