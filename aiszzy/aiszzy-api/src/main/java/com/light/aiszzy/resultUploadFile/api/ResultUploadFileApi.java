package com.light.aiszzy.resultUploadFile.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileBo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 扫描上传图片接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
public interface ResultUploadFileApi  {

	/**
	 * 查询扫描上传图片列表
	 * <AUTHOR>
	 * @date 2025-07-19 14:10:33
	 */
	@PostMapping("/resultUploadFile/pageList")
	@ApiOperation(value = "分页查询扫描上传图片",httpMethod = "POST")
	AjaxResult<PageInfo<ResultUploadFileVo>> getResultUploadFilePageListByCondition(@RequestBody ResultUploadFileConditionBo condition);

	/**
	 * 查询所有扫描上传图片列表
	 * <AUTHOR>
	 * @date 2025-07-19 14:10:33
	 */
	@PostMapping("/resultUploadFile/list")
	@ApiOperation(value = "查询所有扫描上传图片",httpMethod = "POST")
	AjaxResult<List<ResultUploadFileVo>> getResultUploadFileListByCondition(@RequestBody ResultUploadFileConditionBo condition);


	/**
	 * 新增扫描上传图片
	 * <AUTHOR>
	 * @date 2025-07-19 14:10:33
	 */
	@PostMapping("/resultUploadFile/add")
	@ApiOperation(value = "新增扫描上传图片",httpMethod = "POST")
	AjaxResult addResultUploadFile(@Validated @RequestBody ResultUploadFileBo resultUploadFileBo);

	/**
	 * 修改扫描上传图片
	 * @param resultUploadFileBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-19 14:10:33
	 */
	@PostMapping("/resultUploadFile/update")
	@ApiOperation(value = "修改扫描上传图片",httpMethod = "POST")
	AjaxResult updateResultUploadFile(@Validated @RequestBody ResultUploadFileBo resultUploadFileBo);

	/**
	 * 查询扫描上传图片详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-19 14:10:33
	 */
	@GetMapping("/resultUploadFile/detail")
	@ApiOperation(value = "查询扫描上传图片详情",httpMethod = "GET")
	AjaxResult<ResultUploadFileVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除扫描上传图片
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-19 14:10:33
	 */
	@GetMapping("/resultUploadFile/delete")
	@ApiOperation(value = "删除扫描上传图片",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

