package com.light.aiszzy.apiRequestLog.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 第三方请求日志表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class ApiRequestLogBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 第三方请求日志表id
	 */
	@ApiModelProperty("第三方请求日志表id")
	private Long apiRequestLogId;
	
	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条目录记录")
	private Long id;
	/**
	 * 请求地址
	 */
	@ApiModelProperty("请求地址")
	private String url;
	/**
	 * 请求方式 POST GET PUT
	 */
	@ApiModelProperty("请求方式 POST GET PUT")
	private String method;
	/**
	 * 入参
	 */
	@ApiModelProperty("入参")
	private String params;
	/**
	 * 响应
	 */
	@ApiModelProperty("响应")
	private String response;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
