package com.light.aiszzy.xkw.xkwQuestionDifficulties.service;


import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.vo.XkwQuestionDifficultiesVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.api.XkwQuestionDifficultiesApi;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.bo.XkwQuestionDifficultiesBo;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.bo.XkwQuestionDifficultiesConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 试题难度等级接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "xkwQuestionDifficultiesApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwQuestionDifficultiesApiService.XkwQuestionDifficultiesApiFallbackFactory.class)
@Component
public interface XkwQuestionDifficultiesApiService  extends XkwQuestionDifficultiesApi {

	@Component
	class XkwQuestionDifficultiesApiFallbackFactory implements FallbackFactory<XkwQuestionDifficultiesApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwQuestionDifficultiesApiService.XkwQuestionDifficultiesApiFallbackFactory.class);
		@Override
		public XkwQuestionDifficultiesApiService create(Throwable cause) {
			XkwQuestionDifficultiesApiService.XkwQuestionDifficultiesApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwQuestionDifficultiesApiService() {
				@Override
				public AjaxResult<List<XkwQuestionDifficultiesVo>> findAll() {
					return AjaxResult.fail("获取失败");
				}
			};
		}
	}
}