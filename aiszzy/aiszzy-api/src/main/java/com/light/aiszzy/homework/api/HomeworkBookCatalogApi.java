package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogVo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业本目录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkBookCatalogApi  {

	/**
	 * 查询作业本目录列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalog/pageList")
	@ApiOperation(value = "分页查询作业本目录",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkBookCatalogVo>> getHomeworkBookCatalogPageListByCondition(@RequestBody HomeworkBookCatalogConditionBo condition);

	/**
	 * 查询作业本目录子目录和作业
	 * <AUTHOR>
	 * @date 2025-03-28 16:01:05
	 */
	@PostMapping("/homeworkBookCatalog/listAllChild")
	@ApiOperation(value = "查询作业本目录子目录和作业",httpMethod = "POST")
	AjaxResult listAllChild(@RequestBody HomeworkBookCatalogConditionBo condition);

	@PostMapping("/homeworkBookCatalog/listAllChildByBook")
	@ApiOperation(value = "根据作业本查所有目录和作业",httpMethod = "POST")
	AjaxResult listAllChildByBook(@RequestBody HomeworkBookCatalogConditionBo condition);

	@PostMapping("/homeworkBookCatalog/sort")
	@ApiOperation(value = "排序目录",httpMethod = "POST")
	AjaxResult sortHomeworkBookCatalog(@RequestBody HomeworkBookCatalogBo condition);


	/**
	 * 查询所有作业本目录列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalog/list")
	@ApiOperation(value = "查询所有作业本目录",httpMethod = "POST")
	AjaxResult<List<HomeworkBookCatalogVo>> getHomeworkBookCatalogListByCondition(@RequestBody HomeworkBookCatalogConditionBo condition);


	/**
	 * 新增作业本目录
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalog/add")
	@ApiOperation(value = "新增作业本目录",httpMethod = "POST")
	AjaxResult addHomeworkBookCatalog(@Validated @RequestBody HomeworkBookCatalogBo homeworkBookCatalogBo);

	/**
	 * 修改作业本目录
	 * @param homeworkBookCatalogBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalog/update")
	@ApiOperation(value = "修改作业本目录",httpMethod = "POST")
	AjaxResult updateHomeworkBookCatalog(@Validated @RequestBody HomeworkBookCatalogBo homeworkBookCatalogBo);

	/**
	 * 查询作业本目录详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkBookCatalog/detail")
	@ApiOperation(value = "查询作业本目录详情",httpMethod = "GET")
	AjaxResult<HomeworkBookCatalogVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业本目录
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkBookCatalog/delete")
	@ApiOperation(value = "删除作业本目录",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	@GetMapping("/homeworkBookCatalog/deleteByBookOid")
	@ApiOperation(value = "删除作业本目录",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult deleteByBookOid(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

}

