package com.light.aiszzy.schoolBook.service;


import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookExportVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.schoolBook.api.SchoolBookApi;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.BatchSchoolBookBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 教辅或作业本开通记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@FeignClient(contextId = "schoolBookApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = SchoolBookApiService.SchoolBookApiFallbackFactory.class)
@Component
public interface SchoolBookApiService  extends SchoolBookApi {

	@Component
	class SchoolBookApiFallbackFactory implements FallbackFactory<SchoolBookApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(SchoolBookApiService.SchoolBookApiFallbackFactory.class);
		@Override
		public SchoolBookApiService create(Throwable cause) {
			SchoolBookApiService.SchoolBookApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new SchoolBookApiService() {
				public AjaxResult getSchoolBookPageListByCondition(SchoolBookConditionBo condition){
					return AjaxResult.fail("教辅或作业本开通记录表查询失败");
				}
				public AjaxResult getSchoolBookListByCondition(SchoolBookConditionBo condition){
					return AjaxResult.fail("教辅或作业本开通记录表查询失败");
				}

				public AjaxResult addSchoolBook(SchoolBookBo schoolBookBo){
					return AjaxResult.fail("教辅或作业本开通记录表新增失败");
				}

				public AjaxResult batchAddSchoolBook(BatchSchoolBookBo batchSchoolBookBo){
					return AjaxResult.fail("批量开通教辅或作业本失败");
				}

				public AjaxResult updateSchoolBook(SchoolBookBo schoolBookBo){
					return AjaxResult.fail("教辅或作业本开通记录表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("教辅或作业本开通记录表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("教辅或作业本开通记录表删除失败");
				}

				public AjaxResult getPracticeBookPageListWithSchoolCount(com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo condition){
					return AjaxResult.fail("教辅或作业开通列表查询失败");
					}

					public AjaxResult getPracticeBookWithSchoolPageListByCondition(SchoolBookConditionBo condition){
						return AjaxResult.fail("查询教辅列表与学校开通信息关联失败");
				}

				@Override
				public AjaxResult<List<PracticeBookExportVo>> exportPracticeBookWithSchoolCount(PracticeBookConditionBo condition) {
					return AjaxResult.fail("导出教辅列表失败");
				}
			};
		}
	}
}