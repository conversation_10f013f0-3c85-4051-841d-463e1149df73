package com.light.aiszzy.practiceBook.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教辅目录每页图片记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface PracticeBookPageApi  {

	/**
	 * 查询教辅目录每页图片记录表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookPage/pageList")
	@ApiOperation(value = "分页查询教辅目录每页图片记录表",httpMethod = "POST")
	AjaxResult<PageInfo<PracticeBookPageVo>> getPracticeBookPagePageListByCondition(@RequestBody PracticeBookPageConditionBo condition);

	/**
	 * 查询所有教辅目录每页图片记录表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookPage/list")
	@ApiOperation(value = "查询所有教辅目录每页图片记录表",httpMethod = "POST")
	AjaxResult<List<PracticeBookPageVo>> getPracticeBookPageListByCondition(@RequestBody PracticeBookPageConditionBo condition);


	/**
	 * 新增教辅目录每页图片记录表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookPage/add")
	@ApiOperation(value = "新增教辅目录每页图片记录表",httpMethod = "POST")
	AjaxResult addPracticeBookPage(@Validated @RequestBody PracticeBookPageBo practiceBookPageBo);

	/**
	 * 修改教辅目录每页图片记录表
	 * @param practiceBookPageBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/practiceBookPage/update")
	@ApiOperation(value = "修改教辅目录每页图片记录表",httpMethod = "POST")
	AjaxResult updatePracticeBookPage(@Validated @RequestBody PracticeBookPageBo practiceBookPageBo);

	/**
	 * 查询教辅目录每页图片记录表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/practiceBookPage/detail")
	@ApiOperation(value = "查询教辅目录每页图片记录表详情",httpMethod = "GET")
	AjaxResult<PracticeBookPageVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除教辅目录每页图片记录表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/practiceBookPage/delete")
	@ApiOperation(value = "删除教辅目录每页图片记录表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);


	/**
	 *  保存教辅每页数据
	 * @param practiceBookOid the practiceBook oid 教辅 OID
	 * @param list the practice book page 分页数据
	 * @return {@link AjaxResult }
	 */
	@PostMapping("/practiceBookPage/saveBatchByPracticeBookOid/{practiceBookOid}")
    AjaxResult saveBatchByPracticeBookOid(@PathVariable("practiceBookOid") String practiceBookOid, @RequestBody List<PracticeBookPageBo> list);

	/**
	 *  根据教辅 OID 删除所有页码数据
	 * @param practiceBookOid the practice book oid 教辅 OID
	 * @return {@link AjaxResult }<{@link Void }>
	 */
	@PostMapping("/practiceBookPage/deleteByPraticeBookOid/{practiceBookOid}")
	AjaxResult<Void> deleteByPraticeBookOid(@PathVariable("practiceBookOid") String practiceBookOid);
}

