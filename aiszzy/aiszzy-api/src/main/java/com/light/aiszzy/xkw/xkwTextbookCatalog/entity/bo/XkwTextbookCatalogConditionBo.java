package com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 教材目录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwTextbookCatalogConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 教材ID
	 */
	@ApiModelProperty("教材ID")
	private Long textbookId;

	/**
	 * 排序值
	 */
	@ApiModelProperty("排序值")
	private Long ordinal;

	/**
	 * 父节点ID
	 */
	@ApiModelProperty("父节点ID")
	private Long parentId;

	/**
	 * 节点类型，分为实节点和虚节点（真实教材目录不存在的节点，例如：单元综合与测试）,可用值:VIRTUAL,REA
	 */
	@ApiModelProperty("节点类型，分为实节点和虚节点（真实教材目录不存在的节点，例如：单元综合与测试）,可用值:VIRTUAL,REA")
	private String type;

	/**
	 * 节点名称
	 */
	@ApiModelProperty("节点名称")
	private String name;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;


	private Integer grade;

	private String term;

	private Long versionId;

}
