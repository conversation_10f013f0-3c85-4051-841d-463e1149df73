package com.light.aiszzy.xkw.xkwQuestionTypes.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 试题类型
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwQuestionTypesVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 	课程ID
     */
    private Long courseId;

    /**
     * 题型名称
     */
    private String name;

    /**
     * 父题型
     */
    private Long parentId;

    /**
     * 排序值
     */
    private Long ordinal;

    /**
     * 是否客观题
     */
    private String objective;

}
