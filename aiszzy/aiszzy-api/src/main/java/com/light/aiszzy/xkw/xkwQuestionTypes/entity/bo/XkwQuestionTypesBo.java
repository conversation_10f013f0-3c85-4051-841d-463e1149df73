package com.light.aiszzy.xkw.xkwQuestionTypes.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 试题类型
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwQuestionTypesBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 试题类型id
	 */
	@ApiModelProperty("试题类型id")
	private Long xkwQuestionTypesId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private String id;
	/**
	 * 	课程ID
	 */
	@ApiModelProperty("	课程ID")
	private Long courseId;
	/**
	 * 题型名称
	 */
	@ApiModelProperty("题型名称")
	private String name;
	/**
	 * 父题型
	 */
	@ApiModelProperty("父题型")
	private Long parentId;
	/**
	 * 排序值
	 */
	@ApiModelProperty("排序值")
	private Long ordinal;
	/**
	 * 是否客观题
	 */
	@ApiModelProperty("是否客观题")
	private String objective;

}
