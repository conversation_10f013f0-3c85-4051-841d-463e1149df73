package com.light.aiszzy.userPaper.service;


import com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.userPaper.api.UserPaperPageApi;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 用户上传每页图片表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "userPaperPageApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = UserPaperPageApiService.UserPaperPageApiFallbackFactory.class)
@Component
public interface UserPaperPageApiService  extends UserPaperPageApi {

	@Component
	class UserPaperPageApiFallbackFactory implements FallbackFactory<UserPaperPageApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(UserPaperPageApiService.UserPaperPageApiFallbackFactory.class);
		@Override
		public UserPaperPageApiService create(Throwable cause) {
			UserPaperPageApiService.UserPaperPageApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new UserPaperPageApiService() {
				public AjaxResult getUserPaperPagePageListByCondition(UserPaperPageConditionBo condition){
					return AjaxResult.fail("用户上传每页图片表查询失败");
				}
				public AjaxResult getUserPaperPageListByCondition(UserPaperPageConditionBo condition){
					return AjaxResult.fail("用户上传每页图片表查询失败");
				}

				public AjaxResult addUserPaperPage(UserPaperPageBo userPaperPageBo){
					return AjaxResult.fail("用户上传每页图片表新增失败");
				}

				public AjaxResult updateUserPaperPage(UserPaperPageBo userPaperPageBo){
					return AjaxResult.fail("用户上传每页图片表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("用户上传每页图片表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("用户上传每页图片表删除失败");
				}

				@Override
				public AjaxResult deleteByUserPaperOid(String userPaperOid) {
					return AjaxResult.fail("删除失败");
				}

				@Override
				public AjaxResult saveBatchByUserPaperOid(String userPaperOid, List<UserPaperPageBo> list) {
					return AjaxResult.fail("保存失败");
				}

				@Override
				public AjaxResult<List<UserPaperPageVo>> queryByUserPaperOid(String userPaperOid) {
					return AjaxResult.fail("查询失败");
				}
			};
		}
	}
}