package com.light.aiszzy.schoolResourcesQuestion.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 资源库题目表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class SchoolResourcesQuestionBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 资源库题目表id
	 */
	@ApiModelProperty("资源库题目表id")
	private Long schoolResourcesQuestionId;
	
	/**
	 * 主键id
	 */
	@ApiModelProperty("主键id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 学校CODE
	 */
	@ApiModelProperty("学校CODE")
	private String orgCode;
	/**
	 * 题型id
	 */
	@ApiModelProperty("题型id")
	private String questionTypeId;
	/**
	 * 题型名称
	 */
	@ApiModelProperty("题型名称")
	private String questionTypeName;
	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Integer subject;
	/**
	 * 年份
	 */
	@ApiModelProperty("年份")
	private String year;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;

	/**
	 * 难度
	 */
	@ApiModelProperty("难度")
	private Integer difficultId;
	/**
	 * 题目url或文字
	 */
	@ApiModelProperty("题目url或文字")
	private String quesBody;
	/**
	 * 公共题干url或文字
	 */
	@ApiModelProperty("公共题干url或文字")
	private String publicQues;
	/**
	 * 答案url或文字
	 */
	@ApiModelProperty("答案url或文字")
	private String quesAnswer;
	/**
	 * 解析url或文字
	 */
	@ApiModelProperty("解析url或文字")
	private String analysisAnswer;
	/**
	 * 题目展示类型  0：图片url  1：html文字 
	 */
	@ApiModelProperty("题目展示类型  0：图片url  1：html文字 ")
	private Long quesBodyType;
	/**
	 * 公共题干展示类型  0：图片url  1：html文字
	 */
	@ApiModelProperty("公共题干展示类型  0：图片url  1：html文字")
	private Long publicQuesType;
	/**
	 * 答案展示类型  0：图片url  1：html文字 
	 */
	@ApiModelProperty("答案展示类型  0：图片url  1：html文字 ")
	private Long quesAnswerType;
	/**
	 * 解析展示类型  0：图片url  1：html文字 
	 */
	@ApiModelProperty("解析展示类型  0：图片url  1：html文字 ")
	private Long analysisAnswerType;

	/**
	 * 知识点
	 */
	@ApiModelProperty("知识点")
	private String knowledgePointsId;
	/**
	 * 章节ID
	 */
	@ApiModelProperty("章节ID")
	private String chapterId;
	/**
	 * 节ID
	 */
	@ApiModelProperty("节ID")
	private String sectionId;
	/**
	 * 来源(学科网xkw，好未来hwl)
	 */
	@ApiModelProperty("来源(学科网xkw，好未来hwl)")
	private String thirdSourceType;
	/**
	 * 外部id
	 */
	@ApiModelProperty("外部id")
	private String thirdOutId;
	/**
	 * 关联question题目oid
	 */
	@ApiModelProperty("关联question题目oid")
	private String questionOid;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
