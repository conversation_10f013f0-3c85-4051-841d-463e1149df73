package com.light.aiszzy.schoolResourcesQuestion.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 资源库题目相似题
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class SchoolResourcesQuestionSimilarRelationBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 资源库题目相似题id
	 */
	@ApiModelProperty("资源库题目相似题id")
	private Long schoolResourcesQuestionSimilarRelationId;
	
	/**
	 * 主键id
	 */
	@ApiModelProperty("主键id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 学校CODE
	 */
	@ApiModelProperty("学校CODE")
	private String orgCode;
	/**
	 * school_resource_question的oid
	 */
	@ApiModelProperty("school_resource_question的oid")
	private String schoolResourcesQuestionOid;
	/**
	 * 外部题目id
	 */
	@ApiModelProperty("外部题目id")
	private String thirdOutId;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
