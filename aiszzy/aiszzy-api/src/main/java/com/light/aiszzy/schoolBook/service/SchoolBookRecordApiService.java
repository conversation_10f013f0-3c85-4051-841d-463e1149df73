package com.light.aiszzy.schoolBook.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.schoolBook.api.SchoolBookRecordApi;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 教辅或作业本开通记录详情表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@FeignClient(contextId = "schoolBookRecordApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = SchoolBookRecordApiService.SchoolBookRecordApiFallbackFactory.class)
@Component
public interface SchoolBookRecordApiService  extends SchoolBookRecordApi {

	@Component
	class SchoolBookRecordApiFallbackFactory implements FallbackFactory<SchoolBookRecordApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(SchoolBookRecordApiFallbackFactory.class);
		@Override
		public SchoolBookRecordApiService create(Throwable cause) {
			SchoolBookRecordApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new SchoolBookRecordApiService() {
				public AjaxResult getSchoolBookRecordPageListByCondition(SchoolBookRecordConditionBo condition){
					return AjaxResult.fail("教辅或作业本开通记录详情表查询失败");
				}
				public AjaxResult getSchoolBookRecordListByCondition(SchoolBookRecordConditionBo condition){
					return AjaxResult.fail("教辅或作业本开通记录详情表查询失败");
				}

				public AjaxResult addSchoolBookRecord(SchoolBookRecordBo schoolBookRecordBo){
					return AjaxResult.fail("教辅或作业本开通记录详情表新增失败");
				}

				public AjaxResult updateSchoolBookRecord(SchoolBookRecordBo schoolBookRecordBo){
					return AjaxResult.fail("教辅或作业本开通记录详情表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("教辅或作业本开通记录详情表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("教辅或作业本开通记录详情表删除失败");
				}
			};
		}
	}
}