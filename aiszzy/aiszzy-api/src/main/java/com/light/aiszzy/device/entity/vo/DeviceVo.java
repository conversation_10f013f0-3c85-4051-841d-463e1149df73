package com.light.aiszzy.device.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 设备表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@Data
public class DeviceVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 型号
     */
    private String model;

    /**
     * 硬件序列号
     */
    private String hardwareCode;

    /**
     * 软件激活码
     */
    private String activationCode;

    /**
     * 是否激活 (0-未激活 1-已激活)
     */
    private Integer isActivated;

    /**
     * 设备状态  0禁用 1闲置 2正常
     */
    private Integer status;

    /**
     * 当前绑定学校
     */
    private String orgCode;

    /**
     * 绑定学校所在地区
     */
    private String orgName;

    /**
     * 学校区域
     */
    private String orgAreaName;

    /**
     * 学校区域CODE
     */
    private String orgAreaCode;

    /**
     * 学校市区
     */
    private String orgCityName;

    /**
     * 学校市区CODE
     */
    private String orgCityCode;

    /**
     * 学校省
     */
    private String orgProvinceName;

    /**
     * 学校省CODE
     */
    private String orgProvinceCode;

    /**
     * 设备MAC地址，设备绑定的时候更新
     */
    private String deviceMacAddress;

    /**
     * 客户端版本
     */
    private String clientVersion;

    /**
     * 扩展信息，备用
     */
    private String extendInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
