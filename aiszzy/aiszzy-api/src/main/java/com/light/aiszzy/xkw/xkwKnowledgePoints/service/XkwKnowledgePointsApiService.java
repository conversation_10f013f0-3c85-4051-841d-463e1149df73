package com.light.aiszzy.xkw.xkwKnowledgePoints.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.xkw.xkwKnowledgePoints.api.XkwKnowledgePointsApi;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsBo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 知识树接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@FeignClient(contextId = "xkwKnowledgePointsApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = XkwKnowledgePointsApiService.XkwKnowledgePointsApiFallbackFactory.class)
@Component
public interface XkwKnowledgePointsApiService  extends XkwKnowledgePointsApi {

	@Component
	class XkwKnowledgePointsApiFallbackFactory implements FallbackFactory<XkwKnowledgePointsApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(XkwKnowledgePointsApiService.XkwKnowledgePointsApiFallbackFactory.class);
		@Override
		public XkwKnowledgePointsApiService create(Throwable cause) {
			XkwKnowledgePointsApiService.XkwKnowledgePointsApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new XkwKnowledgePointsApiService() {
				public AjaxResult getXkwKnowledgePointsPageListByCondition(XkwKnowledgePointsConditionBo condition){
					return AjaxResult.fail("知识树查询失败");
				}
				public AjaxResult getXkwKnowledgePointsListByCondition(XkwKnowledgePointsConditionBo condition){
					return AjaxResult.fail("知识树查询失败");
				}

				public AjaxResult addXkwKnowledgePoints(XkwKnowledgePointsBo xkwKnowledgePointsBo){
					return AjaxResult.fail("知识树新增失败");
				}

				public AjaxResult updateXkwKnowledgePoints(XkwKnowledgePointsBo xkwKnowledgePointsBo){
					return AjaxResult.fail("知识树更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("知识树获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("知识树删除失败");
				}
			};
		}
	}
}