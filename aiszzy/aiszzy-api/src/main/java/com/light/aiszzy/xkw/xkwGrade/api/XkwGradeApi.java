package com.light.aiszzy.xkw.xkwGrade.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwGrade.entity.vo.XkwGradeVo;
import com.light.aiszzy.xkw.xkwGrade.entity.bo.XkwGradeBo;
import com.light.aiszzy.xkw.xkwGrade.entity.bo.XkwGradeConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 年级接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface XkwGradeApi  {

	/**
	 * 查询年级列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwGrade/pageList")
	@ApiOperation(value = "分页查询年级",httpMethod = "POST")
	AjaxResult<PageInfo<XkwGradeVo>> getXkwGradePageListByCondition(@RequestBody XkwGradeConditionBo condition);

	/**
	 * 查询所有年级列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwGrade/list")
	@ApiOperation(value = "查询所有年级",httpMethod = "POST")
	AjaxResult<List<XkwGradeVo>> getXkwGradeListByCondition(@RequestBody XkwGradeConditionBo condition);


	/**
	 * 新增年级
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwGrade/add")
	@ApiOperation(value = "新增年级",httpMethod = "POST")
	AjaxResult addXkwGrade(@Validated @RequestBody XkwGradeBo xkwGradeBo);

	/**
	 * 修改年级
	 * @param xkwGradeBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwGrade/update")
	@ApiOperation(value = "修改年级",httpMethod = "POST")
	AjaxResult updateXkwGrade(@Validated @RequestBody XkwGradeBo xkwGradeBo);

	/**
	 * 查询年级详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/xkwGrade/detail")
	@ApiOperation(value = "查询年级详情",httpMethod = "GET")
	AjaxResult<XkwGradeVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除年级
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/xkwGrade/delete")
	@ApiOperation(value = "删除年级",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

