package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkBookCatalogInfoApi;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业本目录关联作业接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "homeworkBookCatalogInfoApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkBookCatalogInfoApiService.HomeworkBookCatalogInfoApiFallbackFactory.class)
@Component
public interface HomeworkBookCatalogInfoApiService  extends HomeworkBookCatalogInfoApi {

	@Component
	class HomeworkBookCatalogInfoApiFallbackFactory implements FallbackFactory<HomeworkBookCatalogInfoApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkBookCatalogInfoApiService.HomeworkBookCatalogInfoApiFallbackFactory.class);
		@Override
		public HomeworkBookCatalogInfoApiService create(Throwable cause) {
			HomeworkBookCatalogInfoApiService.HomeworkBookCatalogInfoApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkBookCatalogInfoApiService() {
				public AjaxResult getHomeworkBookCatalogInfoPageListByCondition(HomeworkBookCatalogInfoConditionBo condition){
					return AjaxResult.fail("作业本目录关联作业查询失败");
				}
				public AjaxResult getHomeworkBookCatalogInfoListByCondition(HomeworkBookCatalogInfoConditionBo condition){
					return AjaxResult.fail("作业本目录关联作业查询失败");
				}

				@Override
				public AjaxResult bind(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
					return AjaxResult.fail("绑作业本目录关联作业失败");
				}

				@Override
				public AjaxResult unbind(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
					return AjaxResult.fail("解绑作业本目录关联作业失败");
				}

				@Override
				public AjaxResult sortHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
					return AjaxResult.fail("关联后排序失败");
				}

				public AjaxResult addHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo){
					return AjaxResult.fail("作业本目录关联作业新增失败");
				}

				public AjaxResult updateHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo){
					return AjaxResult.fail("作业本目录关联作业更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业本目录关联作业获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业本目录关联作业删除失败");
				}

			};
		}
	}
}