package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogInfoVo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业本目录关联作业接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkBookCatalogInfoApi  {

	/**
	 * 查询作业本目录关联作业列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalogInfo/pageList")
	@ApiOperation(value = "分页查询作业本目录关联作业",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkBookCatalogInfoVo>> getHomeworkBookCatalogInfoPageListByCondition(@RequestBody HomeworkBookCatalogInfoConditionBo condition);

	/**
	 * 查询所有作业本目录关联作业列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalogInfo/list")
	@ApiOperation(value = "查询所有作业本目录关联作业",httpMethod = "POST")
	AjaxResult<List<HomeworkBookCatalogInfoVo>> getHomeworkBookCatalogInfoListByCondition(@RequestBody HomeworkBookCatalogInfoConditionBo condition);


	/**
	 * 绑定作业本目录关联作业
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalogInfo/bind")
	@ApiOperation(value = "绑定作业本目录关联作业",httpMethod = "POST")
	AjaxResult bind(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);

	/**
	 * 解绑作业本目录关联作业
	 * @param homeworkBookCatalogInfoBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalogInfo/unbind")
	@ApiOperation(value = "解绑作业本目录关联作业",httpMethod = "POST")
	AjaxResult unbind(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);


	/**
	 * 关联后排序
	 * @param homeworkBookCatalogInfoBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-03-28 16:01:05
	 */
	@PostMapping("/homeworkBookCatalogInfo/sort")
	@ApiOperation(value = "关联后排序",httpMethod = "POST")
	AjaxResult sortHomeworkBookCatalogInfo(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);


	/**
	 * 新增作业本目录关联作业
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalogInfo/add")
	@ApiOperation(value = "新增作业本目录关联作业",httpMethod = "POST")
	AjaxResult addHomeworkBookCatalogInfo(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);

	/**
	 * 修改作业本目录关联作业
	 * @param homeworkBookCatalogInfoBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkBookCatalogInfo/update")
	@ApiOperation(value = "修改作业本目录关联作业",httpMethod = "POST")
	AjaxResult updateHomeworkBookCatalogInfo(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);

	/**
	 * 查询作业本目录关联作业详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkBookCatalogInfo/detail")
	@ApiOperation(value = "查询作业本目录关联作业详情",httpMethod = "GET")
	AjaxResult<HomeworkBookCatalogInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业本目录关联作业
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkBookCatalogInfo/delete")
	@ApiOperation(value = "删除作业本目录关联作业",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

