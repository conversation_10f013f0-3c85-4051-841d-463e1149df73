package com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 知识树
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class XkwKnowledgePointsConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 知识点名称
	 */
	@ApiModelProperty("知识点名称")
	private String name;

	/**
	 * 课程ID
	 */
	@ApiModelProperty("课程ID")
	private Long courseId;

	/**
	 * 节点深度，一级节点的深度为1，二级节点的深度为2，以此类推。
	 */
	@ApiModelProperty("节点深度，一级节点的深度为1，二级节点的深度为2，以此类推。")
	private String depth;

	/**
	 * root节点的ID
	 */
	@ApiModelProperty("root节点的ID")
	private String rootId;

	/**
	 * 父节点ID
	 */
	@ApiModelProperty("父节点ID")
	private String parentId;

	/**
	 * 适用于精简版
	 */
	@ApiModelProperty("适用于精简版")
	private String forLite;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 一级知识点的标签，仅当节点为一级知识点时有效，分为：STANDARD|课标,CONTEST|竞赛,PREPARE|学段衔接,NEXT-GEN|新一代
	 */
	@ApiModelProperty("一级知识点的标签，仅当节点为一级知识点时有效，分为：STANDARD|课标,CONTEST|竞赛,PREPARE|学段衔接,NEXT-GEN|新一代")
	private String tag;

	/**
	 * 	节点类型，可用值：NODE、KNOWLEDGE_POINT、TESTING_POINT，分别代表普通节点、知识点、考点
	 */
	@ApiModelProperty("	节点类型，可用值：NODE、KNOWLEDGE_POINT、TESTING_POINT，分别代表普通节点、知识点、考点")
	private String type;

	/**
	 * 排序值
	 */
	@ApiModelProperty("排序值")
	private Long ordinal;



	private Integer stageId;

	private String subject;

}
