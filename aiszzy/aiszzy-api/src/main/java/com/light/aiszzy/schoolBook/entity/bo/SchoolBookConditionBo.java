package com.light.aiszzy.schoolBook.entity.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 教辅或作业本开通记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class SchoolBookConditionBo extends PageLimitBo{

	/**
	 * 自增主键，唯一标识每一条开通记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条开通记录")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 学校CODE
	 */
	@ApiModelProperty("学校CODE")
	private String orgCode;

	/**
	 * 学校名称
	 */
	@ApiModelProperty("学校名称")
	private String orgName;

	/**
	 * 学校区域
	 */
	@ApiModelProperty("学校区域")
	private String orgAreaName;

	/**
	 * 学校区域CODE
	 */
	@ApiModelProperty("学校区域CODE")
	private String orgAreaCode;

	/**
	 * 学校市区
	 */
	@ApiModelProperty("学校市区")
	private String orgCityName;

	/**
	 * 学校市区CODE
	 */
	@ApiModelProperty("学校市区CODE")
	private String orgCityCode;

	/**
	 * 学校省
	 */
	@ApiModelProperty("学校省")
	private String orgProvinceName;

	/**
	 * 学校省CODE
	 */
	@ApiModelProperty("学校省CODE")
	private String orgProvinceCode;

	/**
	 * 教辅OID或作业本OID
	 */
	@ApiModelProperty("教辅OID或作业本OID")
	private String bookOid;

	/**
	 * 启用 1启用 2停用
	 */
	@ApiModelProperty("启用 1启用 2停用")
	private Integer status;

	/**
	 * 开通开始日期
	 */
	@ApiModelProperty("开通开始日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date startDate;

	/**
	 * 教辅或作业本
	 */
	@ApiModelProperty("教辅或作业本")
	private String bookType;

	/**
	 * 开通结束日期
	 */
	@ApiModelProperty("开通结束日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDate;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 查询状态字段(1启用，2停用，3使用中，4即将到期，5已到期，6未开始)
	 * 3的时候表示当前时间在开通开始日期和开通结束日期之间
	 * // 先不加：4的时候表示当前时间在开通结束日期之前，且开通结束日期在当前时间之后的30天内
	 * 5的时候表示 当前时间在开通结束日期之前
	 * 6的时候表示 当前时间在开通开始日期之前
	 */
	@ApiModelProperty("查询状态字段(1启用，2停用，3使用中，4即将到期，5已到期，6未开始)")
	private Integer computedStatus;

	/**
	 * 教辅名称（用于关联查询practice_book表）
	 */
	@ApiModelProperty("教辅名称")
	private String practiceBookName;

	/**
	 * 教辅学科（用于关联查询practice_book表）
	 */
	@ApiModelProperty("教辅学科")
	private Integer practiceBookSubject;

	/**
	 * 状态 1下架，2上架
	 */
	@ApiModelProperty("状态 1下架，2上架")
	private Integer practiceBookStatus;

	/**
	 * 教辅年级（用于关联查询practice_book表）
	 */
	@ApiModelProperty("教辅年级")
	private Integer practiceBookGrade;

	/**
	 * 模糊搜索查询字段：教辅名称
	 */
	@ApiModelProperty("模糊搜索查询字段：教辅名称")
	private String keywords;
}
