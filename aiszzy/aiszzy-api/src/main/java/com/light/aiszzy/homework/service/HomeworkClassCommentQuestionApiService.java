package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkClassCommentQuestionApi;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 班级作业讲评题目接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@FeignClient(contextId = "homeworkClassCommentQuestionApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkClassCommentQuestionApiService.HomeworkClassCommentQuestionApiFallbackFactory.class)
@Component
public interface HomeworkClassCommentQuestionApiService  extends HomeworkClassCommentQuestionApi {

	@Component
	class HomeworkClassCommentQuestionApiFallbackFactory implements FallbackFactory<HomeworkClassCommentQuestionApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkClassCommentQuestionApiFallbackFactory.class);
		@Override
		public HomeworkClassCommentQuestionApiService create(Throwable cause) {
			HomeworkClassCommentQuestionApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkClassCommentQuestionApiService() {
				public AjaxResult getHomeworkClassCommentQuestionPageListByCondition(HomeworkClassCommentQuestionConditionBo condition){
					return AjaxResult.fail("班级作业讲评题目查询失败");
				}
				public AjaxResult getHomeworkClassCommentQuestionListByCondition(HomeworkClassCommentQuestionConditionBo condition){
					return AjaxResult.fail("班级作业讲评题目查询失败");
				}

				public AjaxResult addHomeworkClassCommentQuestion(HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo){
					return AjaxResult.fail("班级作业讲评题目新增失败");
				}

				public AjaxResult updateHomeworkClassCommentQuestion(HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo){
					return AjaxResult.fail("班级作业讲评题目更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("班级作业讲评题目获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("班级作业讲评题目删除失败");
				}
			};
		}
	}
}