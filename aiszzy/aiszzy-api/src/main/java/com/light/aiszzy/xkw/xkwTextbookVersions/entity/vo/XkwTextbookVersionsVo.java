package com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 教材版本
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class XkwTextbookVersionsVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 教材版本ID
     */
    private Long id;

    /**
     * 	课程ID
     */
    private Long courseId;

    /**
     * 启用年份
     */
    private Long year;

    /**
     * 排序值
     */
    private Long ordinal;

    /**
     * 教材版本名称
     */
    private String name;

}
