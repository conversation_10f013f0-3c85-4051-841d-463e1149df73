package com.light.aiszzy.xkw.xkwCourses.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkw.xkwCourses.entity.vo.XkwCoursesVo;
import com.light.aiszzy.xkw.xkwCourses.entity.bo.XkwCoursesBo;
import com.light.aiszzy.xkw.xkwCourses.entity.bo.XkwCoursesConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 课程接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface XkwCoursesApi  {

	/**
	 * 查询课程列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwCourses/pageList")
	@ApiOperation(value = "分页查询课程",httpMethod = "POST")
	AjaxResult<PageInfo<XkwCoursesVo>> getXkwCoursesPageListByCondition(@RequestBody XkwCoursesConditionBo condition);

	/**
	 * 查询所有课程列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwCourses/list")
	@ApiOperation(value = "查询所有课程",httpMethod = "POST")
	AjaxResult<List<XkwCoursesVo>> getXkwCoursesListByCondition(@RequestBody XkwCoursesConditionBo condition);


	/**
	 * 新增课程
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwCourses/add")
	@ApiOperation(value = "新增课程",httpMethod = "POST")
	AjaxResult addXkwCourses(@Validated @RequestBody XkwCoursesBo xkwCoursesBo);

	/**
	 * 修改课程
	 * @param xkwCoursesBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@PostMapping("/xkwCourses/update")
	@ApiOperation(value = "修改课程",httpMethod = "POST")
	AjaxResult updateXkwCourses(@Validated @RequestBody XkwCoursesBo xkwCoursesBo);

	/**
	 * 查询课程详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/xkwCourses/detail")
	@ApiOperation(value = "查询课程详情",httpMethod = "GET")
	AjaxResult<XkwCoursesVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除课程
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:35
	 */
	@GetMapping("/xkwCourses/delete")
	@ApiOperation(value = "删除课程",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

