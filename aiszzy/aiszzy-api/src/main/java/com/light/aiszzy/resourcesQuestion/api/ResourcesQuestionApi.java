package  com.light.aiszzy.resourcesQuestion.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionVo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 资源库题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface ResourcesQuestionApi  {

	/**
	 * 查询资源库题目表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/resourcesQuestion/pageList")
	@ApiOperation(value = "分页查询资源库题目表",httpMethod = "POST")
	AjaxResult<PageInfo<ResourcesQuestionVo>> getResourcesQuestionPageListByCondition(@RequestBody ResourcesQuestionConditionBo condition);

	/**
	 * 查询所有资源库题目表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/resourcesQuestion/list")
	@ApiOperation(value = "查询所有资源库题目表",httpMethod = "POST")
	AjaxResult<List<ResourcesQuestionVo>> getResourcesQuestionListByCondition(@RequestBody ResourcesQuestionConditionBo condition);


	/**
	 * 新增资源库题目表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/resourcesQuestion/add")
	@ApiOperation(value = "新增资源库题目表",httpMethod = "POST")
	AjaxResult addResourcesQuestion(@Validated @RequestBody ResourcesQuestionBo resourcesQuestionBo);

	/**
	 * 修改资源库题目表
	 * @param resourcesQuestionBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/resourcesQuestion/update")
	@ApiOperation(value = "修改资源库题目表",httpMethod = "POST")
	AjaxResult updateResourcesQuestion(@Validated @RequestBody ResourcesQuestionBo resourcesQuestionBo);

	/**
	 * 查询资源库题目表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/resourcesQuestion/detail")
	@ApiOperation(value = "查询资源库题目表详情",httpMethod = "GET")
	AjaxResult<ResourcesQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除资源库题目表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/resourcesQuestion/delete")
	@ApiOperation(value = "删除资源库题目表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

