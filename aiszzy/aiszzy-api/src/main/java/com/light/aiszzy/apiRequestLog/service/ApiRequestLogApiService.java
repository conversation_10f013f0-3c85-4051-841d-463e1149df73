package com.light.aiszzy.apiRequestLog.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.apiRequestLog.api.ApiRequestLogApi;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogBo;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 第三方请求日志表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "apiRequestLogApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = ApiRequestLogApiService.ApiRequestLogApiFallbackFactory.class)
@Component
public interface ApiRequestLogApiService  extends ApiRequestLogApi {

	@Component
	class ApiRequestLogApiFallbackFactory implements FallbackFactory<ApiRequestLogApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(ApiRequestLogApiService.ApiRequestLogApiFallbackFactory.class);
		@Override
		public ApiRequestLogApiService create(Throwable cause) {
			ApiRequestLogApiService.ApiRequestLogApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new ApiRequestLogApiService() {
				public AjaxResult getApiRequestLogPageListByCondition(ApiRequestLogConditionBo condition){
					return AjaxResult.fail("第三方请求日志表查询失败");
				}
				public AjaxResult getApiRequestLogListByCondition(ApiRequestLogConditionBo condition){
					return AjaxResult.fail("第三方请求日志表查询失败");
				}

				public AjaxResult addApiRequestLog(ApiRequestLogBo apiRequestLogBo){
					return AjaxResult.fail("第三方请求日志表新增失败");
				}

				public AjaxResult updateApiRequestLog(ApiRequestLogBo apiRequestLogBo){
					return AjaxResult.fail("第三方请求日志表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("第三方请求日志表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("第三方请求日志表删除失败");
				}
			};
		}
	}
}