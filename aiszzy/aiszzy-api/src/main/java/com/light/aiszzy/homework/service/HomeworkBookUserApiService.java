package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkBookUserApi;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 老师使用作业本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-18 14:20:30
 */
@FeignClient(contextId = "homeworkBookUserApiService", value = "light-aiszzy", configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkBookUserApiService.HomeworkBookUserApiFallbackFactory.class)
@Component
public interface HomeworkBookUserApiService extends HomeworkBookUserApi {

    @Component
    class HomeworkBookUserApiFallbackFactory implements FallbackFactory<HomeworkBookUserApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkBookUserApiFallbackFactory.class);

        @Override
        public HomeworkBookUserApiService create(Throwable cause) {
            HomeworkBookUserApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
            return new HomeworkBookUserApiService() {
                public AjaxResult getHomeworkBookUserListByCondition(HomeworkBookUserConditionBo condition) {
                    return AjaxResult.fail("老师使用作业本查询失败");
                }

                public AjaxResult addHomeworkBookUser(HomeworkBookUserBo homeworkBookUserBo) {
                    return AjaxResult.fail("老师使用作业本新增失败");
                }

                public AjaxResult updateHomeworkBookUser(HomeworkBookUserBo homeworkBookUserBo) {
                    return AjaxResult.fail("老师使用作业本更新失败");
                }

                public AjaxResult getDetail(String oid) {
                    return AjaxResult.fail("老师使用作业本获取详情失败");
                }

                public AjaxResult delete(String oid) {
                    return AjaxResult.fail("老师使用作业本删除失败");
                }

                @Override
                public AjaxResult deleteUse(String homeworkBookOid, String userCode, String orgCode) {
                    return AjaxResult.fail("老师使用作业本删除失败");
                }
            };
        }
    }
}