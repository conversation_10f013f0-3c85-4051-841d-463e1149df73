package com.light.enums;

/**
 * 内部来源类型枚举
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public enum InsideSourceTypeEnum {

    /**
     * 教辅题目
     */
    PRACTICE_BOOK_QUESTION("practice_book_question", "教辅题目"),
    
    /**
     * 校本作业题目
     */
    HOMEWORK_QUESTION("homework_question", "校本作业题目"),
    
    /**
     * 用户试卷题目
     */
    USER_PAPER_QUESTION("user_paper_question", "用户试卷题目"),
    
    /**
     * 资源库题目
     */
    RESOURCE_QUESTION("resource_question", "资源库题目"),
    
    /**
     * 学校资源库题目
     */
    SCHOOL_RESOURCE_QUESTION("school_resource_question", "学校资源库题目");

    private final String code;
    private final String desc;

    InsideSourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static InsideSourceTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (InsideSourceTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
