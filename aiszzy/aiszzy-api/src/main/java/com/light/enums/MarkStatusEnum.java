package com.light.enums;

/**
 * 标注状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public enum MarkStatusEnum {

    /**
     * 未完成标注
     */
    NOT_COMPLETED(0, "未完成"),
    
    /**
     * 已完成标注
     */
    COMPLETED(1, "已完成");

    private final Integer code;
    private final String desc;

    MarkStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static MarkStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MarkStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
