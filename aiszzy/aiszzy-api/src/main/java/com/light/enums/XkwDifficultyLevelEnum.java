package com.light.enums;

import java.util.Arrays;
import java.util.List;

/**
 * @Classname XkwDifficultyLevelEnum
 * @Description 学科网题目难度枚举
 * @Date 2025/4/10 9:45
 * @Created by admin
 */
public enum XkwDifficultyLevelEnum {

    EASY(17, "容易"),
    FAIRLY_EASY(18, "较易"),
    AVERAGE(19, "一般"),
    HARD(20, "较难"),
    VERY_HARD(21, "困难");

    private Integer code;
    private String value;

    XkwDifficultyLevelEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValue(Integer code) {
        XkwDifficultyLevelEnum[] values = values();
        for (XkwDifficultyLevelEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getValue();
            }
        }
        return null;
    }

    public static Integer getCode(String value) {
        XkwDifficultyLevelEnum[] values = values();
        for (XkwDifficultyLevelEnum oneEnum : values) {
            if (oneEnum.getValue().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }

    /**
     * 根据code获取枚举值
     * @param code 难度等级代码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static XkwDifficultyLevelEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        XkwDifficultyLevelEnum[] values = values();
        for (XkwDifficultyLevelEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum;
            }
        }
        return null;
    }



    /**
     * 推荐策略枚举 - 支持扩展不同的推荐策略
     */
    public enum RecommendStrategy {
        DEFAULT(Arrays.asList(XkwDifficultyLevelEnum.AVERAGE, XkwDifficultyLevelEnum.HARD,
                XkwDifficultyLevelEnum.VERY_HARD)),
        EASY_FOCUS(Arrays.asList(XkwDifficultyLevelEnum.EASY, XkwDifficultyLevelEnum.FAIRLY_EASY,
                XkwDifficultyLevelEnum.AVERAGE)),
        HARD_FOCUS(Arrays.asList(XkwDifficultyLevelEnum.AVERAGE, XkwDifficultyLevelEnum.HARD,
                XkwDifficultyLevelEnum.VERY_HARD)),
        ALL_LEVELS(Arrays.asList(XkwDifficultyLevelEnum.values()));

        private final List<XkwDifficultyLevelEnum> difficulties;

        RecommendStrategy(List<XkwDifficultyLevelEnum> difficulties) {
            this.difficulties = difficulties;
        }

        public List<XkwDifficultyLevelEnum> getDifficulties() {
            return difficulties;
        }
    }
}
