package com.light.enums;

/**
 * 题目题干修改状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public enum QuestionModifyStatusEnum {

    /**
     * 未修改
     */
    NOT_MODIFIED(0, "未修改"),
    
    /**
     * 已修改
     */
    MODIFIED(1, "已修改");

    private final Integer code;
    private final String desc;

    QuestionModifyStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static QuestionModifyStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (QuestionModifyStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否已修改
     * @param code
     * @return
     */
    public static boolean isModified(Integer code) {
        return MODIFIED.getCode().equals(code);
    }

    /**
     * 判断是否未修改
     * @param code
     * @return
     */
    public static boolean isNotModified(Integer code) {
        return code == null || NOT_MODIFIED.getCode().equals(code);
    }
}