package com.light.enums;

/**
 * 题目展示类型枚举
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public enum QuestionBodyTypeEnum {

    /**
     * 图片URL
     */
    IMAGE_URL(0L, "图片URL"),
    
    /**
     * HTML文字
     */
    HTML_TEXT(1L, "HTML文字");

    private final Long code;
    private final String desc;

    QuestionBodyTypeEnum(Long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Long getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static QuestionBodyTypeEnum getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (QuestionBodyTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
