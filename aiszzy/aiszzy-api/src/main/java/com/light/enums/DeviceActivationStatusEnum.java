package com.light.enums;

/**
 * 设备激活状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
public enum DeviceActivationStatusEnum {

    /**
     * 未激活
     */
    NOT_ACTIVATED(0, "未激活"),
    
    /**
     * 已激活
     */
    ACTIVATED(1, "已激活");

    private final Integer code;
    private final String desc;

    DeviceActivationStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static DeviceActivationStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeviceActivationStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否已激活
     * @param code
     * @return
     */
    public static boolean isActivated(Integer code) {
        return ACTIVATED.getCode().equals(code);
    }

    /**
     * 判断是否未激活
     * @param code
     * @return
     */
    public static boolean isNotActivated(Integer code) {
        return code == null || NOT_ACTIVATED.getCode().equals(code);
    }
}
