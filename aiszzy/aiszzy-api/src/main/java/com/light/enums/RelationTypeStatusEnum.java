package com.light.enums;

/**
 * 外部关系类型确认状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public enum RelationTypeStatusEnum {

    /**
     * 未确认
     */
    NOT_CONFIRMED(0, "未确认"),
    
    /**
     * 已确认
     */
    CONFIRMED(1, "已确认");

    private final Integer code;
    private final String desc;

    RelationTypeStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static RelationTypeStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RelationTypeStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
