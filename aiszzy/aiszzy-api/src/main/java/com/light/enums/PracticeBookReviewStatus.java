package com.light.enums;

import lombok.Getter;

@Getter
public enum PracticeBookReviewStatus {

    NOT_SUBMITTED(1, "未提交"),
    IN_REVIEW(2, "审核中"),
    APPROVED(3, "审核成功"),
    REJECTED(4, "拒绝");

    // 获取状态码
    private final int code;
    // 获取描述
    private final String description;

    // 构造方法
    PracticeBookReviewStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据状态码获取枚举值
    public static PracticeBookReviewStatus fromCode(int code) {
        for (PracticeBookReviewStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid code for ReviewStatus: " + code);
    }

    @Override
    public String toString() {
        return "ReviewStatus{" +
                "code=" + code +
                ", description='" + description + '\'' +
                '}';
    }
}
