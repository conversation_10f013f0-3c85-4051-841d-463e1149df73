package com.light.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum Semester {


    FIRST(1, "LAST"),  // 上学期
    SECOND(2, "NEXT"); // 下学期

    // 获取枚举值的方法
    private final int value; // 定义枚举的值

    private final String code;

    // 构造方法
    Semester(int value, String code) {
        this.value = value;
        this.code = code;
    }


    public static Semester getByValue(int value) {
        return Arrays.stream(values()).filter(x-> x.getValue() == value).findFirst().orElse(null);
    }

}