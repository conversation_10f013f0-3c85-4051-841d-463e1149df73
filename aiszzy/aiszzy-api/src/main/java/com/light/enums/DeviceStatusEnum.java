package com.light.enums;

/**
 * 设备状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
public enum DeviceStatusEnum {

    /**
     * 禁用
     */
    DISABLED(0, "禁用"),
    
    /**
     * 闲置
     */
    IDLE(1, "闲置"),
    
    /**
     * 正常
     */
    NORMAL(2, "正常");

    private final Integer code;
    private final String desc;

    DeviceStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static DeviceStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeviceStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为正常状态
     * @param code
     * @return
     */
    public static boolean isNormal(Integer code) {
        return NORMAL.getCode().equals(code);
    }

    /**
     * 判断是否为禁用状态
     * @param code
     * @return
     */
    public static boolean isDisabled(Integer code) {
        return DISABLED.getCode().equals(code);
    }

    /**
     * 判断是否为闲置状态
     * @param code
     * @return
     */
    public static boolean isIdle(Integer code) {
        return IDLE.getCode().equals(code);
    }
}
