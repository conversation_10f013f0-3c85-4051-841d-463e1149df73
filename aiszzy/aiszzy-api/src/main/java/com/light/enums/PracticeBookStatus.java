package com.light.enums;

import lombok.Getter;

@Getter
public enum PracticeBookStatus {

    //状态 1下架，2上架
    UN_PUB(1, "下架"),
    PUBLISHED(2, "上架");

    // 获取状态码
    private final int code;
    // 获取描述
    private final String description;

    // 构造方法
    PracticeBookStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据状态码获取枚举值
    public static PracticeBookStatus fromCode(int code) {
        for (PracticeBookStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "PracticeBookStatus{" +
                "code=" + code +
                ", description='" + description + '\'' +
                '}';
    }
}
