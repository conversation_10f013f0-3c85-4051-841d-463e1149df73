package com.light.enums;

/**
 * 学校教辅开通状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public enum SchoolBookStatus {

    /**
     * 启用
     */
    ENABLED(1, "启用"),
    
    /**
     * 停用
     */
    DISABLED(2, "停用");

    private final Integer code;
    private final String desc;

    SchoolBookStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static SchoolBookStatus getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SchoolBookStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
