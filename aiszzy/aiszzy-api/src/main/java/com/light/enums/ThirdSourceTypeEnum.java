package com.light.enums;

/**
 * 第三方来源类型枚举
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public enum ThirdSourceTypeEnum {

    /**
     * 学科网
     */
    XKW("xkw", "学科网"),
    
    /**
     * 好未来
     */
    HWL("hwl", "好未来");

    private final String code;
    private final String desc;

    ThirdSourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static ThirdSourceTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ThirdSourceTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
