package com.light.enums;

/**
 * 批改结果枚举
 */
public enum CorrectResultEnum {

    CORRECT(1L, "正确"),
    WRONG(2L, "错误"),
    UNKNOWN(3L, "未知");

    private final Long code;

    private final String reason;

    CorrectResultEnum(Long code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public Long getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }

}
