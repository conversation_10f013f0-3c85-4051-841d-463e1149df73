package com.light.aiszzy.xkw.xkwPaperInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.dto.XkwPaperInfoDto;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoConditionBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.vo.XkwPaperInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
public interface IXkwPaperInfoService extends IService<XkwPaperInfoDto> {

    List<XkwPaperInfoVo> getXkwPaperInfoListByCondition(XkwPaperInfoConditionBo condition);

    AjaxResult addXkwPaperInfo(QuestionBo bo);

    AjaxResult editXkwPaperInfo(QuestionBo bo);

    AjaxResult updateXkwPaperInfo(XkwPaperInfoBo xkwPaperInfoBo);

    XkwPaperInfoVo getDetail(String oid);

}

