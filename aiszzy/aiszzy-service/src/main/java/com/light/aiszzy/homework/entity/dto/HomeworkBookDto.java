package com.light.aiszzy.homework.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 作业本
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_book")
public class HomeworkBookDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业本名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 学校CODE
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 启用年份
	 */
	@TableField("year")
	private String year;

	/**
	 * 学期，1上学期，2下学期
	 */
	@TableField("term")
	private Integer term;

	/**
	 * 教材版本
	 */
	@TableField("text_book_version_id")
	private Long textBookVersionId;

	/**
	 * 教材
	 */
	@TableField("text_book_id")
	private Long textBookId;

	/**
	 * 1基础2
巩固3
提高
	 */
	@TableField("exercise_type")
	private Integer exerciseType;

	/**
	 * 封面
	 */
	@TableField("cover_url")
	private String coverUrl;

	/**
	 * 作业数量
	 */
	@TableField("num")
	private Integer num;

	/**
	 * 状态0未发布，1已经发布
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 是否有未审核作业1是，0否
	 */
	@TableField("has_no_confirm")
	private Integer hasNoConfirm;

	/**
	 * 是否完结1是，0否
	 */
	@TableField("is_completed")
	private Integer isCompleted;

	/**
	 * 来源（1、自建；2、引用教辅；3引用作业本）
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 教辅的oid或作业本oid
	 */
	@TableField("source_oid")
	private String sourceOid;

	/**
	 * 运营创建和老师创建
	 */
	@TableField("create_source")
	private String createSource;

	/**
	 * 所有作业pdf合并生成pdf下载地址
	 */
	@TableField("homework_pdf_url")
	private String homeworkPdfUrl;

	/**
	 * 所有作业答案解析合并生成word下载地址
	 */
	@TableField("homework_answer_pdf_url")
	private String homeworkAnswerPdfUrl;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
