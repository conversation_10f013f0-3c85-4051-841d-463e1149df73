package com.light.aiszzy.practiceBook.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookReviewVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookReviewService;

import com.light.aiszzy.practiceBook.api.PracticeBookReviewApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 教辅信息审核
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@RestController
@Validated
@Api(value = "", tags = "教辅信息审核接口")
public class PracticeBookReviewController implements PracticeBookReviewApi {

    @Autowired
    private IPracticeBookReviewService practiceBookReviewService;

    public AjaxResult<PageInfo<PracticeBookReviewVo>> getPracticeBookReviewPageListByCondition(@RequestBody PracticeBookReviewConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<PracticeBookReviewVo> pageInfo = new PageInfo<>(practiceBookReviewService.getPracticeBookReviewListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<PracticeBookReviewVo>> getPracticeBookReviewListByCondition(@RequestBody PracticeBookReviewConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(practiceBookReviewService.getPracticeBookReviewListByCondition(condition));
    }

    public AjaxResult addPracticeBookReview(@Validated @RequestBody PracticeBookReviewBo practiceBookReviewBo) {
        return practiceBookReviewService.addPracticeBookReview(practiceBookReviewBo);
    }

    public AjaxResult updatePracticeBookReview(@Validated @RequestBody PracticeBookReviewBo practiceBookReviewBo) {
        if (null == practiceBookReviewBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return practiceBookReviewService.updatePracticeBookReview(practiceBookReviewBo);
    }

    public AjaxResult<PracticeBookReviewVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(practiceBookReviewService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            PracticeBookReviewBo practiceBookReviewBo = new PracticeBookReviewBo();
            practiceBookReviewBo.setOid(oid);
            practiceBookReviewBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return practiceBookReviewService.updatePracticeBookReview(practiceBookReviewBo);
    }
}
