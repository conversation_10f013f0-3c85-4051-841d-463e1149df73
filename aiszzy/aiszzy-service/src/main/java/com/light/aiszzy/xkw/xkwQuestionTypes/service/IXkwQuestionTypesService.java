package com.light.aiszzy.xkw.xkwQuestionTypes.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.xkw.xkwQuestionTypes.entity.dto.XkwQuestionTypesDto;
import com.light.aiszzy.xkw.xkwQuestionTypes.entity.vo.XkwQuestionTypesVo;

import java.util.List;

/**
 * 试题类型接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
public interface IXkwQuestionTypesService extends IService<XkwQuestionTypesDto> {


    List<XkwQuestionTypesVo> getByGradeAndSubject(Integer grade, String subject);

    List<XkwQuestionTypesVo> getByStageAndSubject(Integer stage, String subject);
}

