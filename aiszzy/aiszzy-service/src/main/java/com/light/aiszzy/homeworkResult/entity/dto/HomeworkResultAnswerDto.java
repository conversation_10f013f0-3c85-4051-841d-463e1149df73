package com.light.aiszzy.homeworkResult.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.light.aiszzy.resultUploadFile.bo.HomeworkQuestionPosition;
import com.light.beans.Rect;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 学生题目答案表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_result_answer")
public class HomeworkResultAnswerDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业oid
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 学生作业结果oid
	 */
	@TableField("homework_result_oid")
	private String homeworkResultOid;

	/**
	 * 内容(原，包含学生作答区)
	 */
	@TableField("content")
	private String content;

	/**
	 * 内容(不包含学生作答区)
	 */
	@TableField("small_content")
	private String smallContent;

	/**
	 * 题目文本内容
	 */
	@TableField("text_content")
	private String textContent;

	/**
	 * 位置(不包含学生作答区)
	 */
	@TableField("small_position")
	private String smallPosition;

	/**
	 * 题号
	 */
	@TableField("question_no")
	private String questionNo;

	/**
	 * 大题号
	 */
	@TableField("big_num_title")
	private String bigNumTitle;

	/**
	 * 小题号
	 */
	@TableField("small_num_title")
	private String smallNumTitle;

	/**
	 * 排序编号
	 */
	@TableField("ques_order_num")
	private String quesOrderNum;

	/**
	 * 题型id
	 */
	@TableField("question_type_id")
	private String questionTypeId;

	/**
	 * 题型名称
	 */
	@TableField("question_type_name")
	private String questionTypeName;

	/**
	 * 答案
	 */
	@TableField("ques_answer")
	private String quesAnswer;

	/**
	 * 章节ID
	 */
	@TableField("chapter_id")
	private String chapterId;

	/**
	 * 节ID
	 */
	@TableField("section_id")
	private String sectionId;

	/**
	 * 知识点，多个
	 */
	@TableField("knowledge_points_id")
	private String knowledgePointsId;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 难度
	 */
	@TableField("difficult_id")
	private Integer difficultId;

	/**
	 * 启用年份
	 */
	@TableField("year")
	private String year;

	/**
	 * 题目图片,跨页包含多个地址
	 */
	@TableField("image_urls")
	private String imageUrls;

	/**
	 * 位置(原，包含学生作答区),跨页包含多个地址
	 */
	@TableField("positions")
	private String positions;

	/**
	 * 作业题目区域
	 */
	@TableField(exist = false)
	private Rect contentRect;
	/**
	 * 关联题目oid
	 */
	@TableField("question_oid")
	private String questionOid;

	/**
	 * 学校code
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 学生oid
	 */
	@TableField("stu_oid")
	private String stuOid;

	/**
	 * 页码
	 */
	@TableField("stu_page_no")
	private Long stuPageNo;

	/**
	 * 学生班级
	 */
	@TableField("stu_class_id")
	private String stuClassId;

	/**
	 * 学号
	 */
	@TableField("stu_no")
	private String stuNo;

	/**
	 * 是否正确
	 */
	@TableField("is_correct")
	private Long isCorrect;

	/**
	 * 作答结果
	 */
	@TableField("stu_answer")
	private String stuAnswer;

	/**
	 * 学生答题路径,跨页包含多个地址
	 */
	@TableField("stu_answer_urls")
	private String stuAnswerUrls;

	/**
	 * 标记坐标,跨页包含多个地址
	 */
	@TableField("stu_positions")
	private String stuPositions;

	/**
	 * 是否有疑问 0：无，1：存在，2：已经处理
	 */
	@TableField("is_doubt")
	private Integer isDoubt;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
