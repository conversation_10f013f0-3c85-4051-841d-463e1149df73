package com.light.aiszzy.resourcesQuestion.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationConditionBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionSimilarRelationVo;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionSimilarRelationService;

import com.light.aiszzy.resourcesQuestion.api.ResourcesQuestionSimilarRelationApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 资源库题目相似题
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "资源库题目相似题接口")
public class ResourcesQuestionSimilarRelationController implements ResourcesQuestionSimilarRelationApi {

    @Autowired
    private IResourcesQuestionSimilarRelationService resourcesQuestionSimilarRelationService;

    public AjaxResult<PageInfo<ResourcesQuestionSimilarRelationVo>> getResourcesQuestionSimilarRelationPageListByCondition(@RequestBody ResourcesQuestionSimilarRelationConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<ResourcesQuestionSimilarRelationVo> pageInfo = new PageInfo<>(resourcesQuestionSimilarRelationService.getResourcesQuestionSimilarRelationListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<ResourcesQuestionSimilarRelationVo>> getResourcesQuestionSimilarRelationListByCondition(@RequestBody ResourcesQuestionSimilarRelationConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(resourcesQuestionSimilarRelationService.getResourcesQuestionSimilarRelationListByCondition(condition));
    }

    public AjaxResult addResourcesQuestionSimilarRelation(@Validated @RequestBody ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo) {
        return resourcesQuestionSimilarRelationService.addResourcesQuestionSimilarRelation(resourcesQuestionSimilarRelationBo);
    }

    public AjaxResult updateResourcesQuestionSimilarRelation(@Validated @RequestBody ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo) {
        if (null == resourcesQuestionSimilarRelationBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return resourcesQuestionSimilarRelationService.updateResourcesQuestionSimilarRelation(resourcesQuestionSimilarRelationBo);
    }

    public AjaxResult<ResourcesQuestionSimilarRelationVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(resourcesQuestionSimilarRelationService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo = new ResourcesQuestionSimilarRelationBo();
            resourcesQuestionSimilarRelationBo.setOid(oid);
            resourcesQuestionSimilarRelationBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return resourcesQuestionSimilarRelationService.updateResourcesQuestionSimilarRelation(resourcesQuestionSimilarRelationBo);
    }
}
