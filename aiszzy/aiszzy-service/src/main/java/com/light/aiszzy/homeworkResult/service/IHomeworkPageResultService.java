package com.light.aiszzy.homeworkResult.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 扫描结构每页处理结果
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
public interface IHomeworkPageResultService extends IService<HomeworkPageResultDto> {

    List<HomeworkPageResultVo> getHomeworkPageResultListByCondition(HomeworkPageResultConditionBo condition);

    AjaxResult addHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo);

    AjaxResult updateHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo);

    HomeworkPageResultVo getDetail(String oid);

    AjaxResult dealNoStudent(HomeworkPageResultBo bo);

    AjaxResult dealRepeat(HomeworkPageResultBo bo);

}

