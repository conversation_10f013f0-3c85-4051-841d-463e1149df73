package com.light.aiszzy.resultUploadFile.deal.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.light.aiszzy.homework.entity.dto.HomeworkDto;
import com.light.aiszzy.homework.entity.dto.HomeworkPageDto;
import com.light.aiszzy.homework.entity.dto.HomeworkQuestionDto;
import com.light.aiszzy.homework.mapper.HomeworkMapper;
import com.light.aiszzy.homework.mapper.HomeworkPageMapper;
import com.light.aiszzy.homework.mapper.HomeworkQuestionMapper;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkPageResultService;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultAnswerService;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultService;
import com.light.aiszzy.hwlOpen.service.HwlService;
import com.light.aiszzy.resultUploadFile.bo.DealResultBo;
import com.light.aiszzy.resultUploadFile.bo.HomeworkQuestionPosition;
import com.light.aiszzy.resultUploadFile.bo.ResultUploadFileDealContext;
import com.light.aiszzy.resultUploadFile.deal.IResultUploadFileDealService;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileConditionBo;
import com.light.aiszzy.resultUploadFile.entity.dto.ResultUploadFileDto;
import com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo;
import com.light.aiszzy.resultUploadFile.mapper.ResultUploadFileMapper;
import com.light.aiszzy.utils.ImageUtils;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.attachment.service.AttachmentApiService;
import com.light.beans.HwlOpenComeducationHandTextVO;
import com.light.beans.ImageRequestBo;
import com.light.beans.Point;
import com.light.beans.Rect;
import com.light.contants.AISzzyConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.BeanUtils;
import com.light.core.utils.StringUtils;
import com.light.doubao.DouBaoApi;
import com.light.doubao.DouBaoCorrectResult;
import com.light.enums.CorrectResultEnum;
import com.light.enums.Enable;
import com.light.ocr.tencent.TencentOcrApi;
import com.light.utils.StuQuesResultMqUtil;
import com.tencentcloudapi.ocr.v20181119.models.Coord;
import com.tencentcloudapi.ocr.v20181119.models.GeneralHandwritingOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.TextGeneralHandwriting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 扫描处理图片实现
 */
@Service
@Slf4j
public class IResultUploadFileDealServiceImpl implements IResultUploadFileDealService {

    /**
     * 角度90度
     */
    private static final int ANGLE_90 = 90;
    /**
     * 角度180度
     */
    private static final int ANGLE_180 = 180;
    /**
     * 角度270度
     */
    private static final int ANGLE_270 = 270;
    /**
     * 图片后缀
     */
    private static final String ImageType = "jpg";
    /**
     * 文件类型
     */
    private static final String FileType = "file";
    /**
     * 目标图片名称格式
     */
    private static final String DEST_IMAGE_NAME_FORMAT = "dest_%s_%d_%d.%s";
    /**
     * 目标图片仅保留红色部分名称格式
     */
    private static final String DEST_RED_IMAGE_NAME_FORMAT = "dest_red_%s_%d_%d.%s";

    /**
     * 截图图片区域
     * %s 图片url
     * 第一个%d：截取结果图片宽
     * 第二个%d：截取结果图片高
     * 第三个%d： x
     * 第四个%d: y
     */
    private static final String IMAGE_CUT_URL_FORMAT = "%s?imageMogr2/cut/%dx%dx%dx%d";
    /**
     * 选择题
     */
    private static final String QUESTION_TYPE_NAME = "选择题@单选题@单选@听力选择@多选单题@阅读单选@单项选择@不定项选择题@多选@多选题";

    /**
     * 豆包聊天api
     */
    @Resource
    private DouBaoApi doubaoApi;
    /**
     * 腾讯OCR识别API
     */
    @Resource
    private TencentOcrApi tencentOcrApi;
    /**
     * 扫描处理图片Mapper
     */
    @Resource
    private ResultUploadFileMapper resultUploadFileMapper;

    /**
     * 作业本Mapper
     */
    @Resource
    private HomeworkMapper homeworkMapper;

    /**
     * 作业本页面Mapper
     */
    @Resource
    private HomeworkPageMapper homeworkPageMapper;

    /**
     * 作业本题目Mapper
     */
    @Resource
    private HomeworkQuestionMapper homeworkQuestionMapper;

    /**
     * 附件服务
     */
    @Resource
    private AttachmentApiService attachmentApiService;

    @Resource
    private HwlService hwlService;

    /**
     * 作业结果服务
     */
    @Resource
    private IHomeworkResultService homeworkResultService;
    /**
     * 作业结果页服务
     */
    @Resource
    private IHomeworkPageResultService homeworkPageResultService;
    /**
     * 作业结果答案服务
     */
    @Resource
    private IHomeworkResultAnswerService homeworkResultAnswerService;
    /**
     * 学号最小长度
     */
    private static final int STU_NO_MIN_LENGTH = 3;

    @Override
    public void dealResultUploadFile() {
        // 获取待处理图片列表
        List<ResultUploadFileVo> toDealFileList = getToDealFileList();

        for (ResultUploadFileVo resultUploadFileVo : toDealFileList) {
            // 1、创建处理上下文
            ResultUploadFileDealContext dealContext = new ResultUploadFileDealContext();
            dealContext.setResultUploadFileVo(resultUploadFileVo);

            try {
                // 2、解析二维码
                parseQrCode(dealContext);
            } catch (IOException e) {
                log.error("处理图片异常", e);
                dealContext.setDealFailed(true);
                dealContext.setDealDetail("处理图片异常");
            }
            if (dealContext.isDealFailed()) {
                // 更新扫描图片处理状态为处理失败
                updateDealStatus(dealContext);
                continue;
            }

            // 3、组装上下文
            assembleContext(dealContext);
            if (dealContext.isDealFailed()) {
                // 更新扫描图片处理状态为处理失败
                updateDealStatus(dealContext);
                continue;
            }

            // 4、将原始图片旋转到正，并设置正确顺序
            rotateImageToDest(dealContext);
            if (dealContext.isDealFailed()) {
                // 更新扫描图片处理状态为处理失败
                updateDealStatus(dealContext);
                continue;
            }

            // 5、上传旋转后图片到服务器
            uploadDestImages(dealContext);
            if (dealContext.isDealFailed()) {
                // 更新扫描图片处理状态为处理失败
                updateDealStatus(dealContext);
                continue;
            }


            // 6、批改作业，批改后存储作业结果
            correctHomeworkQuestions(dealContext);
            if (dealContext.isDealFailed()) {
                // 更新扫描图片处理状态为处理失败
                updateDealStatus(dealContext);
                continue;
            }

            // 7、组装作业结果并落库
            saveHomeworkAnswers(dealContext);

            // 8、回收内存
            recycle(dealContext);

        }

    }

    /**
     * 组装并落库处理结果、作业结果、作业答案等数据
     *
     * @param dealContext 处理上下文
     */
    private void saveHomeworkAnswers(ResultUploadFileDealContext dealContext) {
        // TODO 1、用户信息


        // 2、作业结果DTO
        assembleHomeworkResultDtoBaseInfo(dealContext);

        // 3、组装作业页结果DTO
        assembleHomeworkPageResultDto(dealContext);

        // 4、组装作业结果DTO数量等信息，依赖结果页中统计数量
        assembleHomeworkResultDtoNums(dealContext);

        // TODO 加事务处理

        // 插入或更新作业结果
        if (StringUtils.isEmpty(dealContext.getHomeworkResultDto().getOid())) {
            // 设置作业结果DTO OID
            dealContext.getHomeworkResultDto().setOid(IdUtil.fastSimpleUUID());
            homeworkResultService.save(dealContext.getHomeworkResultDto());
        } else {
            homeworkResultService.updateById(dealContext.getHomeworkResultDto());
        }

        // 插入作业页结果
        dealContext.getHomeworkPageResultDto().setHomeworkResultOid(dealContext.getHomeworkResultDto().getOid());
        homeworkPageResultService.save(dealContext.getHomeworkPageResultDto());


        // 设置作业结果DTO
//        dealContext.setHomeworkResultDto(homeworkResultDto);

        // 2、保存作业题目答案
        homeworkResultAnswerService.saveBatch(dealContext.getPageOneHomeworkResultAnswerDtoList());
        if (dealContext.getPageTwoHomeworkResultAnswerDtoList() != null && !dealContext.getPageTwoHomeworkResultAnswerDtoList().isEmpty()) {
            homeworkResultAnswerService.saveBatch(dealContext.getPageTwoHomeworkResultAnswerDtoList());
        }

        // 3、更新状态 处理成功
        dealContext.setDealDetail("处理成功");
        updateDealStatus(dealContext);


        // 4、发消息队列
        dealContext.getPageOneHomeworkResultAnswerDtoList().forEach(homeworkResultAnswerDto -> StuQuesResultMqUtil.publishMessage(JSON.toJSONString(homeworkResultAnswerDto)));
        if (dealContext.getPageTwoHomeworkResultAnswerDtoList() != null && !dealContext.getPageTwoHomeworkResultAnswerDtoList().isEmpty()) {
            dealContext.getPageTwoHomeworkResultAnswerDtoList().forEach(homeworkResultAnswerDto -> StuQuesResultMqUtil.publishMessage(JSON.toJSONString(homeworkResultAnswerDto)));
        }
    }

    /**
     * 组装作业结果DTO
     *
     * @param dealContext 处理上下文
     */
    private void assembleHomeworkResultDtoBaseInfo(ResultUploadFileDealContext dealContext) {

        // 学生OID不为空，则先查询作业信息是否已创建
        HomeworkResultDto homeworkResultDto = null;
        if (StringUtils.isNotEmpty(dealContext.getStuOid())) {
            QueryWrapper<HomeworkResultDto> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(HomeworkResultDto::getHomeworkOid, dealContext.getHomeworkDto().getOid());
            queryWrapper.lambda().eq(HomeworkResultDto::getStuOid, dealContext.getStuOid());
            queryWrapper.lambda().eq(HomeworkResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            homeworkResultDto = homeworkResultService.getOne(queryWrapper);
        }

        // 组装DealResult
        DealResultBo dealResultBo = new DealResultBo();
        BeanUtils.copyProperties(dealContext, dealResultBo);

        // 结果对象存在，则更新
        if (homeworkResultDto != null) {
            // 设置作业页码
            if (dealContext.getHomeworkPageDtoTwo() != null) {
                homeworkResultDto.setPageNos(homeworkResultDto.getPageNos() + AISzzyConstants.SPLIT_CHAR + dealContext.getPageNo() + AISzzyConstants.SPLIT_CHAR + (dealContext.getPageNo() + 1));
            } else {
                homeworkResultDto.setPageNos(homeworkResultDto.getPageNos() + AISzzyConstants.SPLIT_CHAR + dealContext.getPageNo());
            }
            homeworkResultDto.setStuAnswerUrls(homeworkResultDto.getStuAnswerUrls() + AISzzyConstants.SPLIT_CHAR + dealContext.getDestBufferedImageOneUrl() + AISzzyConstants.SPLIT_CHAR + dealContext.getDestBufferedImageTwoUrl());

            List<DealResultBo> dealResultBoList = JSON.parseArray(homeworkResultDto.getStuAnswerPageInfoJson(), DealResultBo.class);
            dealResultBoList.add(dealResultBo);
            // 解析出来的重要过程信息
            homeworkResultDto.setStuAnswerPageInfoJson(JSON.toJSONString(dealResultBoList));
        } else {
            // 结果对象为空，则创建对象
            homeworkResultDto = new HomeworkResultDto();
            // 此处不设置OID，用于区分新建的作业结果
//            homeworkResultDto.setOid(IdUtil.getSnowflakeNextIdStr());
            homeworkResultDto.setHomeworkOid(dealContext.getHomeworkDto().getOid());
            homeworkResultDto.setOrgCode(dealContext.getHomeworkDto().getOrgCode());
            homeworkResultDto.setGrade(dealContext.getHomeworkDto().getGrade());
            homeworkResultDto.setClassId(dealContext.getClassId());
            homeworkResultDto.setStuOid(dealContext.getStuOid());
            homeworkResultDto.setStuNo(dealContext.getOcrStuNo());
            homeworkResultDto.setStuName(dealContext.getOcrStuName());

            // 设置作业页码
            if (dealContext.getHomeworkPageDtoTwo() != null) {
                homeworkResultDto.setPageNos(dealContext.getPageNo() + AISzzyConstants.SPLIT_CHAR + (dealContext.getPageNo() + 1));
            } else {
                homeworkResultDto.setPageNos(String.valueOf(dealContext.getPageNo()));
            }
            homeworkResultDto.setStuAnswerUrls(dealContext.getDestBufferedImageOneUrl() + AISzzyConstants.SPLIT_CHAR + dealContext.getDestBufferedImageTwoUrl());

            // 组装DealResult
            List<DealResultBo> dealResultBos = new ArrayList<>();
            dealResultBos.add(dealResultBo);
            // 解析出来的重要过程信息
            homeworkResultDto.setStuAnswerPageInfoJson(JSON.toJSONString(dealResultBos));
        }

        dealContext.setHomeworkResultDto(homeworkResultDto);
    }

    /**
     * 设置作业结果DTO的正确、错误、未知等题目数
     *
     * @param dealContext 处理上下文
     */
    private static void assembleHomeworkResultDtoNums(ResultUploadFileDealContext dealContext) {
        HomeworkResultDto homeworkResultDto = dealContext.getHomeworkResultDto();

        // 设置正确、错误、未知
        homeworkResultDto.setRightNum(Optional.ofNullable(homeworkResultDto.getRightNum()).orElse(0) + dealContext.getRightNum());
        homeworkResultDto.setWrongNum(Optional.ofNullable(homeworkResultDto.getWrongNum()).orElse(0) + dealContext.getWrongNum());
        homeworkResultDto.setUnknownNum(Optional.ofNullable(homeworkResultDto.getUnknownNum()).orElse(0) + dealContext.getUnknownNum());
        // 设置作业总题数
        homeworkResultDto.setTotalNum(dealContext.getHomeworkDto().getQuestionNum());
        // 计算正确率
        homeworkResultDto.setAccuracyRate(homeworkResultDto.getTotalNum() > 0 ? homeworkResultDto.getRightNum() * 10000 / homeworkResultDto.getTotalNum() : null);

        // 设置完成状态，根据总题目数和错误、正确、未知题数关系判断
        if (homeworkResultDto.getTotalNum() < homeworkResultDto.getRightNum() + homeworkResultDto.getWrongNum() + homeworkResultDto.getUnknownNum()) {
            homeworkResultDto.setIsComplete(Enable.NO.getValue());
        } else {
            homeworkResultDto.setIsComplete(Enable.YES.getValue());
        }
    }

    /**
     * 更新文件处理状态
     *
     * @param dealContext 处理上下文
     */
    private void updateDealStatus(ResultUploadFileDealContext dealContext) {
        if (dealContext.isDealFailed()) {
            dealContext.getResultUploadFileVo().setIsDeal(AISzzyConstants.DealStatus.DEAL_ERROR.getCode());
        } else {
            dealContext.getResultUploadFileVo().setIsDeal(AISzzyConstants.DealStatus.DEAL_SUCCESS.getCode());
        }
        dealContext.getResultUploadFileVo().setDealDetail(dealContext.getDealDetail());

        //更新处理状态
        ResultUploadFileDto resultUploadFileDto = new ResultUploadFileDto();
        BeanUtils.copyProperties(dealContext.getResultUploadFileVo(), resultUploadFileDto);
        resultUploadFileMapper.updateById(resultUploadFileDto);

        // 释放图片文件
        recycle(dealContext);
    }

    /**
     * 释放资源
     *
     * @param dealContext 处理上下文
     */
    private void recycle(ResultUploadFileDealContext dealContext) {
        if (dealContext.getSrcBufferedImageOne() != null) {
            dealContext.getSrcBufferedImageOne().flush();
        }
        if (dealContext.getSrcBufferedImageTwo() != null) {
            dealContext.getSrcBufferedImageTwo().flush();
        }
        if (dealContext.getDestBufferedImageOne() != null) {
            dealContext.getDestBufferedImageOne().flush();
        }
        if (dealContext.getDestBufferedImageTwo() != null) {
            dealContext.getDestBufferedImageTwo().flush();
        }
        if (dealContext.getDestBufferedImageOneOnlyRed() != null) {
            dealContext.getDestBufferedImageOneOnlyRed().flush();
        }
        if (dealContext.getDestBufferedImageTwoOnlyRed() != null) {
            dealContext.getDestBufferedImageTwoOnlyRed().flush();
        }
    }

    /**
     * 上传处理后的图片
     *
     * @param dealContext 处理上下文
     */
    private void uploadDestImages(ResultUploadFileDealContext dealContext) {
        // 上传第一张图片
        String destImageNameOne = String.format(DEST_IMAGE_NAME_FORMAT, dealContext.getHomeworkCode(), System.currentTimeMillis(), dealContext.getPageNo(), ImageType);
        String destOneUrl = uploadFile2Server(dealContext, dealContext.getDestBufferedImageOne(), destImageNameOne);
        if (StringUtils.isEmpty(destOneUrl)) {
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("上传图片到服务器失败");
            return;
        }
        dealContext.setDestBufferedImageOneUrl(destOneUrl);

        // 上传第二张图片
        String destImageNameTwo = String.format(DEST_IMAGE_NAME_FORMAT, dealContext.getHomeworkCode(), System.currentTimeMillis(), dealContext.getPageNo() + 1, ImageType);
        String destTwoUrl = uploadFile2Server(dealContext, dealContext.getDestBufferedImageTwo(), destImageNameTwo);
        if (StringUtils.isEmpty(destTwoUrl)) {
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("上传图片到服务器失败");
            return;
        }
        dealContext.setDestBufferedImageTwoUrl(destTwoUrl);

    }

    /**
     * 上传图片到服务器
     *
     * @param dealContext 处理上下文
     * @param imageName   图片名称
     */
    private String uploadFile2Server(ResultUploadFileDealContext dealContext, BufferedImage bufferedImage, String imageName) {
        String uploadUrl = null;

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(bufferedImage, ImageType, baos);

            MultipartFile multipartFile = new MockMultipartFile(FileType, imageName, MediaType.MULTIPART_FORM_DATA_VALUE, baos.toByteArray());
            AjaxResult<AttachmentVo> attachmentResult = this.attachmentApiService.uploadFileFromPath(multipartFile, "/file/homework");
            if (attachmentResult.isSuccess()) {
                AttachmentVo data = attachmentResult.getData();
                uploadUrl = data.getNginxUrl();
            }
        } catch (IOException e) {
            log.error("上传文件服务器异常", e);
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("上传文件服务器异常");
        } finally {
            try {
                baos.close();
            } catch (IOException e) {
                log.error("关闭字节流失败", e);
                dealContext.setDealFailed(true);
                dealContext.setDealDetail("上传文件服务器异常");
            }
        }
        return uploadUrl;
    }

    /**
     * 获取待处理图片列表
     *
     * @return 待处理图片列表
     */
    private List<ResultUploadFileVo> getToDealFileList() {
        ResultUploadFileConditionBo condition = new ResultUploadFileConditionBo();
        condition.setIsDeal(AISzzyConstants.DealStatus.NOT_DEAL.getCode());
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrderBy("id");
        return resultUploadFileMapper.getResultUploadFileListByCondition(condition);
    }

    /**
     * 解析二维码，组装context
     *
     * @param dealContext 图片处理上下文信息
     */
    private void parseQrCode(ResultUploadFileDealContext dealContext) throws IOException {
        ResultUploadFileVo resultUploadFileVo = dealContext.getResultUploadFileVo();
        // 设置图片
        dealContext.setSrcBufferedImageOne(ImageIO.read(new URL(dealContext.getResultUploadFileVo().getPathOne())));
        dealContext.setSrcBufferedImageTwo(ImageIO.read(new URL(dealContext.getResultUploadFileVo().getPathTwo())));

        // 图片是否包含二维码标记
        try {
            // 识别第一张图片二维码信息
            String qrDecode = QrCodeUtil.decode(dealContext.getSrcBufferedImageOne());
            dealContext.setQrCodeFirstFlag(true);
            if (StringUtils.isEmpty(qrDecode)) {
                // 识别第二张图片二维码信息
                qrDecode = QrCodeUtil.decode(dealContext.getSrcBufferedImageTwo());
                dealContext.setQrCodeFirstFlag(false);
            }
            dealContext.setQrDeCode(qrDecode);
            log.info("图片二维码信息：{}", qrDecode);
        } catch (Exception e) {
            log.warn("解析二维码信息失败", e);
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("解析二维码信息失败");
            return;
        }
        if (StringUtils.isEmpty(dealContext.getQrDeCode())) {
            log.warn("图片二维码信息为空，图片 ID: {}, 文件1地址：{}, 文件2地址：{}", resultUploadFileVo.getId(), resultUploadFileVo.getPathOne(), resultUploadFileVo.getPathTwo());
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("未识别到二维码信息");
            return;
        }

        // 拆分二维码字符串
        String[] split = StringUtils.split(dealContext.getQrDeCode(), "#");
        if (split == null || split.length != 3) {
            log.warn("图片二维码信息错误，图片 ID: {}, 文件1地址：{}, 文件2地址：{}", resultUploadFileVo.getId(), resultUploadFileVo.getPathOne(), resultUploadFileVo.getPathTwo());
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("非法的二维码信息");
            return;
        }

        dealContext.setType(split[0]);
        dealContext.setHomeworkCode(split[1]);
        dealContext.setPageNo(Integer.valueOf(split[2]));
    }

    /**
     * 组装上下文信息
     *
     * @param dealContext 上下文信息
     */
    private void assembleContext(ResultUploadFileDealContext dealContext) {
        // 打印作业类型，TODO 修改枚举
        if ("1".equals(dealContext.getType())) {
            // 1、根据二维码code获取作业信息
            HomeworkDto homeworkDto = homeworkMapper.selectOne(new LambdaQueryWrapper<HomeworkDto>()
                    .eq(HomeworkDto::getCode, dealContext.getHomeworkCode()).eq(HomeworkDto::getIsDelete, StatusEnum.NOTDELETE));
            // 作业不存在
            if (homeworkDto == null) {
                log.warn("作业信息不存在，作业code: {}", dealContext.getHomeworkCode());
                dealContext.setDealFailed(true);
                dealContext.setDealDetail("作业信息不存在");
                return;
            }

            // 设置作业信息
            dealContext.setHomeworkDto(homeworkDto);

            // 2、作业page信息
            List<HomeworkPageDto> homeworkPageDtoList = homeworkPageMapper.selectList(new LambdaQueryWrapper<HomeworkPageDto>().eq(HomeworkPageDto::getHomeworkCode, dealContext.getHomeworkCode())
                    .eq(HomeworkPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode()).between(HomeworkPageDto::getPageNo, dealContext.getPageNo(), dealContext.getPageNo() + 1));
            if (homeworkPageDtoList == null || homeworkPageDtoList.isEmpty() || homeworkPageDtoList.size() > 2) {
                log.warn("未查到作业页信息，homeworkCode: {}", dealContext.getHomeworkCode());
                dealContext.setDealFailed(true);
                dealContext.setDealDetail("未查到作业页信息");
                return;
            }
            for (HomeworkPageDto homeworkPageDto : homeworkPageDtoList) {
                if (dealContext.getPageNo().equals(homeworkPageDto.getPageNo())) {
                    // 设置作业第一页
                    dealContext.setHomeworkPageDtoOne(homeworkPageDto);
                } else if (dealContext.getPageNo() + 1 == homeworkPageDto.getPageNo()) {
                    // 设置作业第二页
                    dealContext.setHomeworkPageDtoTwo(homeworkPageDto);
                }
            }

            // 3、作业题目信息
            List<HomeworkQuestionDto> homeworkQuestionDtoList = homeworkQuestionMapper.selectList(
                    new LambdaQueryWrapper<HomeworkQuestionDto>().eq(HomeworkQuestionDto::getHomeworkOid, homeworkDto.getOid())
                            .eq(HomeworkQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

            if (homeworkQuestionDtoList == null || homeworkQuestionDtoList.isEmpty()) {
                log.warn("未查到作业题目信息，homeworkOid: {}", homeworkDto.getOid());
                dealContext.setDealFailed(true);
                dealContext.setDealDetail("未查到作业题目信息");
                return;
            }
            // 作业题目map，key为作业题目OID
            Map<String, HomeworkQuestionDto> homeworkQuestionDtoMap = homeworkQuestionDtoList.stream().collect(Collectors.toMap(HomeworkQuestionDto::getOid, e -> e));
            dealContext.setHomeworkQuestionDtoMap(homeworkQuestionDtoMap);
        } else if ("2".equals(dealContext.getType())) {
            // TODO 修改枚举，查询practice表？？？

        }

    }

    /**
     * 将原始图片旋转到正，并设置正确序号
     *
     * @param dealContext 处理上下文
     */
    private void rotateImageToDest(ResultUploadFileDealContext dealContext) {
        // 包含二维码的原始图片
        BufferedImage qrCodeImage = dealContext.getQrCodeFirstFlag() ? dealContext.getSrcBufferedImageOne() : dealContext.getSrcBufferedImageTwo();
        BufferedImage noQrCodeImage = dealContext.getQrCodeFirstFlag() ? dealContext.getSrcBufferedImageTwo() : dealContext.getSrcBufferedImageOne();

        // 计算中心点坐标
        int centerX = qrCodeImage.getWidth() / 2;
        int centerY = qrCodeImage.getHeight() / 2;

        switch (dealContext.getHomeworkDto().getPageSize()) {
            case AISzzyConstants.PAGE_SIZE_A4:
                // 左上包含二维码
                if (StringUtils.isNotEmpty(QrCodeUtil.decode(qrCodeImage.getSubimage(0, 0, centerX, centerY)))) {
                    dealContext.setDestBufferedImageOne(qrCodeImage);
                    dealContext.setDestBufferedImageTwo(noQrCodeImage);
                } else if (StringUtils.isNotEmpty(QrCodeUtil.decode(qrCodeImage.getSubimage(centerX, centerY, centerX, centerY)))) {
                    // 右下包含二维码
                    dealContext.setDestBufferedImageOne(ImageUtils.rotateImage(qrCodeImage, ANGLE_180));
                    dealContext.setDestBufferedImageTwo(ImageUtils.rotateImage(noQrCodeImage, ANGLE_180));
                } else if (StringUtils.isNotEmpty(QrCodeUtil.decode(qrCodeImage.getSubimage(centerX, 0, centerX, centerY)))) {
                    // 右上包含二维码
                    dealContext.setDestBufferedImageOne(ImageUtils.rotateImage(qrCodeImage, ANGLE_270));
                    dealContext.setDestBufferedImageTwo(ImageUtils.rotateImage(noQrCodeImage, ANGLE_90));
                } else if (StringUtils.isNotEmpty(QrCodeUtil.decode(qrCodeImage.getSubimage(0, centerY, centerX, centerY)))) {
                    // 左下包含二维码
                    dealContext.setDestBufferedImageOne(ImageUtils.rotateImage(qrCodeImage, ANGLE_90));
                    dealContext.setDestBufferedImageTwo(ImageUtils.rotateImage(noQrCodeImage, ANGLE_270));
                }
                break;
            case AISzzyConstants.PAGE_SIZE_A3:
                if (StringUtils.isNotEmpty(QrCodeUtil.decode(qrCodeImage.getSubimage(centerX, 0, centerX, centerY)))) {
                    // 右上包含二维码
                    dealContext.setDestBufferedImageOne(ImageUtils.rotateImage(qrCodeImage, ANGLE_270));
                    dealContext.setDestBufferedImageTwo(ImageUtils.rotateImage(noQrCodeImage, ANGLE_90));
                } else if (StringUtils.isNotEmpty(QrCodeUtil.decode(qrCodeImage.getSubimage(0, centerY, centerX, centerY)))) {
                    // 左下包含二维码
                    dealContext.setDestBufferedImageOne(ImageUtils.rotateImage(qrCodeImage, ANGLE_90));
                    dealContext.setDestBufferedImageTwo(ImageUtils.rotateImage(noQrCodeImage, ANGLE_270));
                } else if (StringUtils.isNotEmpty(QrCodeUtil.decode(qrCodeImage.getSubimage(0, 0, centerX, centerY)))) {
                    dealContext.setDestBufferedImageOne(qrCodeImage);
                    dealContext.setDestBufferedImageTwo(noQrCodeImage);
                } else if (StringUtils.isNotEmpty(QrCodeUtil.decode(qrCodeImage.getSubimage(centerX, centerY, centerX, centerY)))) {
                    // 右下包含二维码
                    dealContext.setDestBufferedImageOne(ImageUtils.rotateImage(qrCodeImage, ANGLE_180));
                    dealContext.setDestBufferedImageTwo(ImageUtils.rotateImage(noQrCodeImage, ANGLE_180));
                }
                break;
            default:
                log.warn("图片尺寸非A4或A3");
        }

        // 处理后图片为空
        if (dealContext.getDestBufferedImageOne() == null) {
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("图片尺寸非A4或A3");
        }

    }

    /**
     * 保留旋转后的图形红色部分
     *
     * @param dealContext 处理上下文
     */
    private void retainDestImageRedPixel(ResultUploadFileDealContext dealContext) {
        try {
            dealContext.setDestBufferedImageOneOnlyRed(ImageUtils.retainRedLine(dealContext.getDestBufferedImageOne()));
            dealContext.setDestBufferedImageTwoOnlyRed(ImageUtils.retainRedLine(dealContext.getDestBufferedImageTwo()));
        } catch (IOException e) {
            log.error("保留旋转后图形红色部分失败", e);
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("保留旋转后图形红色部分失败");
        }

    }

    /**
     * 批改作业
     *
     * @param dealContext 处理上下文
     */
    private void correctHomeworkQuestions(ResultUploadFileDealContext dealContext) {
        // 1、批改第一页题目
        List<HomeworkQuestionPosition> pageOneHomeworkQuestionPositionList = JSON.parseArray(dealContext.getHomeworkPageDtoOne().getQuestionJson(), HomeworkQuestionPosition.class);
        if (pageOneHomeworkQuestionPositionList == null || pageOneHomeworkQuestionPositionList.isEmpty()) {
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("作业第一页题目为空");
            return;
        }
        try {
            // 第一页图片留红
            dealContext.setDestBufferedImageOneOnlyRed(ImageUtils.retainRedLine(dealContext.getDestBufferedImageOne()));
            // 上传第一张图片
            String destImageNameOne = String.format(DEST_RED_IMAGE_NAME_FORMAT, dealContext.getHomeworkCode(), System.currentTimeMillis(), dealContext.getPageNo(), ImageType);
            String destOneRedUrl = uploadFile2Server(dealContext, dealContext.getDestBufferedImageOneOnlyRed(), destImageNameOne);
            if (StringUtils.isEmpty(destOneRedUrl)) {
                dealContext.setDealFailed(true);
                dealContext.setDealDetail("上传图片到服务器失败");
                return;
            }
            // 设置留红图片URL
            dealContext.setDestBufferedImageOneOnlyRedUrl(destOneRedUrl);
        } catch (IOException e) {
            log.error("上传图片到服务器失败", e);
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("上传图片到服务器失败");
            return;
        }

        // 判断题目是否正确
        List<HomeworkResultAnswerDto> pageOneResultAnswerDtoList = null;
        try {
            pageOneResultAnswerDtoList = judgeQuestion(dealContext, pageOneHomeworkQuestionPositionList, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        dealContext.setPageOneHomeworkResultAnswerDtoList(pageOneResultAnswerDtoList);
        // 设置题目数量
        dealContext.setHomeworkPageOneQuestionNum(pageOneHomeworkQuestionPositionList.size());

        // 2、批改第二页题目
        if (dealContext.getHomeworkPageDtoTwo() == null) {
            return;
        }
        List<HomeworkQuestionPosition> pageTwoHomeworkQuestionPositionList = JSON.parseArray(dealContext.getHomeworkPageDtoTwo().getQuestionJson(), HomeworkQuestionPosition.class);
        if (pageTwoHomeworkQuestionPositionList == null || pageTwoHomeworkQuestionPositionList.isEmpty()) {
            log.info("第二页题目为空");
            return;
        }
        try {
            // 第二页图片留红
            dealContext.setDestBufferedImageTwoOnlyRed(ImageUtils.retainRedLine(dealContext.getDestBufferedImageTwo()));
            // 上传第一张图片
            String destTwoRedImageName = String.format(DEST_RED_IMAGE_NAME_FORMAT, dealContext.getHomeworkCode(), System.currentTimeMillis(), dealContext.getPageNo() + 1, ImageType);
            String destTwoRedUrl = uploadFile2Server(dealContext, dealContext.getDestBufferedImageTwoOnlyRed(), destTwoRedImageName);
            if (StringUtils.isEmpty(destTwoRedUrl)) {
                dealContext.setDealFailed(true);
                dealContext.setDealDetail("上传图片到服务器失败");
                return;
            }
            // 设置留红图片URL
            dealContext.setDestBufferedImageTwoOnlyRedUrl(destTwoRedUrl);
        } catch (IOException e) {
            log.error("上传图片到服务器失败", e);
            dealContext.setDealFailed(true);
            dealContext.setDealDetail("上传图片到服务器失败");
            return;
        }
        List<HomeworkResultAnswerDto> pageTwoResultAnswerDtoList = null;
        try {
            pageTwoResultAnswerDtoList = judgeQuestion(dealContext, pageTwoHomeworkQuestionPositionList, false);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        dealContext.setPageTwoHomeworkResultAnswerDtoList(pageTwoResultAnswerDtoList);

        // 设置题目数量
        dealContext.setHomeworkPageTwoQuestionNum(pageTwoHomeworkQuestionPositionList.size());
    }

    /**
     * 组装作业结果页处理信息
     *
     * @param dealContext 处理上下文
     */
    private void assembleHomeworkPageResultDto(ResultUploadFileDealContext dealContext) {
        // 作业信息，此处已经设置作业基本信息
        HomeworkDto homeworkDto = dealContext.getHomeworkDto();

        // 题目批改结果
        List<HomeworkResultAnswerDto> resultAnswerDtoList = new ArrayList<>(dealContext.getPageOneHomeworkResultAnswerDtoList());
        if (dealContext.getPageTwoHomeworkResultAnswerDtoList() != null) {
            resultAnswerDtoList.addAll(dealContext.getPageTwoHomeworkResultAnswerDtoList());
        }

        // 统计题目批改结果
        for (HomeworkResultAnswerDto resultAnswerDto : resultAnswerDtoList) {
            // 统计
            if (Objects.equals(resultAnswerDto.getIsCorrect(), CorrectResultEnum.CORRECT.getCode())) {
                dealContext.setRightNum(dealContext.getRightNum() + 1);
                resultAnswerDto.setIsDoubt(AISzzyConstants.DoubtStatus.NO.getType());
            } else if (Objects.equals(resultAnswerDto.getIsCorrect(), CorrectResultEnum.WRONG.getCode())) {
                dealContext.setWrongNum(dealContext.getWrongNum() + 1);
                resultAnswerDto.setIsDoubt(AISzzyConstants.DoubtStatus.NO.getType());
            } else {
                dealContext.setUnknownNum(dealContext.getUnknownNum() + 1);
                resultAnswerDto.setIsDoubt(AISzzyConstants.DoubtStatus.EXIST.getType());
            }
            // 设置答案信息
            resultAnswerDto.setOrgCode(homeworkDto.getOrgCode());
            resultAnswerDto.setHomeworkResultOid(homeworkDto.getOrgCode());
            resultAnswerDto.setStuOid(dealContext.getStuOid());
            resultAnswerDto.setStuClassId(dealContext.getClassId());
            resultAnswerDto.setStuNo(dealContext.getOcrStuNo());
        }

        // 作业结果页信息
        HomeworkPageResultDto homeworkPageResultDto = new HomeworkPageResultDto();
        homeworkPageResultDto.setOid(IdUtil.fastSimpleUUID());
        homeworkPageResultDto.setHomeworkOid(homeworkDto.getOid());
        homeworkPageResultDto.setOrgCode(homeworkDto.getOrgCode());
        homeworkPageResultDto.setGrade(homeworkDto.getGrade());
        homeworkPageResultDto.setPageNo(dealContext.getPageNo());
        homeworkPageResultDto.setStuAnswerUrlOne(dealContext.getDestBufferedImageOneUrl());
        homeworkPageResultDto.setStuAnswerUrlTwo(dealContext.getDestBufferedImageTwoUrl());
        homeworkPageResultDto.setRightNum(dealContext.getRightNum());
        homeworkPageResultDto.setWrongNum(dealContext.getWrongNum());
        homeworkPageResultDto.setUnknownNum(dealContext.getUnknownNum());
        homeworkPageResultDto.setTotalNum(resultAnswerDtoList.size());

//        homeworkPageResultDto.setStuAnswerCorrectResult();
//        homeworkPageResultDto.setStuAnswerCorrectResultPrint();
//        homeworkPageResultDto.setStuAnswerPageInfoJson();
        //        homeworkPageResultDto.setIsDoubt();
//        homeworkPageResultDto.setDoubtType();

        // 设置学生信息
        homeworkPageResultDto.setOcrStuNo(dealContext.getOcrStuNo());
        homeworkPageResultDto.setOcrStuName(dealContext.getOcrStuName());

        // TODO
        // 学号长度小于3位进疑问项
        if (StringUtils.length(dealContext.getOcrStuNo()) < STU_NO_MIN_LENGTH) {
            homeworkPageResultDto.setIsDoubt(AISzzyConstants.DoubtStatus.EXIST.getType());
            homeworkPageResultDto.setDoubtType(AISzzyConstants.DoubtType.NOERROR.getType());
        } else if (StringUtils.length(dealContext.getOcrStuNo()) == STU_NO_MIN_LENGTH) {
            // 第一位为班级，后面为学号
            // TODO 获取班级信息和学生oid
            //        homeworkPageResultDto.setClassId();
//        homeworkPageResultDto.setStuOid();
        } else {
            // 前两位为班级，后面为学号
            // TODO 获取班级信息和学生oid
            //        homeworkPageResultDto.setClassId();
//        homeworkPageResultDto.setStuOid();
        }

        // 疑问项 重复判断，从库里查询 根据homeworkOid、学号、作业结果页码查询，确定是否存在，存在则疑问
        if (StringUtils.length(dealContext.getOcrStuNo()) >= STU_NO_MIN_LENGTH && Optional.ofNullable(dealContext.getPageNo()).orElse(0) > 0) {
            HomeworkPageResultConditionBo condition = new HomeworkPageResultConditionBo();
            condition.setHomeworkOid(homeworkDto.getOid());
            condition.setOcrStuNo(dealContext.getOcrStuNo());
            condition.setPageNo(dealContext.getPageNo());
            condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
            List<HomeworkPageResultVo> list = homeworkPageResultService.getHomeworkPageResultListByCondition(condition);
            if (list != null && list.size() > 0) {
                homeworkPageResultDto.setIsDoubt(AISzzyConstants.DoubtStatus.EXIST.getType());
                homeworkPageResultDto.setDoubtType(AISzzyConstants.DoubtType.REPEAT.getType());
            }
        }


        dealContext.setHomeworkPageResultDto(homeworkPageResultDto);
    }

    /**
     * 判断题目是否正确
     *
     * @param dealContext                  处理上下文
     * @param homeworkQuestionPositionList 作业题目位置列表。
     */
    private List<HomeworkResultAnswerDto> judgeQuestion(ResultUploadFileDealContext dealContext, List<HomeworkQuestionPosition> homeworkQuestionPositionList, boolean isOne) throws IOException {
        List<HomeworkResultAnswerDto> resultAnswerDtoList = new ArrayList<>();
        //需要OCR识别判断对错的list
        List<HomeworkResultAnswerDto> resultAnswerOCRList = new ArrayList<>();
        // 按比例计算位置及宽高
        double scale = 1.0 * dealContext.getDestBufferedImageOne().getWidth() / dealContext.getHomeworkDto().getPaperWidth();

        for (HomeworkQuestionPosition position : homeworkQuestionPositionList) {
            // 作业题目信息
            HomeworkQuestionDto homeworkQuestionDto = dealContext.getHomeworkQuestionDtoMap().get(position.getHomeworkQuestionOid());
            // 创建作答结果对象
            HomeworkResultAnswerDto homeworkResultAnswerDto = new HomeworkResultAnswerDto();
            BeanUtils.copyProperties(homeworkQuestionDto, homeworkResultAnswerDto);
            homeworkResultAnswerDto.setId(null);
            homeworkResultAnswerDto.setOid(IdUtil.simpleUUID());
            homeworkResultAnswerDto.setCreateBy(null);
            homeworkResultAnswerDto.setUpdateBy(null);
            homeworkResultAnswerDto.setCreateTime(null);
            homeworkResultAnswerDto.setUpdateTime(null);
            // TODO 这里是多个？？？
            homeworkResultAnswerDto.setPositions(homeworkQuestionDto.getPosition());
            homeworkResultAnswerDto.setStuPositions(homeworkQuestionDto.getPosition());
            // resultAnswerDtoList.add(homeworkResultAnswerDto);

            // 按比例计算位置及宽高
            int width = (int) (position.getRect().getW() * scale + 1);
            int height = (int) (position.getRect().getH() * scale + 1);
            int x = (int) (position.getRect().getX() * scale + 1);
            int y = (int) (position.getRect().getY() * scale + 1);

            Rect rect = new Rect(x, y, width, height);
            homeworkResultAnswerDto.setContentRect(rect);

            // 待处理仅留红的图片url
            String toDealRedOnlyImageUrl = isOne ? dealContext.getDestBufferedImageOneOnlyRedUrl() : dealContext.getDestBufferedImageTwoOnlyRedUrl();
            // 答案图片url
            String answerImageUrl = String.format(IMAGE_CUT_URL_FORMAT, toDealRedOnlyImageUrl, width, height, x, y);

            // 旋转后的学生答案图片url
            String stuAnswerImageUrl = isOne ? dealContext.getDestBufferedImageOneUrl() : dealContext.getDestBufferedImageTwoUrl();
            //结果图片存入
            homeworkResultAnswerDto.setStuAnswerUrls(String.format(IMAGE_CUT_URL_FORMAT, stuAnswerImageUrl, width, height, x, y));

            try {
                // 调用豆包处理判断是否正确
                DouBaoCorrectResult douBaoCorrectResult = doubaoApi.correctResult(answerImageUrl);
                homeworkResultAnswerDto.setIsCorrect(douBaoCorrectResult.getContent());
            } catch (IOException | URISyntaxException e) {
                log.warn("调用豆包异常，url:{},error:", answerImageUrl, e);
                // 设置异常，交由OCR处理
                homeworkResultAnswerDto.setIsCorrect(CorrectResultEnum.UNKNOWN.getCode());
            }
            // 豆包给出明确结果，则继续处理下一题
            if (Objects.equals(homeworkResultAnswerDto.getIsCorrect(), CorrectResultEnum.CORRECT.getCode()) || Objects.equals(homeworkResultAnswerDto.getIsCorrect(), CorrectResultEnum.WRONG.getCode())) {
                resultAnswerDtoList.add(homeworkResultAnswerDto);
                continue;
            }
            // 豆包未给明确结果，如果题目为选择题，则调用腾讯手写体识别，比较答案，否则认为回复错误
            if (QUESTION_TYPE_NAME.contains(homeworkQuestionDto.getQuestionTypeName())) {
                resultAnswerOCRList.add(homeworkResultAnswerDto);
            } else {
                //没有批红，也不是客观题，则默认错误
                homeworkResultAnswerDto.setIsCorrect(CorrectResultEnum.WRONG.getCode());
                resultAnswerDtoList.add(homeworkResultAnswerDto);
            }

        }

        //tencentOCRJudgeQuestion(dealContext, resultAnswerOCRList, destImage, isOne, scale);

        // 旋转后的图片
        BufferedImage destImage = isOne ? dealContext.getDestBufferedImageOne() : dealContext.getDestBufferedImageTwo();
        //调用好未来OCR识别学号、用户名、客观题等
        hwlOCRJudgeQuestion(dealContext, resultAnswerOCRList, destImage, isOne, scale);

        resultAnswerDtoList.addAll(resultAnswerOCRList);

        return resultAnswerDtoList;
    }

    /**
     * 通过好未来OCR判断题目是否正确
     *
     * @param dealContext         处理上下文
     * @param resultAnswerOCRList 作业题目位置列表。
     * @param destImage           目标图片URL
     */
    private List<HomeworkResultAnswerDto> hwlOCRJudgeQuestion(ResultUploadFileDealContext dealContext, List<HomeworkResultAnswerDto> resultAnswerOCRList, BufferedImage destImage, boolean isOne, double scale) throws IOException {
        //如果不是首页并且没有需要OCR题目，则返回
        if (!isOne && (0 == resultAnswerOCRList.size())) {
            return null;
        }

        BufferedImage resultOCRImage = new BufferedImage(destImage.getWidth(), destImage.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g = resultOCRImage.createGraphics();

        Rect stuNoRect = JSONUtil.toBean(dealContext.getHomeworkDto().getUserIdRect(), Rect.class);
        Rect stuNameRect = JSONUtil.toBean(dealContext.getHomeworkDto().getUserNameRect(), Rect.class);
        //取出头部班级学号和姓名信息
        if (isOne) {
            // 按比例计算位置及宽高
            String headImageStuNoUrl = String.format(IMAGE_CUT_URL_FORMAT, dealContext.getDestBufferedImageOneUrl(), (int) (stuNoRect.getW() * scale), (int) (stuNoRect.getH() * scale), (int) (stuNoRect.getX() * scale), (int) (stuNoRect.getY() * scale));
            String headImageStuNameUrl = String.format(IMAGE_CUT_URL_FORMAT, dealContext.getDestBufferedImageOneUrl(), (int) (stuNameRect.getW() * scale), (int) (stuNameRect.getH() * scale), (int) (stuNameRect.getX() * scale), (int) (stuNameRect.getY() * scale));
            Image headImageStuNo = ImageIO.read(new URL(headImageStuNoUrl));
            Image headImageStuName = ImageIO.read(new URL(headImageStuNameUrl));
            g.drawImage(headImageStuNo, (int) (stuNoRect.getX() * scale), (int) (stuNoRect.getY() * scale), null);
            g.drawImage(headImageStuName, (int) (stuNameRect.getX() * scale), (int) (stuNameRect.getY() * scale), null);
        }

        if (resultAnswerOCRList.size() > 0) {

            for (HomeworkResultAnswerDto resultAnswerDto : resultAnswerOCRList) {
                Image answerUrl = ImageIO.read(new URL(resultAnswerDto.getStuAnswerUrls()));
                g.drawImage(answerUrl, resultAnswerDto.getContentRect().getX(), resultAnswerDto.getContentRect().getY(), null);
            }
            g.dispose();
        }

        try {
            String imageBase64 = ImageUtils.convertImageToBase64(resultOCRImage, "png");
            // 调用好未来OCR识别手写体
            ImageRequestBo imageRequestBo = new ImageRequestBo();
            imageRequestBo.setImageBase64(imageBase64);
            //-2 手写文字识别
            imageRequestBo.setFunction(2);
            AjaxResult handwritingOCR = hwlService.processEducationOcr(imageRequestBo);
            // 解析内容
            JSONObject handwritingObj = (JSONObject) handwritingOCR.getData();
            JSONObject singleBox = handwritingObj.getJSONObject("single_box");
            JSONArray handTextResult = singleBox.getJSONArray("hand_text");
            // 遍历数组中的每个元素（每个元素是 JSONObject）
            for (int i = 0; i < handTextResult.size(); i++) {
                // 从 JSONArray 中获取 JSONObject
                HwlOpenComeducationHandTextVO hwlOpenComeducationHandTextVO = JSON.parseObject(JSON.toJSONString(handTextResult.getJSONObject(i)), HwlOpenComeducationHandTextVO.class);
                System.out.println("\nhandwritingOCR:\n" + hwlOpenComeducationHandTextVO.toString());

                //            // 识别到的文本
                //获取坐标中心点位置坐标
                List<Point> pointList = hwlOpenComeducationHandTextVO.getPoses();
                Integer minx = pointList.get(0).getX();
                Integer miny = pointList.get(0).getY();
                Integer maxx = pointList.get(0).getX();
                Integer maxy = pointList.get(0).getY();
                for (Point point : pointList) {
                    //分别获取最小的和最大的X坐标
                    if (point.getX() < minx) {
                        minx = point.getX();
                    } else if (point.getX() > maxx) {
                        maxx = point.getX();
                    }
                    //分别获取最小的和最大的Y坐标
                    if (point.getY() < miny) {
                        miny = point.getY();
                    } else if (point.getY() > maxy) {
                        maxy = point.getY();
                    }
                }
                int x = (int) (maxx + minx) / 2;
                int y = (int) (maxy + miny) / 2;


                //取出头部班级学号和姓名信息
                if (isOne) {
                    // 按比例计算位置及宽高
                    if ((x > (int) (stuNoRect.getX() * scale)) && (x < (int) (stuNoRect.getX() * scale) + (int) (stuNoRect.getW() * scale))
                            && (y > (int) (stuNoRect.getY() * scale)) && (y < (int) (stuNoRect.getY() * scale) + (int) (stuNoRect.getH() * scale))) {
                        //如果是数字，则算作学号
                        String orcStuNo = StringUtils.extractDigitsWithStringBuilder(hwlOpenComeducationHandTextVO.getTexts().trim());
                        if (StringUtils.isNotBlank(orcStuNo)) {
                            //如果分多次，则合并
                            if (StringUtils.isNotBlank(dealContext.getOcrStuNo())) {
                                dealContext.setOcrStuNo(dealContext.getOcrStuNo() + orcStuNo);
                            } else {
                                dealContext.setOcrStuNo(orcStuNo);
                            }
                        }
                        continue;
                    }

                    if ((x > (int) (stuNameRect.getX() * scale)) && (x < (int) (stuNameRect.getX() * scale) + (int) (stuNameRect.getW() * scale))
                            && (y > (int) (stuNameRect.getY() * scale)) && (y < (int) (stuNameRect.getY() * scale) + (int) (stuNameRect.getH() * scale))) {
                        //如果分多次，则合并
                        if (StringUtils.isNotBlank(dealContext.getOcrStuName())) {
                            dealContext.setOcrStuName(dealContext.getOcrStuName() + hwlOpenComeducationHandTextVO.getTexts().trim());
                        } else {
                            dealContext.setOcrStuName(hwlOpenComeducationHandTextVO.getTexts().trim());
                        }
                        continue;
                    }
                }

                for (HomeworkResultAnswerDto resultAnswerDto : resultAnswerOCRList) {
                    //如果识别内容的中心坐标点在题目划定范围内，则被认为属于答案
                    if ((x > resultAnswerDto.getContentRect().getX()) && (x < (resultAnswerDto.getContentRect().getX() + resultAnswerDto.getContentRect().getW()) / 2)
                            && (y > resultAnswerDto.getContentRect().getY()) && (y < (resultAnswerDto.getContentRect().getY() + resultAnswerDto.getContentRect().getH()) / 2)) {
                        // 识别到的文本并且全部是大写字母，才放入答案区
                        String answer = StringUtils.extractUppercaseLettersWithSB(hwlOpenComeducationHandTextVO.getTexts().trim());
                        if (StringUtils.isNotBlank(resultAnswerDto.getStuAnswer())) {
                            resultAnswerDto.setStuAnswer(resultAnswerDto.getStuAnswer() + answer);
                        } else {
                            resultAnswerDto.setStuAnswer(answer);
                        }
                        break;
                    }
                }

            }

            for (HomeworkResultAnswerDto resultAnswerDto : resultAnswerOCRList) {
                if (StringUtils.isAllUpperCase(resultAnswerDto.getQuesAnswer())) {
                    if (StringUtils.equals(resultAnswerDto.getStuAnswer(), resultAnswerDto.getQuesAnswer().trim())) {
                        resultAnswerDto.setIsCorrect(CorrectResultEnum.CORRECT.getCode());
                    } else {
                        resultAnswerDto.setIsCorrect(CorrectResultEnum.WRONG.getCode());
                    }
                } else {
                    String quesAnswer = StringUtils.removeHtmlTags(resultAnswerDto.getQuesAnswer());
                    quesAnswer = StringUtils.extractUppercaseLettersWithSB(quesAnswer);

                    if (StringUtils.equals(resultAnswerDto.getStuAnswer(), quesAnswer)) {
                        resultAnswerDto.setIsCorrect(CorrectResultEnum.CORRECT.getCode());
                    } else {
                        resultAnswerDto.setIsCorrect(CorrectResultEnum.WRONG.getCode());
                    }
                }


            }

        } catch (Exception e) {
            log.error("调用好未来OCR识别手写体异常,error:", e);
        }

        return resultAnswerOCRList;
    }

    /**
     * 通过腾讯OCR判断题目是否正确
     *
     * @param dealContext         处理上下文
     * @param resultAnswerOCRList 作业题目位置列表。
     * @param destImage           目标图片URL
     */
    private List<HomeworkResultAnswerDto> tencentOCRJudgeQuestion(ResultUploadFileDealContext dealContext, List<HomeworkResultAnswerDto> resultAnswerOCRList, BufferedImage destImage, boolean isOne, double scale) throws IOException {
        //如果不是首页并且没有需要OCR题目，则返回
        if (!isOne && (0 == resultAnswerOCRList.size())) {
            return null;
        }

        BufferedImage resultOCRImage = new BufferedImage(destImage.getWidth(), destImage.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g = resultOCRImage.createGraphics();

        Rect stuNoRect = JSONUtil.toBean(dealContext.getHomeworkDto().getUserIdRect(), Rect.class);
        Rect stuNameRect = JSONUtil.toBean(dealContext.getHomeworkDto().getUserNameRect(), Rect.class);
        //取出头部班级学号和姓名信息
        if (isOne) {
            // 按比例计算位置及宽高
            String headImageStuNoUrl = String.format(IMAGE_CUT_URL_FORMAT, dealContext.getDestBufferedImageOneUrl(), (int) (stuNoRect.getW() * scale), (int) (stuNoRect.getH() * scale), (int) (stuNoRect.getX() * scale), (int) (stuNoRect.getY() * scale));
            String headImageStuNameUrl = String.format(IMAGE_CUT_URL_FORMAT, dealContext.getDestBufferedImageOneUrl(), (int) (stuNameRect.getW() * scale), (int) (stuNameRect.getH() * scale), (int) (stuNameRect.getX() * scale), (int) (stuNameRect.getY() * scale));
            Image headImageStuNo = ImageIO.read(new URL(headImageStuNoUrl));
            Image headImageStuName = ImageIO.read(new URL(headImageStuNameUrl));
            g.drawImage(headImageStuNo, (int) (stuNoRect.getX() * scale), (int) (stuNoRect.getY() * scale), null);
            g.drawImage(headImageStuName, (int) (stuNameRect.getX() * scale), (int) (stuNameRect.getY() * scale), null);
        }

        if (resultAnswerOCRList.size() > 0) {

            for (HomeworkResultAnswerDto resultAnswerDto : resultAnswerOCRList) {
                Image answerUrl = ImageIO.read(new URL(resultAnswerDto.getStuAnswerUrls()));
                g.drawImage(answerUrl, resultAnswerDto.getContentRect().getX(), resultAnswerDto.getContentRect().getY(), null);
            }
            g.dispose();
        }

        try {
            String imageBase64 = ImageUtils.convertImageToBase64(resultOCRImage, "png");
            // 调用腾讯OCR识别手写体
            GeneralHandwritingOCRResponse handwritingOCR = tencentOcrApi.getHandwritingOCR(imageBase64, null, "only_hw");
            if (handwritingOCR == null) {
                log.error("【腾讯OCR】 腾讯文字识别， 错误:{}", handwritingOCR.getRequestId());
            }

            // 识别到的文本
            for (TextGeneralHandwriting textGeneralHandwriting : handwritingOCR.getTextDetections()) {
                //获取坐标中心点位置坐标
                Coord[] coord = textGeneralHandwriting.getPolygon();
                Long minx = coord[0].getX();
                Long miny = coord[0].getY();
                Long maxx = coord[0].getX();
                Long maxy = coord[0].getY();
                for (Coord coord1 : coord) {
                    //分别获取最小的和最大的X坐标
                    if (coord1.getX() < minx) {
                        minx = coord1.getX();
                    } else if (coord1.getX() > maxx) {
                        maxx = coord1.getX();
                    }
                    //分别获取最小的和最大的Y坐标
                    if (coord1.getY() < miny) {
                        miny = coord1.getY();
                    } else if (coord1.getY() > maxy) {
                        maxy = coord1.getY();
                    }
                }

                int x = (int) (maxx + minx) / 2;
                int y = (int) (maxy + miny) / 2;

                //取出头部班级学号和姓名信息
                if (isOne) {
                    // 按比例计算位置及宽高
                    if ((x > (int) (stuNoRect.getX() * scale)) && (x < (int) (stuNoRect.getX() * scale) + (int) (stuNoRect.getW() * scale))
                            && (y > (int) (stuNoRect.getY() * scale)) && (y < (int) (stuNoRect.getY() * scale) + (int) (stuNoRect.getH() * scale))) {
                        //如果是数字和字母，则算作学号，否则算姓名
                        if (StringUtils.isAlphanumeric(textGeneralHandwriting.getDetectedText().trim())) {
                            //如果分多次，则合并
                            if (StringUtils.isNotBlank(dealContext.getOcrStuNo())) {
                                dealContext.setOcrStuNo(dealContext.getOcrStuNo() + textGeneralHandwriting.getDetectedText().trim());
                            } else {
                                dealContext.setOcrStuNo(textGeneralHandwriting.getDetectedText().trim());
                            }
                        }
                    }

                    if ((x > (int) (stuNameRect.getX() * scale)) && (x < (int) (stuNameRect.getX() * scale) + (int) (stuNameRect.getW() * scale))
                            && (y > (int) (stuNameRect.getY() * scale)) && (y < (int) (stuNameRect.getY() * scale) + (int) (stuNameRect.getH() * scale))) {
                        //如果分多次，则合并
                        if (StringUtils.isNotBlank(dealContext.getOcrStuName())) {
                            dealContext.setOcrStuName(dealContext.getOcrStuName() + textGeneralHandwriting.getDetectedText().trim());
                        } else {
                            dealContext.setOcrStuName(textGeneralHandwriting.getDetectedText().trim());
                        }
                    }
                }

                for (HomeworkResultAnswerDto resultAnswerDto : resultAnswerOCRList) {
                    //如果识别内容的中心坐标点在题目划定范围内，则被认为属于答案
                    if ((x > resultAnswerDto.getContentRect().getX()) && (x < (resultAnswerDto.getContentRect().getX() + resultAnswerDto.getContentRect().getW()) / 2)
                            && (y > resultAnswerDto.getContentRect().getY()) && (y < (resultAnswerDto.getContentRect().getY() + resultAnswerDto.getContentRect().getH()) / 2)) {
                        // 识别到的文本并且全部是大写字母，才放入答案区
                        if (StringUtils.isAllUpperCase(resultAnswerDto.getStuAnswerUrls())) {
                            resultAnswerDto.setStuAnswer(resultAnswerDto.getStuAnswer() + textGeneralHandwriting.getDetectedText());
                        } else {
                            resultAnswerDto.setStuAnswer(textGeneralHandwriting.getDetectedText());
                        }
                    }
                }

            }

            for (HomeworkResultAnswerDto resultAnswerDto : resultAnswerOCRList) {
                if (StringUtils.equals(resultAnswerDto.getStuAnswer(), resultAnswerDto.getQuesAnswer())) {
                    resultAnswerDto.setIsCorrect(CorrectResultEnum.CORRECT.getCode());
                } else {
                    resultAnswerDto.setIsCorrect(CorrectResultEnum.WRONG.getCode());
                }
            }

        } catch (Exception e) {
            log.error("调用腾讯OCR识别手写体异常,error:", e);
        }

        return resultAnswerOCRList;
    }

}
