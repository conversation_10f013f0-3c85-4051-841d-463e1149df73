package com.light.aiszzy.xkwOpen.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.light.beans.*;
import com.light.core.exception.WarningException;
import com.light.enums.XkwDifficultyLevelEnum;
import com.light.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 *  学科网 API 实现
 * <AUTHOR>
 * @date 2025/07/19
 */
@Slf4j
@Service
public class XkwApiImpl extends XkwAbsApiImpl {

    @Resource
    private ThreadPoolTaskExecutor asyncServiceExecutor;

    @Override
    public XkwApiResponseVo<List<XkwQuestionVo>> questionSearch(TikuBo tikuBo) {
        Optional<XkwApiResponseVo<List<XkwQuestionVo>>> xkwApiResponseVo = this.doPost("/xopqbm/questions/tiku-search", (t)->{
            log.debug("【海量题目搜索】 请求参数:{} ， 响应信息:{}", JSON.toJSONString(tikuBo), t);
            JSONObject jsonObject = JSON.parseObject(t);
            XkwApiResponseVo<List<XkwQuestionVo>> vo = jsonObject.to(new TypeReference<XkwApiResponseVo<List<XkwQuestionVo>>>() {
            });
            vo.put("image_ocr_text", jsonObject.getString("image_ocr_text"));
            return vo;
        }, this.convertToMap(tikuBo));
        // 校验是否允许
        return xkwApiResponseVo.filter(XkwApiResponseVo::isSuccess).orElse(null);
    }

    @Override
    public XkwApiResponseVo<List<XkwQuestionVo>> questionTextSearch(TikuBo tikuBo) {

        Optional<XkwApiResponseVo<List<XkwQuestionVo>>> xkwApiResponseVo = this.doPost("/xopqbm/questions/text-search"
                , (t)-> {
                    log.debug("【精品题目搜索】 请求参数:{} ， 响应信息:{}", JSON.toJSONString(tikuBo), t);
                    JSONObject jsonObject = JSON.parseObject(t);
                    XkwApiResponseVo<List<XkwQuestionVo>> vo = jsonObject.to(new TypeReference<XkwApiResponseVo<List<XkwQuestionVo>>>() {
                    });
                    vo.put("image_ocr_text", jsonObject.getString("image_ocr_text"));
                    return vo;
                }, this.convertToMap(tikuBo));

        return xkwApiResponseVo.orElse(null);
    }

    @Override
    public XkwApiResponseVo<List<XkwQuestionVo>> questionBffTextSearch(TikuBo tikuBo) {

        // 精品搜索
        String imgOcrText = null;
        Optional<XkwApiResponseVo<List<XkwQuestionVo>>> xkwApiResponseVo = Optional.ofNullable(this.questionTextSearch(tikuBo));
        if(xkwApiResponseVo.isPresent()) {
            imgOcrText = Optional.ofNullable(xkwApiResponseVo.get().get("image_ocr_text")).map(Object::toString).orElse(null);
        }
        if(xkwApiResponseVo.map(XkwApiResponseVo::getData).filter(CollUtil::isNotEmpty).isPresent()) {
            return xkwApiResponseVo.get();
        }

        // 海量搜索
        Optional<XkwApiResponseVo<List<XkwQuestionVo>>> vo = Optional.ofNullable(this.questionSearch(tikuBo));
        if(StrUtil.isEmpty(imgOcrText) && vo.isPresent()) {
            imgOcrText = Optional.ofNullable(vo.get().get("image_ocr_text")).map(Object::toString).orElse(null);
        }

        // Ocr内容为空 重新获取
        if(StrUtil.isEmpty(imgOcrText)) {
            imgOcrText = this.ocr(tikuBo.getImage_base64());
        }

        // 海量题库 未查询到数据 查询相似题
        if(!vo.map(XkwApiResponseVo::getData).filter(CollUtil::isNotEmpty).isPresent()) {
            if(StrUtil.isEmpty(imgOcrText)) {
                return new XkwApiResponseVo<>();
            }
            List<XkwQuestionVo> similarQuestionVos = this.querySimilarQuestion(imgOcrText, tikuBo.getCourse_id(), 1);
            XkwApiResponseVo<List<XkwQuestionVo>> resp = new XkwApiResponseVo<>();
            resp.setCode(200);
            resp.setData(similarQuestionVos);
            resp.put("image_ocr_text", imgOcrText);
            return resp;
        } else {
            // 查询相似题 填充知识点
            XkwApiResponseVo<List<XkwQuestionVo>> resp = vo.get();
            Optional.ofNullable(this.querySimilarQuestion(resp.getData().get(0).getStem(), tikuBo.getCourse_id(), 1))
                    .filter(CollectionUtil::isNotEmpty).ifPresent(e-> {
                                XkwQuestionVo xkwQuestionVo = e.get(0);
                                resp.getData().forEach(x-> {
                                    x.setType(xkwQuestionVo.getType());
                                    x.setType_id(xkwQuestionVo.getType_id());
                                    x.setKpoints(xkwQuestionVo.getKpoints());
                                    x.setKpoint_ids(xkwQuestionVo.getKpoint_ids());
                                    x.setCatalogs(xkwQuestionVo.getCatalogs());
                                    x.setCatalog_ids(xkwQuestionVo.getCatalog_ids());
                                });

                    });

            return resp;
        }

    }


    @Override
    public String ocr(String base64Img) {

        OcrBo ocrBo = new OcrBo();
        ocrBo.setImage_base64(base64Img);
        Optional<String> ocrOptional = this.doPost("/xopqbm/ocr",(t)->{
            log.debug("【学科网 OCR】 响应信息:{}", t);
            JSONObject jsonObject = JSON.parseObject(t);
            return jsonObject.getJSONObject("data").getString("text");
        }, this.convertToMap(ocrBo));

        return ocrOptional.orElse(null);
    }

    @Override
    public List<SimilarRecommendVo> querySimilarQuestions(String questionText, Integer xkwCourseId, List<Integer> difficultyLevels, Integer count) {
        SimilarBo similarBo = new SimilarBo();
        similarBo.setText(questionText);
        similarBo.setXkwCourseId(xkwCourseId);
        similarBo.setCount(count);
        return this.querySimilarQuestion(similarBo, difficultyLevels);
    }

    @Override
    public List<SimilarRecommendVo> querySimilarQuestion(SimilarBo similarBo, List<Integer> customDifficultyLevels) {
        // 题干
        String text = Optional.ofNullable(similarBo.getText())
                .filter(StrUtil::isNotEmpty)
                .map(StringUtil::extractTextFromHtml).map(x-> {
                    if(x.length() > 2000) {
                        x = x.substring(0, 2000);
                    }
                    return x;
                }).orElseThrow(()-> new WarningException("文本不能为空"));

        // 学科网courseId
        Integer xkwCourseId = similarBo.getXkwCourseId();
        // 题目数量
        Integer count = similarBo.getCount();

        // 初始化难易度, 未传入难易度 使用默认难易度
        List<XkwDifficultyLevelEnum> customDifficulties = Optional.ofNullable(customDifficultyLevels)
                .map(x-> x.stream().map(XkwDifficultyLevelEnum::fromCode)
                        .filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(XkwDifficultyLevelEnum.RecommendStrategy.DEFAULT.getDifficulties());

        // 生成查询条件
        List<SimilarRecommendBo> similarRecommendBos = customDifficulties.stream()
                .map(x-> this.createSimilarRecommendBo(text, xkwCourseId, x, count))
                .collect(Collectors.toList());

        // 异步查询数据
        List<CompletableFuture<XkwApiResponseVo<List<SimilarRecommendVo>>>> futures =
                similarRecommendBos.stream().map(bo -> CompletableFuture
                                .supplyAsync(() -> this.querySimilarRecommend(bo), asyncServiceExecutor))
                        .collect(Collectors.toList());

        // 等待所有任务完成并收集结果
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 收集成功的结果
        return futures.stream().map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .map(XkwApiResponseVo::getData)
                .flatMap(Collection::stream).collect(Collectors.toList());
    }


    @Override
    public XkwApiResponseVo<List<SimilarRecommendVo>> querySimilarRecommend(SimilarRecommendBo similarBo) {
        Optional<XkwApiResponseVo<List<SimilarRecommendVo>>> xkwApiResponseVo = this.doPost("/xopqbm/questions/similar-recommend"
                , (t)-> {
                    log.debug("【相似题搜索】 请求参数:{} ， 响应信息:{}", JSON.toJSONString(similarBo), t);
                    JSONObject jsonObject = JSON.parseObject(t);
                    return jsonObject.to(new TypeReference<XkwApiResponseVo<List<SimilarRecommendVo>>>() {
                    });
                }, this.convertToMap(similarBo));
        return xkwApiResponseVo.orElse(null);
    }




    private SimilarRecommendBo createSimilarRecommendBo(String text, Integer courseId,
                                                        XkwDifficultyLevelEnum difficulty, Integer count) {
        SimilarRecommendBo bo = new SimilarRecommendBo();
        bo.setText(text);
        bo.setDifficulty_levels(Collections.singletonList(difficulty.getCode()));
        bo.setCount(count);
        bo.setCourse_id(courseId);
        return bo;
    }



    private List<XkwQuestionVo> querySimilarQuestion(String text, Integer courseId, Integer count) {
        // 相似题搜索

        SimilarBo similarBo = new SimilarBo();
        similarBo.setText(text);
        similarBo.setXkwCourseId(courseId);
        similarBo.setCount(count);
        List<SimilarRecommendVo> similarRecommendVos = this.querySimilarQuestion(similarBo, null);
        if(CollUtil.isEmpty(similarRecommendVos)) {
            return Collections.emptyList();
        }
        return similarRecommendVos.stream().map(x-> BeanUtil.toBean(x, XkwQuestionVo.class)).collect(Collectors.toList());

    }
}
