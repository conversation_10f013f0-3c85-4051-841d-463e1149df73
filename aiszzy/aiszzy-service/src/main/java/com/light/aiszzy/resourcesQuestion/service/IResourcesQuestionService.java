package com.light.aiszzy.resourcesQuestion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.resourcesQuestion.entity.dto.ResourcesQuestionDto;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionConditionBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionBo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionVo;
import com.light.beans.SimilarRecommendVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 资源库题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IResourcesQuestionService extends IService<ResourcesQuestionDto> {

    List<ResourcesQuestionVo> getResourcesQuestionListByCondition(ResourcesQuestionConditionBo condition);

	AjaxResult addResourcesQuestion(ResourcesQuestionBo resourcesQuestionBo);

	AjaxResult updateResourcesQuestion(ResourcesQuestionBo resourcesQuestionBo);

    ResourcesQuestionVo getDetail(String oid);

    /**
     * 根据第三方ID和来源类型查询题目
     * @param thirdOutId 第三方ID
     * @param thirdSourceType 来源类型
     * @return ResourcesQuestionBo
     */
    ResourcesQuestionBo getByThirdOutIdAndSourceType(String thirdOutId, String thirdSourceType);

    /**
     * 根据第三方ID和来源类型查询题目OID
     * 符合DDD设计原则的查询方法
     * @param thirdSourceType 第三方来源类型枚举
     * @param thirdOutId 第三方ID
     * @return 题目OID，如果不存在返回null
     */
    String getOidByThirdOutId(com.light.enums.ThirdSourceTypeEnum thirdSourceType, String thirdOutId);

    /**
     * 根据多个第三方ID和来源类型批量查询题目
     * @param thirdOutIds 第三方ID列表
     * @param thirdSourceType 来源类型
     * @return ResourcesQuestionBo列表
     */
    List<ResourcesQuestionBo> getBoListByThirdOutIds(List<String> thirdOutIds, String thirdSourceType);

    /**
     * 批量保存学科网相似题目到资源库
     * 如果题目已存在则将已存在的题目添加到返回列表，不存在则新增
     * @param similarQuestions 相似题目列表
     * @return 保存的题目列表（包含新增和已存在的题目）
     */
    List<ResourcesQuestionBo> saveSimilarQuestionsFromXkw(List<SimilarRecommendVo> similarQuestions);

    /**
     * 查询学科网相似题目并保存到资源库
     * 这是一个完整的业务方法，包含查询和保存的完整逻辑
     * @param questionText 题目文本内容
     * @param xkwCourseId 学科网课程ID
     * @param difficultyLevels 难度等级列表
     * @param everyTypeQuestionCount 查询题目数量（每个类型查询题目数量）
     * @return 成功保存的题目列表
     */
    List<ResourcesQuestionBo> queryAndSaveXkwSimilarQuestions(String questionText, Integer xkwCourseId,
                                                              List<Integer> difficultyLevels, Integer everyTypeQuestionCount);


    /**
     *  查询并保存学科网题目信息
     * @param xkwCourseId the xkw course id 学科网 course Id
     * @param imgBase64 the img base64 图片 base64数据
     * @param count the count 查询数量
     * @return {@link List }<{@link ResourcesQuestionVo }>
     */
    List<ResourcesQuestionVo> queryAndSaveXkwQuestionsByImg(Integer xkwCourseId, String imgBase64, Integer count);

    /**
     * 查询并保存学科网题目信息
     *
     * @param xkwCourseId the xkw course id 学科网 course Id
     * @param imgBase64   the img base64 图片 base64数据
     * @return {@link List }<{@link ResourcesQuestionVo }>
     */
    ResourcesQuestionVo queryAndSaveXkwQuestionsByImg(Integer xkwCourseId, String imgBase64);

    /**
     * 通过图片（Base64）查询题目，并保存题目及其相关的相似题目和关联关系。
     *
     * 该方法的主要流程如下：
     * 1. 根据图片的 Base64 数据，查询并保存题目信息。
     * 2. 使用查询到的题目信息，查找相似题目并保存到数据库。
     * 3. 建立原题目与相似题目的关联关系，并保存到关联表。
     *
     * @param xkwCourseId 学科网课程 ID，用于限定题目的所属范围。
     * @param imgBase64 图片的 Base64 编码，用于查询题目信息。
     * @return AjaxResult 返回操作结果，包含处理后的数据。
     */
    ResourcesQuestionVo queryAndSaveQuestionWithSimilarRelationsByImage(Integer xkwCourseId, String imgBase64);


    /**
     * 通过thirdOutId查询并更新resourceQuestion表的questionOid字段
     * @param thirdSourceType 第三方来源类型code
     * @param thirdOutId 第三方ID
     * @param questionOid 要更新的questionOid
     * @return 更新结果
     */
    AjaxResult updateQuestionIdByThirdOutId(String thirdSourceType, String thirdOutId, String questionOid);

}

