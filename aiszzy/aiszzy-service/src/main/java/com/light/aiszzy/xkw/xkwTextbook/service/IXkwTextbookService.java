package com.light.aiszzy.xkw.xkwTextbook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookConditionBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.dto.XkwTextbookDto;
import com.light.aiszzy.xkw.xkwTextbook.entity.vo.XkwTextbookVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 教材接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
public interface IXkwTextbookService extends IService<XkwTextbookDto> {

    List<XkwTextbookVo> getXkwTextbookListByCondition(XkwTextbookConditionBo condition);



}

