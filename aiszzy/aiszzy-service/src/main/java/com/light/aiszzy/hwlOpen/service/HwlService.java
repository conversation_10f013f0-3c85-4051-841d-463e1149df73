package com.light.aiszzy.hwlOpen.service;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.light.contants.ConstantsInteger;
import com.light.beans.ImageRequestBo;
import com.light.aiszzy.hwlOpen.tal.ailab.enums.RequestMethod;
import com.light.aiszzy.hwlOpen.tal.ailab.sign.SendSignHttp;
import com.light.aiszzy.hwlOpen.tal.ailab.util.DateUtil;
import com.light.core.entity.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;

import static com.light.aiszzy.hwlOpen.tal.ailab.util.HttpUtil.*;


/**
 * 好未来工具类
 * 
 * 参考XkwUtils的简单模式，使用Java 8特性优化代码
 * 
 * <AUTHOR>
 * @date 2025/7/8
 */
@Slf4j
@Component
public class HwlService {

    @Value("${tal.access.key.id:}")
    private String accessKeyId;

    @Value("${tal.access.key.secret:}")
    private String accessKeySecret;

    /**
     * 好未来自动框选API地址
     */
    private static final String AUTOMATIC_BOX_URL = "http://openai.100tal.com/aiimage/ocr/automatic-box";

    /**
     * 教育通用OCR API地址
     */
    private static final String EDUCATION_OCR_URL = "https://openai.100tal.com/aiimage/comeducation";

    /**
     * 作业批改API地址
     */
    private static final String HOMEWORK_CORRECTION_URL = "http://openai.100tal.com/aitext/pigai/v1/proxy/correction";

    /**
     * 通用HTTP请求处理器
     * 使用函数式接口提高代码复用性
     */
    private Optional<JSONObject> executeRequest(String url, Map<String, Object> params,
            Function<Map<String, Object>, HttpResponse> requestExecutor) {
        try {
            HttpResponse response = requestExecutor.apply(params);
            String responseJson = EntityUtils.toString(response.getEntity(), Charset.defaultCharset());
            log.debug("HWL API: {} response: {}", url, responseJson);
            return StringUtils.isNotBlank(responseJson) 
                ? Optional.ofNullable(JSONObject.parseObject(responseJson))
                : Optional.empty();
        } catch (Exception e) {
            log.error("HWL API: {} request failed", url, e);
            throw new RuntimeException("HWL API: " + url + " request failed: " + e.getMessage());
        }
    }

    /**
     * 发送POST请求
     */
    private Optional<JSONObject> doPost(String url, Map<String, Object> params) {
        return executeRequest(url, params, bodyParams -> {
            try {
                Map<String, Object> urlParams = new HashMap<>();
                Date timestamp = DateUtil.getCurrentDate();
                
                return SendSignHttp.sendRequest(
                    accessKeyId,
                    accessKeySecret,
                    timestamp,
                    url,
                    urlParams,
                    bodyParams,
                    RequestMethod.POST,
                    APPLICATION_JSON
                );
            } catch (Exception e) {
                throw new RuntimeException("发送请求失败", e);
            }
        });
    }

    /**
     * 提取响应数据的通用方法
     * 使用Optional优化空值处理（JDK8兼容版本）
     */
    private Optional<AjaxResult> extractResponseData(Optional<JSONObject> responseOpt, String url, String requestInfo) {
        Optional<AjaxResult> result = responseOpt
            .filter(json -> Objects.equals(json.get("code"), ConstantsInteger.TAL_CODE_SUCCESS))
            .map(this::buildSuccessResult);

        // JDK8兼容：使用isPresent()替代or()方法
        if (!result.isPresent()) {
            responseOpt.ifPresent(json -> {
                log.error("HWL API: {}, request failed, {}, response: {}", url, requestInfo, json);
            });
            // 构建失败结果
            return responseOpt.map(this::buildFailResult);
        }

        return result;
    }

    /**
     * 构建成功结果
     */
    private AjaxResult buildSuccessResult(JSONObject json) {
        JSONObject data = json.getJSONObject("data");
        AjaxResult<JSONObject> result = new AjaxResult<>();
        result.setData(data);
        result.setCode(HttpStatus.HTTP_OK);
        result.setSuccess(true);
        result.setMsg(json.getString("msg"));
        return result;
    }

    /**
     * 构建失败结果
     */
    private AjaxResult buildFailResult(JSONObject json) {
        AjaxResult<JSONObject> result = new AjaxResult<>();
        result.setCode(HttpStatus.HTTP_INTERNAL_ERROR);
        result.setSuccess(false);
        result.setMsg(json.getString("msg"));
        return result;
    }

    /**
     * 处理自动框选请求
     *
     * @param imageRequestBo 图片请求参数
     * @return 处理结果
     */
    public AjaxResult processAutomaticBox(ImageRequestBo imageRequestBo) {
        try {
            Map<String, Object> params = buildAutomaticBoxRequestParams(imageRequestBo);
            Optional<JSONObject> responseOpt = doPost(AUTOMATIC_BOX_URL, params);
            return extractResponseData(responseOpt, AUTOMATIC_BOX_URL, "自动框选请求")
                .orElse(AjaxResult.fail("API调用失败"));
        } catch (Exception e) {
            log.error("处理自动框选请求失败", e);
            return AjaxResult.fail("服务异常：" + e.getMessage());
        }
    }

    /**
     * 处理教育通用OCR请求
     *
     * @param imageRequestBo 图片请求参数
     * @return 处理结果
     */
    public AjaxResult processEducationOcr(ImageRequestBo imageRequestBo) {
        try {
            if (!validateImageRequest(imageRequestBo)) {
                return AjaxResult.fail("图片参数不能为空，请提供image_base64或image_url");
            }

            Map<String, Object> params = buildEducationOcrRequestParams(imageRequestBo);
            Optional<JSONObject> responseOpt = doPost(EDUCATION_OCR_URL, params);
            return extractResponseData(responseOpt, EDUCATION_OCR_URL, "教育通用OCR请求")
                .orElse(AjaxResult.fail("API调用失败"));
        } catch (Exception e) {
            log.error("处理教育通用OCR请求失败", e);
            return AjaxResult.fail("服务异常：" + e.getMessage());
        }
    }

    /**
     * 处理作业批改请求
     *
     * @param imageRequestBo 图片请求参数
     * @return 处理结果
     */
    public AjaxResult processHomeworkCorrection(ImageRequestBo imageRequestBo) {
        try {
            if (!validateHomeworkCorrectionRequest(imageRequestBo)) {
                return AjaxResult.fail("作业批改API只支持图片URL参数，请提供imageUrls数组或imageUrl");
            }

            Map<String, Object> params = buildHomeworkCorrectionRequestParams(imageRequestBo);
            Optional<JSONObject> responseOpt = doPost(HOMEWORK_CORRECTION_URL, params);
            return extractResponseData(responseOpt, HOMEWORK_CORRECTION_URL, "作业批改请求")
                .orElse(AjaxResult.fail("API调用失败"));
        } catch (Exception e) {
            log.error("处理作业批改请求失败", e);
            return AjaxResult.fail("服务异常：" + e.getMessage());
        }
    }

    /**
     * 验证图片请求参数
     */
    private boolean validateImageRequest(ImageRequestBo request) {
        return StringUtils.isNotBlank(request.getImageBase64()) ||
               StringUtils.isNotBlank(request.getImageUrl());
    }

    /**
     * 验证作业批改请求参数
     * 作业批改API只支持图片URL，不支持base64
     */
    private boolean validateHomeworkCorrectionRequest(ImageRequestBo request) {
        // 检查是否有imageUrls数组且不为空
        if (request.getImageUrls() != null && !request.getImageUrls().isEmpty()) {
            // 检查数组中的每个URL都不为空
            return request.getImageUrls().stream().allMatch(StringUtils::isNotBlank);
        }
        // 如果没有imageUrls数组，检查单个imageUrl
        return StringUtils.isNotBlank(request.getImageUrl());
    }

    /**
     * 构建好未来框题目请求参数
     */
    private Map<String, Object> buildAutomaticBoxRequestParams(ImageRequestBo imageRequestBo) {
        Map<String, Object> bodyParams = new HashMap<>();

        // 优先使用base64，其次使用URL
        if (StringUtils.isNotBlank(imageRequestBo.getImageBase64())) {
            bodyParams.put("image_base64", imageRequestBo.getImageBase64());
        } else if (StringUtils.isNotBlank(imageRequestBo.getImageUrl())) {
            bodyParams.put("image_url", imageRequestBo.getImageUrl());
        }

        // 返回题目类型
        bodyParams.put("type", 1);

        return bodyParams;
    }

    /**
     * 构建教育通用OCR请求参数
     */
    private Map<String, Object> buildEducationOcrRequestParams(ImageRequestBo imageRequestBo) {
        Map<String, Object> bodyParams = new HashMap<>();

        // 优先使用base64，其次使用URL
        if (StringUtils.isNotBlank(imageRequestBo.getImageBase64())) {
            bodyParams.put("image_base64", imageRequestBo.getImageBase64());
        } else if (StringUtils.isNotBlank(imageRequestBo.getImageUrl())) {
            bodyParams.put("image_url", imageRequestBo.getImageUrl());
        }

        // 功能类型：0-完整识别，1-印刷文字识别，2-手写文字识别，3-印刷行图识别，4-手写行图识别，5-公式识别
        // 如果传入了参数则使用传入值，否则使用默认值0（完整识别）
        bodyParams.put("function", imageRequestBo.getFunction() != null ? imageRequestBo.getFunction() : 0);

        // 是否检测旋转图片朝向，默认true
        bodyParams.put("detect_direction", imageRequestBo.getDetectDirection() != null ? imageRequestBo.getDetectDirection() : true);

        // 文理科选择：liberat-文科，science-理科，默认理科
        bodyParams.put("subject", StringUtils.isNotBlank(imageRequestBo.getSubject()) ? imageRequestBo.getSubject() : "science");

        // 是否打印图片中的文字，默认true
        bodyParams.put("textInImage", imageRequestBo.getTextInImage() != null ? imageRequestBo.getTextInImage() : true);

        // 是否打印表格中的文字，默认true
        bodyParams.put("textInTable", imageRequestBo.getTextInTable() != null ? imageRequestBo.getTextInTable() : true);

        // 是否自适应学拍拍结果，默认false
        bodyParams.put("isXuePaiPai", imageRequestBo.getIsXuePaiPai() != null ? imageRequestBo.getIsXuePaiPai() : false);

        return bodyParams;
    }

    /**
     * 构建作业批改请求参数
     */
    private Map<String, Object> buildHomeworkCorrectionRequestParams(ImageRequestBo imageRequestBo) {
        Map<String, Object> bodyParams = new HashMap<>();

        // 作业批改API需要image_urls数组参数
        List<String> imageUrls = new ArrayList<>();

        // 优先使用传入的imageUrls数组，其次使用单个imageUrl
        if (imageRequestBo.getImageUrls() != null && !imageRequestBo.getImageUrls().isEmpty()) {
            // 如果传入了imageUrls数组，直接使用
            imageUrls.addAll(imageRequestBo.getImageUrls());
        } else if (StringUtils.isNotBlank(imageRequestBo.getImageUrl())) {
            // 如果只传入了单个imageUrl，添加到数组中
            imageUrls.add(imageRequestBo.getImageUrl());
        } else if (StringUtils.isNotBlank(imageRequestBo.getImageBase64())) {
            // 注意：作业批改API只支持URL，如果只有base64需要先上传获取URL
            // 这里暂时记录base64，实际使用时需要先转换为URL
            log.warn("作业批改API只支持image_urls参数，需要先将base64转换为URL");
            // 可以考虑在这里添加base64转URL的逻辑
        }

        bodyParams.put("image_urls", imageUrls);

        return bodyParams;
    }

    /**
     * 通用的好未来API调用方法
     * 支持扩展其他好未来API接口
     *
     * @param url API地址
     * @param params 请求参数
     * @return 处理结果
     */
    public AjaxResult callHwlApi(String url, Map<String, Object> params) {
        String requestInfo = "API request: " + url;
        return extractResponseData(doPost(url, params), url, requestInfo)
            .orElse(AjaxResult.fail("API调用失败"));
    }

    /**
     * 对象转Map的通用方法
     * 使用Optional优化空值处理
     */
    private Map<String, Object> convertToMap(Object requestBody) {
        return Optional.ofNullable(requestBody)
            .map(body -> {
                // 简单的对象转Map实现，可根据需要扩展
                if (body instanceof Map) {
                    return (Map<String, Object>) body;
                }
                // 这里可以添加更复杂的转换逻辑
                return new HashMap<String, Object>();
            })
            .orElse(new HashMap<>());
    }

    /**
     * 发送POST请求（支持对象参数）
     * 注意：requestBody不支持json注解，只支持原生的变量名称转换
     */
    public AjaxResult callHwlApiWithObject(String url, Object requestBody) {
        return callHwlApi(url, convertToMap(requestBody));
    }
}
