package com.light.aiszzy.practiceBook.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 教辅题目表，用于存储题目信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("practice_book_question")
public class PracticeBookQuestionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条题目记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 关联的教辅OID
	 */
	@TableField("practice_book_oid")
	private String practiceBookOid;

	/**
	 * 关联的教辅目录OID
	 */
	@TableField("practice_book_catalog_oid")
	private String practiceBookCatalogOid;

	/**
	 * 关联的页码图片OID
	 */
	@TableField("practice_book_page_oid")
	private String practiceBookPageOid;

	/**
	 * 资源题目oid
	 */
	@TableField("question_oid")
	private String questionOid;

	/**
	 * 学科网xkw，好未来hwl等
	 */
	@TableField("third_source_type")
	private String thirdSourceType;

	/**
	 * 添加外部id
	 */
	@TableField("third_out_id")
	private String thirdOutId;

	/**
	 * 图片地址
	 */
	@TableField("image_url")
	private String imageUrl;

	/**
	 * 题目页码，跨页记录起始页码
	 */
	@TableField("page_no")
	private Integer pageNo;

	/**
	 * 每页题目排序
	 */
	@TableField("page_no_order_num")
	private Long pageNoOrderNum;

	/**
	 * 划题是否完成  0：未完成 1：完成
	 */
	@TableField("mark_status")
	private Integer markStatus;

	/**
	 * 题目是否编辑  0 否 1 是
	 */
	@TableField("is_question_modify")
	private Integer isQuestionModify;
	/**
	 * 是否是跨页题，0：否，1：是
	 */
	@TableField("is_cross_page")
	private Integer isCrossPage;

	/**
	 * 题目内容json
	 */
	@TableField("question_content_json")
	private String questionContentJson;

	/**
	 * 外部关系类型（原题，近似题，未查到）
	 */
	@TableField("out_question_relation_type")
	private String outQuestionRelationType;

	/**
	 * 外部关系类型是否确认 0未确认 1确认
	 */
	@TableField("out_question_relation_type_status")
	private Integer outQuestionRelationTypeStatus;

	/**
	 * orc识别内容（第三方识别，在高拍情况下比对题目查找页码）
	 */
	@TableField("ocr_content")
	private String ocrContent;

	/**
	 * 跟原题相似度
	 */
	@TableField("similarity")
	private String similarity;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
