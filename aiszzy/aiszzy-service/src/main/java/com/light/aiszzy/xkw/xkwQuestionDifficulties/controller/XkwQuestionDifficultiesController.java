package com.light.aiszzy.xkw.xkwQuestionDifficulties.controller;

import com.light.aiszzy.xkw.xkwQuestionDifficulties.api.XkwQuestionDifficultiesApi;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.dto.XkwQuestionDifficultiesDto;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.vo.XkwQuestionDifficultiesVo;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.service.IXkwQuestionDifficultiesService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 试题难度等级
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@RestController
@Validated
@Api(value = "", tags = "试题难度等级接口")
public class XkwQuestionDifficultiesController implements XkwQuestionDifficultiesApi {

    @Autowired
    private IXkwQuestionDifficultiesService xkwQuestionDifficultiesService;

    @Override
    public AjaxResult<List<XkwQuestionDifficultiesVo>> findAll() {
        List<XkwQuestionDifficultiesDto> list = this.xkwQuestionDifficultiesService.list();
        return AjaxResult.success(list);
    }
}
