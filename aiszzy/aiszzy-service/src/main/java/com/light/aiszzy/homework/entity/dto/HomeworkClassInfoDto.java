package com.light.aiszzy.homework.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 作业班级信息，包含疑问项汇总
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_class_info")
public class HomeworkClassInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业名称
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 学校id
	 */
	@TableField("org_code")
	private String orgCode;


	/**
	 * 班级oid
	 */
	@TableField("class_id")
	private Long classId;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Long subject;

	/**
	 * 学期  1:上学期  2：下学期
	 */
	@TableField("term")
	private Long term;

	/**
	 * 剩余疑问项数量
	 */
	@TableField("remainder_doubt_count")
	private Long remainderDoubtCount;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
