package com.light.aiszzy.apiRequestLog.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.apiRequestLog.entity.dto.ApiRequestLogDto;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogConditionBo;
import com.light.aiszzy.apiRequestLog.entity.vo.ApiRequestLogVo;

/**
 * 第三方请求日志表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface ApiRequestLogMapper extends BaseMapper<ApiRequestLogDto> {

	List<ApiRequestLogVo> getApiRequestLogListByCondition(ApiRequestLogConditionBo condition);

}
