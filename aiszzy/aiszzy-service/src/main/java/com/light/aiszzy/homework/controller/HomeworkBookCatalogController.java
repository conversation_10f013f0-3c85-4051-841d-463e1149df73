package com.light.aiszzy.homework.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogVo;
import com.light.aiszzy.homework.service.IHomeworkBookCatalogService;

import com.light.aiszzy.homework.api.HomeworkBookCatalogApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 作业本目录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "作业本目录接口")
public class HomeworkBookCatalogController implements HomeworkBookCatalogApi {

    @Autowired
    private IHomeworkBookCatalogService homeworkBookCatalogService;

    public AjaxResult<PageInfo<HomeworkBookCatalogVo>> getHomeworkBookCatalogPageListByCondition(@RequestBody HomeworkBookCatalogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkBookCatalogVo> pageInfo = new PageInfo<>(homeworkBookCatalogService.getHomeworkBookCatalogListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkBookCatalogVo>> getHomeworkBookCatalogListByCondition(@RequestBody HomeworkBookCatalogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkBookCatalogService.getHomeworkBookCatalogListByCondition(condition));
    }

    @Override
    public AjaxResult listAllChild(@RequestBody HomeworkBookCatalogConditionBo condition) {
        return homeworkBookCatalogService.listAllChild(condition);
    }

    @Override
    public AjaxResult listAllChildByBook(HomeworkBookCatalogConditionBo condition) {
        return homeworkBookCatalogService.listAllChildByBook(condition);
    }

    @Override
    public AjaxResult sortHomeworkBookCatalog(HomeworkBookCatalogBo condition) {
        return homeworkBookCatalogService.sortHomeworkBookCatalog(condition);
    }

    public AjaxResult addHomeworkBookCatalog(@Validated @RequestBody HomeworkBookCatalogBo homeworkBookCatalogBo) {
        return homeworkBookCatalogService.addHomeworkBookCatalog(homeworkBookCatalogBo);
    }

    public AjaxResult updateHomeworkBookCatalog(@Validated @RequestBody HomeworkBookCatalogBo homeworkBookCatalogBo) {
        if (null == homeworkBookCatalogBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkBookCatalogService.updateHomeworkBookCatalog(homeworkBookCatalogBo);
    }

    public AjaxResult<HomeworkBookCatalogVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkBookCatalogService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkBookCatalogBo homeworkBookCatalogBo = new HomeworkBookCatalogBo();
            homeworkBookCatalogBo.setOid(oid);
            homeworkBookCatalogBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkBookCatalogService.deleteHomeworkBookCatalog(homeworkBookCatalogBo);
    }

    @Override
    public AjaxResult deleteByBookOid(String oid) {
        return homeworkBookCatalogService.deleteByBookOid(oid);
    }
}
