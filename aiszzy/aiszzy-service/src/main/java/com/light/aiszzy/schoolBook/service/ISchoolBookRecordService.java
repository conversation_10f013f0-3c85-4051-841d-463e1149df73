package com.light.aiszzy.schoolBook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.schoolBook.entity.dto.SchoolBookRecordDto;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 教辅或作业本开通记录详情表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface ISchoolBookRecordService extends IService<SchoolBookRecordDto> {

    List<SchoolBookRecordVo> getSchoolBookRecordListByCondition(SchoolBookRecordConditionBo condition);

	AjaxResult addSchoolBookRecord(SchoolBookRecordBo schoolBookRecordBo);

	AjaxResult updateSchoolBookRecord(SchoolBookRecordBo schoolBookRecordBo);

    SchoolBookRecordVo getDetail(String oid);

}

