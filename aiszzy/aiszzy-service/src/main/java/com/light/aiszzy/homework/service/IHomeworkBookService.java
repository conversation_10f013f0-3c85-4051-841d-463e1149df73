package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkBookDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 作业本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IHomeworkBookService extends IService<HomeworkBookDto> {

    List<HomeworkBookVo> getHomeworkBookListByCondition(HomeworkBookConditionBo condition);

    Map getHomeworkBookGroupListByCondition(HomeworkBookConditionBo condition);

    List<HomeworkBookVo> getSchoolNotAddHomeworkBookListByCondition(HomeworkBookConditionBo condition);

    AjaxResult addHomeworkBook(HomeworkBookBo homeworkBookBo);

	AjaxResult updateHomeworkBook(HomeworkBookBo homeworkBookBo);

	AjaxResult updateCurrentBook(HomeworkBookBo homeworkBookBo);

    HomeworkBookVo getDetail(String oid);

    AjaxResult downloadBookZip(String oid);

    AjaxResult downloadBookPdf(String oid);

    AjaxResult textbookVersions(String grade, String subject);

    AjaxResult textbook(String versionId,String gradeId);

    AjaxResult textbookCatalog(String oid);

    AjaxResult saveTextbookCatalog(String oid, String textBookId);

    AjaxResult checkTextbookCatalog(String oid);

    AjaxResult<HomeworkBookVo> practiceBookToHomeworkBook(String practiceBookOid, String userCode, String orgCode);

    AjaxResult<HomeworkBookVo> copyHomeworkBook(String homeworkBookOid);


}

