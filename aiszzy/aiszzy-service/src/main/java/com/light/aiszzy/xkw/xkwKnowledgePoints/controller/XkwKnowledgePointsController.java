package com.light.aiszzy.xkw.xkwKnowledgePoints.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.api.XkwKnowledgePointsApi;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsConditionBo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.service.IXkwKnowledgePointsService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 知识树
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@RestController
@Validated
@Api(value = "", tags = "知识树接口")
public class XkwKnowledgePointsController implements XkwKnowledgePointsApi {

    @Resource
    private IXkwKnowledgePointsService iXkwKnowledgePointsService;

    @Override
    public AjaxResult<PageInfo<XkwKnowledgePointsVo>> getXkwKnowledgePointsPageListByCondition(@RequestBody XkwKnowledgePointsConditionBo condition) {
        Page<XkwKnowledgePointsVo> page = PageHelper.startPage(condition.getPageNo(), condition.getPageSize());
        this.iXkwKnowledgePointsService.getXkwKnowledgePointsListByCondition(condition);
        return AjaxResult.success(page.toPageInfo());
    }

    @Override
    public AjaxResult<List<XkwKnowledgePointsVo>> getXkwKnowledgePointsListByCondition(@RequestBody XkwKnowledgePointsConditionBo condition) {
        return AjaxResult.success(this.iXkwKnowledgePointsService.getXkwKnowledgePointsListByCondition(condition));
    }
}
