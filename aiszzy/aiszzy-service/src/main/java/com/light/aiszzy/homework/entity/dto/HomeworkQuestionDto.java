package com.light.aiszzy.homework.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 校本作业题目信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-10 13:50:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_question")
public class HomeworkQuestionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 题目id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 关联question题目oid
	 */
	@TableField("question_oid")
	private String questionOid;

	/**
	 * 内容(原，包含学生作答区)
	 */
	@TableField("content")
	private String content;

	/**
	 * 内容(不包含学生作答区)
	 */
	@TableField("small_content")
	private String smallContent;

	/**
	 * 题目文本内容
	 */
	@TableField("text_content")
	private String textContent;

	/**
	 * 位置(原，包含学生作答区)
	 */
	@TableField("position")
	private String position;

	/**
	 * 位置(不包含学生作答区)
	 */
	@TableField("small_position")
	private String smallPosition;

	/**
	 * 页码
	 */
	@TableField("page_no")
	private Integer pageNo;

	/**
	 * 题号
	 */
	@TableField("question_no")
	private String questionNo;

	/**
	 * 大题号标题
	 */
	@TableField("big_num_title")
	private String bigNumTitle;

	/**
	 * 小题号标题
	 */
	@TableField("small_num_title")
	private String smallNumTitle;

	/**
	 * 题型id
	 */
	@TableField("question_type_id")
	private String questionTypeId;

	/**
	 * 题型名称
	 */
	@TableField("question_type_name")
	private String questionTypeName;

	/**
	 * 章节ID
	 */
	@TableField("chapter_id")
	private String chapterId;

	/**
	 * 节ID
	 */
	@TableField("section_id")
	private String sectionId;

	/**
	 * 知识点，多个
	 */
	@TableField("knowledge_points_id")
	private String knowledgePointsId;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 难度
	 */
	@TableField("difficult_id")
	private Integer difficultId;

	/**
	 * 启用年份
	 */
	@TableField("year")
	private String year;

	/**
	 * 题目排序
	 */
	@TableField("ques_order_num")
	private Long quesOrderNum;

	/**
	 * 题型排序
	 */
	@TableField("type_order_num")
	private Long typeOrderNum;

	/**
	 * 题目url或文字
	 */
	@TableField("ques_body")
	private String quesBody;

	/**
	 * 公共题干url或文字
	 */
	@TableField("public_ques")
	private String publicQues;

	/**
	 * 答案url或文字
	 */
	@TableField("ques_answer")
	private String quesAnswer;

	/**
	 * 解析url或文字
	 */
	@TableField("analysis_answer")
	private String analysisAnswer;

	/**
	 * 题目展示类型  0：图片url  1：html文字 
	 */
	@TableField("ques_body_type")
	private Long quesBodyType;

	/**
	 * 公共题干展示类型  0：图片url  1：html文字
	 */
	@TableField("public_ques_type")
	private Long publicQuesType;

	/**
	 * 答案展示类型  0：图片url  1：html文字 
	 */
	@TableField("ques_answer_type")
	private Long quesAnswerType;

	/**
	 * 解析展示类型  0：图片url  1：html文字 
	 */
	@TableField("analysis_answer_type")
	private Long analysisAnswerType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
