package com.light.aiszzy.homeworkResult.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultErrorDetailVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultErrorDetailService;

import com.light.aiszzy.homeworkResult.api.HomeworkResultErrorDetailApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 校本作业结果详情表(错误试题)
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "校本作业结果详情表(错误试题)接口")
public class HomeworkResultErrorDetailController implements HomeworkResultErrorDetailApi {

    @Autowired
    private IHomeworkResultErrorDetailService homeworkResultErrorDetailService;

    public AjaxResult<PageInfo<HomeworkResultErrorDetailVo>> getHomeworkResultErrorDetailPageListByCondition(@RequestBody HomeworkResultErrorDetailConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkResultErrorDetailVo> pageInfo = new PageInfo<>(homeworkResultErrorDetailService.getHomeworkResultErrorDetailListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkResultErrorDetailVo>> getHomeworkResultErrorDetailListByCondition(@RequestBody HomeworkResultErrorDetailConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkResultErrorDetailService.getHomeworkResultErrorDetailListByCondition(condition));
    }

    public AjaxResult addHomeworkResultErrorDetail(@Validated @RequestBody HomeworkResultErrorDetailBo homeworkResultErrorDetailBo) {
        return homeworkResultErrorDetailService.addHomeworkResultErrorDetail(homeworkResultErrorDetailBo);
    }

    public AjaxResult updateHomeworkResultErrorDetail(@Validated @RequestBody HomeworkResultErrorDetailBo homeworkResultErrorDetailBo) {
        if (null == homeworkResultErrorDetailBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkResultErrorDetailService.updateHomeworkResultErrorDetail(homeworkResultErrorDetailBo);
    }

    public AjaxResult<HomeworkResultErrorDetailVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkResultErrorDetailService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkResultErrorDetailBo homeworkResultErrorDetailBo = new HomeworkResultErrorDetailBo();
            homeworkResultErrorDetailBo.setOid(oid);
            homeworkResultErrorDetailBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkResultErrorDetailService.updateHomeworkResultErrorDetail(homeworkResultErrorDetailBo);
    }
}
