package com.light.aiszzy.apiRequestLog.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogConditionBo;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogBo;
import com.light.aiszzy.apiRequestLog.entity.vo.ApiRequestLogVo;
import com.light.aiszzy.apiRequestLog.service.IApiRequestLogService;

import com.light.aiszzy.apiRequestLog.api.ApiRequestLogApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 第三方请求日志表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "第三方请求日志表接口")
public class ApiRequestLogController implements ApiRequestLogApi {

    @Autowired
    private IApiRequestLogService apiRequestLogService;

    public AjaxResult<PageInfo<ApiRequestLogVo>> getApiRequestLogPageListByCondition(@RequestBody ApiRequestLogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<ApiRequestLogVo> pageInfo = new PageInfo<>(apiRequestLogService.getApiRequestLogListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<ApiRequestLogVo>> getApiRequestLogListByCondition(@RequestBody ApiRequestLogConditionBo condition) {
        return AjaxResult.success(apiRequestLogService.getApiRequestLogListByCondition(condition));
    }

    public AjaxResult addApiRequestLog(@Validated @RequestBody ApiRequestLogBo apiRequestLogBo) {
        return apiRequestLogService.addApiRequestLog(apiRequestLogBo);
    }

    public AjaxResult updateApiRequestLog(@Validated @RequestBody ApiRequestLogBo apiRequestLogBo) {
        return apiRequestLogService.updateApiRequestLog(apiRequestLogBo);
    }

    public AjaxResult<ApiRequestLogVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(apiRequestLogService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            ApiRequestLogBo apiRequestLogBo = new ApiRequestLogBo();
            apiRequestLogBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return apiRequestLogService.updateApiRequestLog(apiRequestLogBo);
    }
}
