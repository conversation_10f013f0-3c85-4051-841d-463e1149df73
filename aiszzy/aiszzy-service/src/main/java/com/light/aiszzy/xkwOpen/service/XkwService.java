package com.light.aiszzy.xkwOpen.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import com.light.base.attachment.api.AttachmentApi;
import com.light.base.attachment.entity.vo.AttachmentByteVo;
import com.light.beans.OcrBo;
import com.light.beans.SimilarBo;
import com.light.beans.SimilarRecommendBo;
import com.light.beans.SimilarRecommendVo;
import com.light.beans.TikuBo;
import com.light.beans.XkwCourseVo;
import com.light.beans.XkwQuestionVo;
import com.light.beans.XkwTikuSearchResultVo;
import com.light.contants.ConstantsInteger;
import com.light.beans.XkwApiResponseVo;
import com.light.core.entity.AjaxResult;
import com.light.enums.XkwDifficultyLevelEnum;
import com.light.redis.component.RedisComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.google.gson.Gson;
import com.xkw.xop.client.XopHttpClient;

import kong.unirest.HttpResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 学科网工具类
 * 
 * <AUTHOR>
 * @date 2025/3/28 15:19
 */
@Slf4j
@Service
public class XkwService {
    @Value("${xkw.access.key.id:}")
    private String xkwAppId;
    @Value("${xkw.access.key.secret:}")
    private String xkwSecret;

    @Resource
    private ThreadPoolTaskExecutor asyncServiceExecutor;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private AttachmentApi attachmentApi;

    /**
     * 题库搜索url
     */
    private static final String TIKU_SEARCH_URL = "/xopqbm/questions/tiku-search";

    /**
     * 相似题推荐接口url
     */
    private static final String SIMILAR_RECOMMEND_URL = "/xopqbm/questions/similar-recommend";
    /**
     * 相似题推荐时候text最大长度
     */
    private static final Integer SIMILAR_RECOMMEND_TEXT_MAX_LENGTH = 2000;

    /**
     * ocr识别
     */
    private static final String OCR_URL = "/xopqbm/ocr";

    /**
     * 学科网课程信息redis key
     */
    private static final String XKW_COURSE = "xkw:course";
    /**
     * 获取学科网课程id列表
     */
    private static final String COURSES_URL = "/xopqbm/courses/all";
    /**
     * 过期时间 2个月，单位（秒）
     */
    private static final long EXPIRE_TIME = 5184000L;
    /**
     * 学科网初中政治名称
     */
    private static final String CHU_ZHONG_ZZ = "道德与法治";
    /**
     * 学科网高中政治名称
     */
    private static final String GAO_ZHONG_ZZ = "思想政治";

    /**
     * 创建XOP HTTP客户端 使用懒加载模式，提高性能
     */
    private final Supplier<XopHttpClient> clientSupplier = () -> new XopHttpClient.Builder().appId(xkwAppId)
        .secret(xkwSecret).timeout(10).maxConnectionPerRoute(10).build();

    /**
     * 通用HTTP请求处理器 使用函数式接口提高代码复用性
     */
    private <T> Optional<JSONObject> executeRequest(String url, T params,
        Function<XopHttpClient, HttpResponse<String>> requestExecutor) {
        try {
            HttpResponse<String> response = requestExecutor.apply(clientSupplier.get());
            return response.getStatus() == 200 ? Optional.ofNullable(JSONObject.parseObject(response.getBody()))
                : Optional.empty();
        } catch (Exception e) {
            log.error("XKW API: {} request failed", url, e);
            throw new RuntimeException("XKW API: " + url + " request failed: " + e.getMessage());
        }
    }

    /**
     * 发送GET请求
     */
    private Optional<JSONObject> doGet(String url, Map<String, Object> queryParams) {
        return executeRequest(url, queryParams, client -> client.get(url, queryParams));
    }

    /**
     * 发送POST请求
     */
    private Optional<JSONObject> doPost(String url, Map<String, Object> params) {
        return executeRequest(url, params, client -> client.post(url, null, params));
    }

    /**
     * 提取响应数据的通用方法 使用Optional和Stream API优化空值处理
     */
    private Optional<Object> extractResponseData(Optional<JSONObject> responseOpt, String url, String requestInfo) {
        Optional<Object> result =
            responseOpt.filter(json -> {
                // 检查响应码是否成功
                if (!Objects.equals(json.get("code"), ConstantsInteger.HDW_CODE_SUCCESS)) {
                    // 如果不成功，记录错误日志
                    log.error("XKW API: {}, request failed, {}, response code: {}, response: {}",
                        url, requestInfo, json.get("code"), json);
                    return false;
                }
                return true;
            }).map(json -> json.get("data")).filter(data -> data instanceof JSONObject || data instanceof JSONArray);

        if (!result.isPresent()) {
            responseOpt
                .ifPresent(json -> log.error("XKW API: {}, request failed, {}, response: {}", url, requestInfo, json));
        }
        return result;
    }

    /**
     * 返回学科网GET请求的data数据
     */
    public Object doGetData(String url, Map<String, Object> queryParams) {
        return extractResponseData(doGet(url, queryParams), url, "GET request").orElse(null);
    }

    /**
     * 返回学科网POST请求的data数据
     */
    public Object doPostData(String url, Map<String, Object> params) {
        String requestInfo = "POST request: " + JSON.toJSONString(params);
        return extractResponseData(doPost(url, params), url, requestInfo).orElse(null);
    }

    /**
     * 对象转Map的通用方法 使用Optional优化空值处理
     */
    private Map<String, Object> convertToMap(Object requestBody) {
        return Optional.ofNullable(requestBody).map(body -> {
            Gson gson = new Gson();
            return gson.<Map<String, Object>>fromJson(gson.toJson(body),
                new TypeToken<Map<String, Object>>() {}.getType());
        }).orElse(null);
    }

    /**
     * 发送GET请求（支持对象参数） 注意：requestBody不支持json注解，只支持原生的变量名称转换
     */
    public Object doGetDataWithObject(String url, Object requestBody) {
        return doGetData(url, convertToMap(requestBody));
    }

    /**
     * 发送POST请求（支持对象参数） 注意：requestBody不支持json注解，只支持原生的变量名称转换
     */
    public Object doPostDataWithObject(String url, Object requestBody) {
        return doPostData(url, convertToMap(requestBody));
    }

    /**
     * 发送GET请求并返回指定类型的数据
     *
     * @param url 请求URL
     * @param requestBody 请求参数对象
     * @param clazz 返回数据类型
     * @return 指定类型的数据
     */
    public <T> T doGetDataWithType(String url, Object requestBody, Class<T> clazz) {
        Object result = doGetDataWithObject(url, requestBody);
        if (result == null) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(result), clazz);
    }

    /**
     * 发送POST请求并返回指定类型的数据
     *
     * @param url 请求URL
     * @param requestBody 请求参数对象
     * @param clazz 返回数据类型
     * @return 指定类型的数据
     */
    public <T> T doPostDataWithType(String url, Object requestBody, Class<T> clazz) {
        Object result = doPostDataWithObject(url, requestBody);
        if (result == null) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(result), clazz);
    }

    /**
     * 发送GET请求并返回API响应包装对象
     *
     * @param url 请求URL
     * @param requestBody 请求参数对象
     * @param dataClass 数据类型
     * @return API响应包装对象
     */
    public <T> XkwApiResponseVo<T> doGetWithResponse(String url, Object requestBody, Class<T> dataClass) {
        Optional<JSONObject> responseOpt = doGet(url, convertToMap(requestBody));
        if (!responseOpt.isPresent()) {
            return null;
        }

        JSONObject response = responseOpt.get();
        XkwApiResponseVo<T> result = new XkwApiResponseVo<>();
        result.setCode(response.getInteger("code"));
        result.setMessage(response.getString("message"));

        Object dataObj = response.get("data");
        if (dataObj != null && result.isSuccess()) {
            T data = JSON.parseObject(JSON.toJSONString(dataObj), dataClass);
            result.setData(data);
        }

        return result;
    }

    /**
     * 发送POST请求并返回API响应包装对象
     *
     * @param url 请求URL
     * @param requestBody 请求参数对象
     * @param dataClass 数据类型
     * @return API响应包装对象
     */
    public <T> XkwApiResponseVo<T> doPostWithResponse(String url, Object requestBody, Class<T> dataClass) {
        Optional<JSONObject> responseOpt = doPost(url, convertToMap(requestBody));
        if (!responseOpt.isPresent()) {
            return null;
        }

        JSONObject response = responseOpt.get();
        XkwApiResponseVo<T> result = new XkwApiResponseVo<>();
        result.setCode(response.getInteger("code"));
        result.setMessage(response.getString("message"));

        Object dataObj = response.get("data");
        if (dataObj != null && result.isSuccess()) {
            T data = JSON.parseObject(JSON.toJSONString(dataObj), dataClass);
            result.setData(data);
        }

        return result;
    }

    /**
     * 题库搜索-海量版 使用Optional和Stream API优化数据处理
     *
     * @param tikuBo 搜索参数
     * @return 搜索结果
     */
    public AjaxResult<XkwTikuSearchResultVo> tikuSearch(TikuBo tikuBo) {
        Object searchResult = doPostDataWithObject(TIKU_SEARCH_URL, tikuBo);

        return Optional.ofNullable(searchResult).map(result -> {
            try {
                XkwTikuSearchResultVo tikuResult = convertToTikuSearchResult(result);
                processSearchResultData(tikuResult);
                return AjaxResult.success(tikuResult);
            } catch (Exception e) {
                log.error("学科网题目搜索失败，存储题目异常：", e);
                return AjaxResult.fail(ConstantsInteger.SEARCH_ITEM_EMPTY, "题目存储异常");
            }
        }).orElse(AjaxResult.fail(ConstantsInteger.SEARCH_ITEM_EMPTY, "学科网题目搜索失败"));
    }

    /**
     * 题库搜索-海量版 使用Optional和Stream API优化数据处理
     *
     * @param tikuBo 搜索参数
     * @return 搜索结果
     */
    public XkwTikuSearchResultVo tikuSearching(TikuBo tikuBo) {
        Optional<JSONObject> searchResult = doPost(TIKU_SEARCH_URL, convertToMap(tikuBo));

        return searchResult.filter(json -> {
            // 检查响应码是否成功
            if (!Objects.equals(json.get("code"), ConstantsInteger.HDW_CODE_SUCCESS)) {
                // 如果不成功，记录错误日志
                log.error("XKW API: {}, request failed, {}, response code: {}, response: {}",
                        TIKU_SEARCH_URL, JSON.toJSONString(tikuBo), json.get("code"), json);
                return false;
            }
            return true;
        }).map(result -> {
            Object data = result.get("data");
            XkwTikuSearchResultVo tikuResult = convertToTikuSearchResult(data);
            processSearchResultData(tikuResult);
            tikuResult.setImageOcrText(result.getString("image_ocr_text"));
            return tikuResult;
        }).orElse(null);
    }

    /**
     * 相似题推荐 使用Java8 Stream API优化并发处理和结果收集
     *
     * @return
     */
    public AjaxResult<SimilarRecommendVo> similarRecommend(SimilarBo similarBo) {
        // 使用默认难度等级策略
        return similarRecommendInternal(similarBo, null);
    }

    /**
     * 内部相似题推荐方法，支持自定义难度等级
     * 
     * @param similarBo 相似题查询参数
     * @param customDifficultyLevels 自定义难度等级列表，如果为null则使用默认策略
     * @return 相似题推荐结果
     */
    private AjaxResult similarRecommendInternal(SimilarBo similarBo, List<Integer> customDifficultyLevels) {
        // 题干
        String text = similarBo.getText();
        // 题目图片在本系统的oid
        String imageFileOid = similarBo.getImageFileOid();
        // 学科网courseId
        Integer xkwCourseId = similarBo.getXkwCourseId();
        // 题目数量
        Integer count = similarBo.getCount();

        // 调用学科网ocr提取题干
        if (StringUtils.isBlank(text) && StringUtils.isNotBlank(imageFileOid)) {
            AjaxResult ocrAjaxResult = ocr(imageFileOid);
            if (ocrAjaxResult.isFail()) {
                return ocrAjaxResult;
            }
            text = (String)ocrAjaxResult.getData();
        }

        // 如果题干包含html，则只提取文本信息。如果文本信息超过2000则只截取2000字
        if (StringUtils.isNotBlank(text)) {
            // 检查是否包含HTML标签
            if (containsHtmlTags(text)) {
                // 提取HTML中的纯文本内容
                text = extractTextFromHtml(text);
            }

            // 如果文本长度超过2000字符，则截取前2000字符
            if (text.length() > SIMILAR_RECOMMEND_TEXT_MAX_LENGTH) {
                text = text.substring(0, SIMILAR_RECOMMEND_TEXT_MAX_LENGTH);
            }
        }

        // 构造相似题推荐入参并并发执行
        List<SimilarRecommendBo> similarRecommendBos;
        if (customDifficultyLevels != null && !customDifficultyLevels.isEmpty()) {
            // 使用自定义难度等级
            List<XkwDifficultyLevelEnum> customDifficulties = customDifficultyLevels.stream()
                .map(XkwDifficultyLevelEnum::fromCode).filter(Objects::nonNull).collect(Collectors.toList());

            if (customDifficulties.isEmpty()) {
                log.warn("未找到有效的难度等级，使用默认难度等级");
                similarRecommendBos = generateSimilarRecommendBo(text, xkwCourseId);
            } else {
                similarRecommendBos =
                    generateSimilarRecommendBoWithCustomDifficulties(text, xkwCourseId, customDifficulties, count);
            }
        } else {
            // 使用默认难度等级策略
            similarRecommendBos = generateSimilarRecommendBo(text, xkwCourseId);
        }

        // 使用Stream API优化并发处理
        List<
            CompletableFuture<AjaxResult>> futures =
                similarRecommendBos
                    .stream().map(bo -> CompletableFuture
                        .supplyAsync(() -> getSimilarItemRpc(SIMILAR_RECOMMEND_URL, bo), asyncServiceExecutor))
                    .collect(Collectors.toList());

        // 等待所有任务完成并收集结果
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 使用Stream API处理结果，提前返回失败结果
        Optional<AjaxResult> failureResult =
            futures.stream().map(CompletableFuture::join).filter(AjaxResult::isFail).findFirst();

        if (failureResult.isPresent()) {
            return failureResult.get();
        }

        // 收集成功的结果
        List<SimilarRecommendVo> similarRecommendVos = futures.stream().map(CompletableFuture::join)
            .filter(AjaxResult::isSuccess).map(AjaxResult::getData).filter(data -> data instanceof SimilarRecommendVo)
            .map(data -> (SimilarRecommendVo)data).collect(Collectors.toList());

        return AjaxResult.success(similarRecommendVos);
    }

    /**
     * 查询学科网相似题目 直接复用similarRecommend的内部逻辑，避免重复代码
     */
    public List<SimilarRecommendVo> querySimilarQuestions(String questionText, Integer xkwCourseId,
        List<Integer> difficultyLevels, Integer count) {
        try {
            // 构造SimilarBo对象
            SimilarBo similarBo = new SimilarBo();
            similarBo.setText(questionText);
            similarBo.setXkwCourseId(xkwCourseId);
            similarBo.setCount(count);

            // 直接调用内部的相似题推荐方法，支持自定义难度等级
            AjaxResult result = similarRecommendInternal(similarBo, difficultyLevels);

            if (result.isSuccess()) {
                Object data = result.getData();
                if (data instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<SimilarRecommendVo> similarRecommendVos = (List<SimilarRecommendVo>)data;
                    return similarRecommendVos;
                }
            }

            log.warn("查询学科网相似题目返回空结果：{}", result.getMsg());
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("查询学科网相似题目失败：questionText={}, xkwCourseId={}, difficultyLevels={}, count={}", questionText,
                xkwCourseId, difficultyLevels, count, e);
            throw new RuntimeException("查询学科网相似题目失败：" + e.getMessage());
        }
    }

    /**
     * 从Redis或API获取指定课程信息 使用Optional优化空值处理和缓存逻辑
     *
     * @param key 年级+学科
     * @return 课程信息结果
     */
    private AjaxResult getXkwCourseByKey(String key) {
        // 先尝试从Redis获取
        Optional<XkwCourseVo> cachedCourse = getCourseFromRedis(key);
        if (cachedCourse.isPresent()) {
            return AjaxResult.success(cachedCourse.get());
        }

        // Redis中没有，从API获取并缓存
        return fetchAndCacheCourses(key);
    }

    /**
     * 从Redis获取课程信息
     */
    private Optional<XkwCourseVo> getCourseFromRedis(String key) {
        if (!redisComponent.hasKey(XKW_COURSE)) {
            return Optional.empty();
        }

        return Optional.ofNullable(redisComponent.hget(XKW_COURSE, key)).filter(obj -> obj instanceof JSONObject)
            .map(obj -> (JSONObject)obj).map(json -> JSON.parseObject(json.toJSONString(), XkwCourseVo.class));
    }

    /**
     * 从API获取课程列表并缓存到Redis
     */
    private AjaxResult fetchAndCacheCourses(String key) {
        Object courseData = doGetDataWithObject(COURSES_URL, null);
        if (Objects.isNull(courseData)) {
            return AjaxResult.fail("调用学科网获取课程信息失败");
        }

        List<XkwCourseVo> courses = JSON.parseArray(JSON.toJSONString(courseData), XkwCourseVo.class);
        if (courses == null || courses.isEmpty()) {
            return AjaxResult.fail("课程数据解析失败");
        }

        // 使用Stream API构建缓存Map
        Map<String, Object> courseMap =
            courses.stream().collect(Collectors.toMap(XkwCourseVo::getName, Function.identity()));

        redisComponent.hmset(XKW_COURSE, courseMap, EXPIRE_TIME);

        XkwCourseVo targetCourse = (XkwCourseVo)courseMap.get(key);
        return AjaxResult.success(targetCourse);
    }

    /**
     * 构造学科网相似题推荐入参 使用Java8 Stream API和函数式编程减少重复代码
     *
     * @param text 题干
     * @param xkwCourseId 学科网课程ID
     * @return 相似题推荐请求列表
     */
    private List<SimilarRecommendBo> generateSimilarRecommendBo(String text, Integer xkwCourseId) {
        // 定义默认的难度等级策略
        List<XkwDifficultyLevelEnum> difficultyLevels = Arrays.asList(XkwDifficultyLevelEnum.AVERAGE,
            XkwDifficultyLevelEnum.HARD, XkwDifficultyLevelEnum.VERY_HARD);

        return difficultyLevels.stream().map(difficulty -> createSimilarRecommendBo(text, xkwCourseId, difficulty, 1))
            .collect(Collectors.toList());
    }

    /**
     * 扩展方法：支持自定义难度等级的相似题推荐 提高可扩展性
     */
    private List<SimilarRecommendBo> generateSimilarRecommendBoWithCustomDifficulties(String text, Integer xkwCourseId,
        List<XkwDifficultyLevelEnum> customDifficulties, Integer count) {
        return customDifficulties.stream()
            .map(difficulty -> createSimilarRecommendBo(text, xkwCourseId, difficulty, count))
            .collect(Collectors.toList());
    }

    /**
     * 根据策略生成相似题推荐请求 支持策略模式，提高扩展性
     */
    private List<SimilarRecommendBo> generateSimilarRecommendBoByStrategy(String text, Integer xkwCourseId,
        RecommendStrategy strategy, Integer count) {
        return strategy.getDifficulties().stream()
            .map(difficulty -> createSimilarRecommendBo(text, xkwCourseId, difficulty, count))
            .collect(Collectors.toList());
    }

    /**
     * 创建单个相似题推荐请求对象 使用Builder模式的思想简化对象创建
     */
    private SimilarRecommendBo createSimilarRecommendBo(String text, Integer courseId,
        XkwDifficultyLevelEnum difficulty, Integer count) {
        SimilarRecommendBo bo = new SimilarRecommendBo();
        bo.setText(text);
        bo.setDifficulty_levels(Collections.singletonList(difficulty.getCode()));
        bo.setCount(count);
        bo.setCourse_id(courseId);
        return bo;
    }

    /**
     * 推荐策略枚举 - 支持扩展不同的推荐策略
     */
    public enum RecommendStrategy {
        DEFAULT(Arrays.asList(XkwDifficultyLevelEnum.AVERAGE, XkwDifficultyLevelEnum.HARD,
            XkwDifficultyLevelEnum.VERY_HARD)),
        EASY_FOCUS(Arrays.asList(XkwDifficultyLevelEnum.EASY, XkwDifficultyLevelEnum.FAIRLY_EASY,
            XkwDifficultyLevelEnum.AVERAGE)),
        HARD_FOCUS(Arrays.asList(XkwDifficultyLevelEnum.AVERAGE, XkwDifficultyLevelEnum.HARD,
            XkwDifficultyLevelEnum.VERY_HARD)),
        ALL_LEVELS(Arrays.asList(XkwDifficultyLevelEnum.values()));

        private final List<XkwDifficultyLevelEnum> difficulties;

        RecommendStrategy(List<XkwDifficultyLevelEnum> difficulties) {
            this.difficulties = difficulties;
        }

        public List<XkwDifficultyLevelEnum> getDifficulties() {
            return difficulties;
        }
    }

    /**
     * 将原始搜索结果转换为题库搜索结果对象
     */
    private XkwTikuSearchResultVo convertToTikuSearchResult(Object result) {
        XkwTikuSearchResultVo tikuResult = new XkwTikuSearchResultVo();

        if (result instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject)result;
            JSONObject dataObj = jsonObject.getJSONObject("data");
            if (dataObj != null) {
                XkwQuestionVo question = JSON.parseObject(dataObj.toJSONString(), XkwQuestionVo.class);
                tikuResult.setData(question);
            }
        } else if (result instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray)result;
            List<XkwQuestionVo> questions = jsonArray.stream().filter(obj -> obj instanceof JSONObject)
                .map(obj -> (JSONObject)obj).map(json -> JSON.parseObject(json.toJSONString(), XkwQuestionVo.class))
                .collect(Collectors.toList());
            tikuResult.setQuestions(questions);
        }

        return tikuResult;
    }

    /**
     * 处理题库搜索结果数据，异步保存题目
     */
    private void processSearchResultData(XkwTikuSearchResultVo tikuResult) {
        if (tikuResult.getData() != null) {
            // 处理单个题目
            XkwQuestionVo question = tikuResult.getData();
            saveItemAsync(question);
        } else if (tikuResult.getQuestions() != null) {
            // 处理题目列表
            tikuResult.getQuestions().forEach(this::saveItemAsync);
        }
    }

    /**
     * 获取学科网相似题（一道题目）
     * 
     * @param url
     * @param similarRecommendBo
     * @return
     */
    private AjaxResult getSimilarItemRpc(String url, SimilarRecommendBo similarRecommendBo) {
        Object responseData = doPostDataWithObject(url, similarRecommendBo);
        if (Objects.nonNull(responseData)) {
            try {
                List<SimilarRecommendVo> similarRecommendVos =
                    JSON.parseArray(JSON.toJSONString(responseData), SimilarRecommendVo.class);
                if (similarRecommendVos != null && !similarRecommendVos.isEmpty()) {
                    SimilarRecommendVo similarRecommendVo = similarRecommendVos.get(0);
                    return AjaxResult.success(similarRecommendVo);
                }
            } catch (Exception e) {
                log.error("相似题推荐结果序列化异常：", e);
                return AjaxResult.fail("序列化结果异常");
            }
        }
        return AjaxResult.success(null);
    }

    /**
     * 学科网OCR识别,data里面是txt文本内容 使用Optional优化空值处理和错误处理
     *
     * @param fileOid 文件OID
     * @return OCR识别结果
     */
    private AjaxResult ocr(String fileOid) {
        try {
            // 下载文件
            AjaxResult<AttachmentByteVo> downloadResult = attachmentApi.readFileNoLog(fileOid);
            if (downloadResult.isFail()) {
                log.error("下载文件失败: fileOid={}, error={}", fileOid, downloadResult.getMsg());
                return AjaxResult.fail("文件下载失败: " + downloadResult.getMsg());
            }

            AttachmentByteVo attachmentByteVo = downloadResult.getData();
            if (attachmentByteVo == null || attachmentByteVo.getBytes() == null) {
                log.error("文件内容为空: fileOid={}", fileOid);
                return AjaxResult.fail("文件内容为空");
            }

            // 转换为Base64并调用OCR
            String imageBase64 = Base64.getEncoder().encodeToString(attachmentByteVo.getBytes());
            OcrBo ocrBo = new OcrBo();
            ocrBo.setImage_base64(imageBase64);

            log.info("开始调用学科网OCR识别, fileOid={}, imageSize={}", fileOid, attachmentByteVo.getBytes().length);
            Object ocrResult = doPostDataWithObject(OCR_URL, ocrBo);

            // 使用Optional处理OCR结果
            return Optional.ofNullable(ocrResult).filter(result -> result instanceof JSONObject)
                .map(result -> (JSONObject)result).map(json -> json.getString("text")).filter(StringUtils::isNotBlank)
                .map(text -> {
                    log.info("OCR识别成功, fileOid={}, textLength={}", fileOid, text.length());
                    return AjaxResult.success(text);
                }).orElseGet(() -> {
                    if (Objects.isNull(ocrResult)) {
                        log.error("OCR识别返回空, fileOid={}", fileOid);
                        return AjaxResult.fail("OCR识别返回空");
                    } else if (!(ocrResult instanceof JSONObject)) {
                        log.error("学科网OCR识别返回结构不符合预期, fileOid={}, resultType={}", fileOid,
                            ocrResult.getClass().getSimpleName());
                        return AjaxResult.fail("学科网OCR识别返回结构不符合预期");
                    } else {
                        log.error("OCR识别返回结果为空, fileOid={}, result={}", fileOid, ocrResult);
                        return AjaxResult.fail("OCR识别返回结果为空");
                    }
                });

        } catch (Exception e) {
            log.error("OCR识别异常, fileOid={}", fileOid, e);
            return AjaxResult.fail("OCR识别异常: " + e.getMessage());
        }
    }

    /**
     * 辅助方法：异步保存题目（使用题目对象） 使用Optional优化空值处理，提高代码健壮性
     *
     * @param xkwQuestionVo 题目对象
     */
    private void saveItemAsync(XkwQuestionVo xkwQuestionVo) {
        Optional.ofNullable(xkwQuestionVo).filter(q -> StringUtils.isNotBlank(q.getId())).ifPresent(q -> {
            CompletableFuture.runAsync(() -> {
                try {
                    // TODO: 需要添加ItemBo和ItemSourceType的import
                    // 以及baseDataApi和itemApi的注入
                    log.info("异步保存题目: thirdItemId={}, questionType={}, difficulty={}", q.getId(),
                        q.getType() != null ? q.getType().getName() : "未知", q.getDifficulty_level());

                    // 实际保存逻辑需要根据项目中的具体实现来完成
                    // ItemBo itemBo = new ItemBo();
                    // itemBo.setItemSourceType(ItemSourceType.XKW.getValue());
                    // itemBo.setThirdItemId(xkwQuestionVo.getId());
                    // itemBo.setItemJson(JSON.toJSONString(xkwQuestionVo));
                    // itemApi.saveItem(itemBo);
                } catch (Exception e) {
                    log.error("异步保存题目失败: thirdItemId={}", q.getId(), e);
                }
            }, asyncServiceExecutor);
        });
    }

    /**
     * 检查文本是否包含HTML标签
     *
     * @param text 待检查的文本
     * @return 如果包含HTML标签返回true，否则返回false
     */
    private boolean containsHtmlTags(String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        // 使用正则表达式检查是否包含HTML标签
        // 匹配 <tag> 或 </tag> 或 <tag/> 格式的标签
        return text.matches(".*<[^>]+>.*");
    }

    /**
     * 从HTML文本中提取纯文本内容
     * 移除所有HTML标签，保留文本内容
     *
     * @param htmlText 包含HTML标签的文本
     * @return 提取后的纯文本内容
     */
    private String extractTextFromHtml(String htmlText) {
        if (StringUtils.isBlank(htmlText)) {
            return htmlText;
        }

        try {
            // 移除HTML标签，保留文本内容
            String text = htmlText.replaceAll("<[^>]+>", "");

            // 处理HTML实体字符
            text = text.replace("&nbsp;", " ")
                      .replace("&lt;", "<")
                      .replace("&gt;", ">")
                      .replace("&amp;", "&")
                      .replace("&quot;", "\"")
                      .replace("&#39;", "'")
                      .replace("&apos;", "'");

            // 清理多余的空白字符
            text = text.replaceAll("\\s+", " ").trim();

            return text;
        } catch (Exception e) {
            log.warn("HTML文本提取失败，返回原始文本: {}", e.getMessage());
            return htmlText;
        }
    }
}
