package com.light.aiszzy.xkw.xkwTextbookVersions.controller;

import java.util.List;

import javax.annotation.Resource;

import com.light.aiszzy.xkw.xkwCourses.entity.vo.XkwCoursesVo;
import com.light.aiszzy.xkw.xkwCourses.service.IXkwCoursesService;
import com.light.aiszzy.xkw.xkwTextbookVersions.service.IXkwTextbookVersionsService;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsConditionBo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo;

import com.light.aiszzy.xkw.xkwTextbookVersions.api.XkwTextbookVersionsApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

/**
 * 教材版本
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
@RestController
@Validated
@Api(value = "", tags = "教材版本接口")
public class XkwTextbookVersionsController implements XkwTextbookVersionsApi {

    @Autowired
    private IXkwTextbookVersionsService xkwTextbookVersionsService;

    @Resource
    private IXkwCoursesService xkwCoursesService;

    public AjaxResult<PageInfo<XkwTextbookVersionsVo>> getXkwTextbookVersionsPageListByCondition(@RequestBody XkwTextbookVersionsConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<XkwTextbookVersionsVo> pageInfo = new PageInfo<>(xkwTextbookVersionsService.getXkwTextbookVersionsListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());

    }

    public AjaxResult<List<XkwTextbookVersionsVo>> getXkwTextbookVersionsListByCondition(@RequestBody XkwTextbookVersionsConditionBo condition) {
        return AjaxResult.success(xkwTextbookVersionsService.getXkwTextbookVersionsListByCondition(condition));
    }

    @Override
    public AjaxResult<List<XkwTextbookVersionsVo>> getByStageAndSubject(@RequestParam("stage") Integer stage, @RequestParam("subject") String subject) {
        XkwCoursesVo xkwCoursesVo = this.xkwCoursesService.queryByStageAndSubject(stage, subject);
        if(xkwCoursesVo == null) {
            return AjaxResult.success();
        }
        XkwTextbookVersionsConditionBo bo = new XkwTextbookVersionsConditionBo();
        bo.setCourseId(xkwCoursesVo.getId());
        return AjaxResult.success(xkwTextbookVersionsService.getXkwTextbookVersionsListByCondition(bo));
    }

}
