package com.light.aiszzy.statistics.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.vo.SchoolGradeQuestionStatisticsVo;
import com.light.aiszzy.statistics.service.ISchoolGradeQuestionStatisticsService;

import com.light.aiszzy.statistics.api.SchoolGradeQuestionStatisticsApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 作业年级题目正确率
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@RestController
@Validated
@Api(value = "", tags = "作业年级题目正确率接口")
public class SchoolGradeQuestionStatisticsController implements SchoolGradeQuestionStatisticsApi {

    @Autowired
    private ISchoolGradeQuestionStatisticsService schoolGradeQuestionStatisticsService;

    public AjaxResult<PageInfo<SchoolGradeQuestionStatisticsVo>> getSchoolGradeQuestionStatisticsPageListByCondition(@RequestBody SchoolGradeQuestionStatisticsConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<SchoolGradeQuestionStatisticsVo> pageInfo = new PageInfo<>(schoolGradeQuestionStatisticsService.getSchoolGradeQuestionStatisticsListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<SchoolGradeQuestionStatisticsVo>> getSchoolGradeQuestionStatisticsListByCondition(@RequestBody SchoolGradeQuestionStatisticsConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(schoolGradeQuestionStatisticsService.getSchoolGradeQuestionStatisticsListByCondition(condition));
    }

    public AjaxResult addSchoolGradeQuestionStatistics(@Validated @RequestBody SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo) {
        return schoolGradeQuestionStatisticsService.addSchoolGradeQuestionStatistics(schoolGradeQuestionStatisticsBo);
    }

    public AjaxResult updateSchoolGradeQuestionStatistics(@Validated @RequestBody SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo) {
        if (null == schoolGradeQuestionStatisticsBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return schoolGradeQuestionStatisticsService.updateSchoolGradeQuestionStatistics(schoolGradeQuestionStatisticsBo);
    }

    public AjaxResult<SchoolGradeQuestionStatisticsVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(schoolGradeQuestionStatisticsService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo = new SchoolGradeQuestionStatisticsBo();
            schoolGradeQuestionStatisticsBo.setOid(oid);
            schoolGradeQuestionStatisticsBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return schoolGradeQuestionStatisticsService.updateSchoolGradeQuestionStatistics(schoolGradeQuestionStatisticsBo);
    }
}
