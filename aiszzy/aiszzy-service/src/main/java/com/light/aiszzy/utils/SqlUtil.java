package com.light.aiszzy.utils;

import cn.hutool.core.util.ReflectUtil;


public class SqlUtil {

    public static String sanitizeOrderBy(String orderBy, Class<?> clazz) {
        // 允许的排序字段
        if (orderBy == null || orderBy.isEmpty()) {
            return "";
        }

        String[] parts = orderBy.split(" ");
        // 超过2，不是column + " " + direction
        if (parts.length > 2) {
            return "";
        }
        String column = parts[0];
        String direction = parts.length > 1 ? parts[1] : "ASC";

        //处理a.createTime这种情况
        if(column.contains(".")){
            column = extName(column);
        }
        // 检查字段是否合法
        if (!ReflectUtil.hasField(clazz, column)) {
            return "";
        }

        // 检查排序方向是否合法
        if (!"ASC".equalsIgnoreCase(direction) && !"DESC".equalsIgnoreCase(direction)) {
            return "";
        }

        return column + " " + direction;
    }

    public static String extName(String columnName) {
        if (columnName == null) {
            return null;
        } else {
            int index = columnName.lastIndexOf(".");
            if (index == -1) {
                return "";
            } else {
                return columnName.substring(index + 1);
            }
        }
    }

}
