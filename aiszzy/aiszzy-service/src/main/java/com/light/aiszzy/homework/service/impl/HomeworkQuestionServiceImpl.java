package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.aiszzy.question.entity.dto.QuestionDto;
import com.light.aiszzy.question.mapper.QuestionMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkQuestionDto;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.aiszzy.homework.service.IHomeworkQuestionService;
import com.light.aiszzy.homework.mapper.HomeworkQuestionMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 校本作业题目信息接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class HomeworkQuestionServiceImpl extends ServiceImpl<HomeworkQuestionMapper, HomeworkQuestionDto> implements IHomeworkQuestionService {

    @Resource
    private HomeworkQuestionMapper homeworkQuestionMapper;

    @Resource
    private QuestionMapper questionMapper;

    @Override
    public List<HomeworkQuestionVo> getHomeworkQuestionListByCondition(HomeworkQuestionConditionBo condition) {
        return homeworkQuestionMapper.getHomeworkQuestionListByCondition(condition);
    }

    @Override
    public AjaxResult addHomeworkQuestion(HomeworkQuestionBo homeworkQuestionBo) {
        HomeworkQuestionDto homeworkQuestion = new HomeworkQuestionDto();
        BeanUtils.copyProperties(homeworkQuestionBo, homeworkQuestion);
        homeworkQuestion.setIsDelete(StatusEnum.NOTDELETE.getCode());
        homeworkQuestion.setOid(IdUtil.simpleUUID());
        if (save(homeworkQuestion)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateHomeworkQuestion(HomeworkQuestionBo homeworkQuestionBo) {
        LambdaQueryWrapper<HomeworkQuestionDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkQuestionDto::getOid, homeworkQuestionBo.getOid());
        HomeworkQuestionDto homeworkQuestion = getOne(lqw);
        Long id = homeworkQuestion.getId();
        if (homeworkQuestion == null) {
            return AjaxResult.fail("保存失败");
        }
        BeanUtils.copyProperties(homeworkQuestionBo, homeworkQuestion);
        homeworkQuestion.setId(id);
        if (updateById(homeworkQuestion)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public HomeworkQuestionVo getDetail(String oid) {
        LambdaQueryWrapper<HomeworkQuestionDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkQuestionDto::getOid, oid);
        HomeworkQuestionDto homeworkQuestion = getOne(lqw);
        HomeworkQuestionVo homeworkQuestionVo = new HomeworkQuestionVo();
        if (homeworkQuestion != null) {
            BeanUtils.copyProperties(homeworkQuestion, homeworkQuestionVo);
        }
        return homeworkQuestionVo;
    }

    @Override
    @Transactional
    public AjaxResult changeQuestion(String oid, String oldQuestionOid, String newQuestionOid) {
        LambdaUpdateWrapper<HomeworkQuestionDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HomeworkQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        updateWrapper.eq(HomeworkQuestionDto::getHomeworkOid, oid);
        updateWrapper.eq(HomeworkQuestionDto::getQuestionOid, oldQuestionOid);
        update(updateWrapper);

        List<QuestionDto> questionDtos = questionMapper.selectList(new LambdaQueryWrapper<QuestionDto>()
                .eq(QuestionDto::getOid, oid)
                .eq(QuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
        if (CollectionUtil.isEmpty(questionDtos) || questionDtos.size() > 1) {
            AjaxResult.fail("获取题目失败");
        }
        QuestionDto questionDto = questionDtos.get(0);
        HomeworkQuestionDto dto = new HomeworkQuestionDto();
        BeanUtils.copyProperties(questionDto, dto);
        dto.setOid(IdUtil.simpleUUID());
        dto.setHomeworkOid(oid);
        dto.setQuestionOid(newQuestionOid);
        dto.setId(null);
        dto.setCreateBy(null);
        dto.setUpdateBy(null);
        dto.setCreateTime(null);
        dto.setUpdateTime(null);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteQuestion(String oid, String questionOid) {
        LambdaUpdateWrapper<HomeworkQuestionDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HomeworkQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        updateWrapper.eq(HomeworkQuestionDto::getHomeworkOid, oid);
        updateWrapper.eq(HomeworkQuestionDto::getQuestionOid, questionOid);
        update(updateWrapper);
        return AjaxResult.success();
    }

}