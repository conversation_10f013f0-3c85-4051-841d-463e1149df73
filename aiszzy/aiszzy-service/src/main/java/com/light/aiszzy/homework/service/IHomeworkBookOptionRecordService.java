package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkBookOptionRecordDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookOptionRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 作业本映射印送记录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface IHomeworkBookOptionRecordService extends IService<HomeworkBookOptionRecordDto> {

    List<HomeworkBookOptionRecordVo> getHomeworkBookOptionRecordListByCondition(HomeworkBookOptionRecordConditionBo condition);

    AjaxResult getHomeworkBookOptionRecordCountByCondition(HomeworkBookOptionRecordConditionBo condition);

	AjaxResult addHomeworkBookOptionRecord(HomeworkBookOptionRecordBo homeworkBookOptionRecordBo);

	AjaxResult updateHomeworkBookOptionRecord(HomeworkBookOptionRecordBo homeworkBookOptionRecordBo);

    HomeworkBookOptionRecordVo getDetail(String oid);

}

