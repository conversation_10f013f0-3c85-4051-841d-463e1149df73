package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.homework.entity.bo.HomeworkBookBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkConditionBo;
import com.light.aiszzy.homework.entity.dto.*;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.aiszzy.homework.mapper.HomeworkBookMapper;
import com.light.aiszzy.homework.mapper.HomeworkMapper;
import com.light.aiszzy.homework.service.IHomeworkBookService;
import com.light.aiszzy.hwlOpen.tal.ailab.util.DateUtil;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.service.impl.PracticeBookCatalogServiceImpl;
import com.light.aiszzy.practiceBook.service.impl.PracticeBookQuestionServiceImpl;
import com.light.aiszzy.practiceBook.service.impl.PracticeBookServiceImpl;
import com.light.aiszzy.question.entity.bo.QuestionWithPracticeBo;
import com.light.aiszzy.question.mapper.QuestionMapper;
import com.light.aiszzy.xkw.xkwCourses.entity.dto.XkwCoursesDto;
import com.light.aiszzy.xkw.xkwCourses.mapper.XkwCoursesMapper;
import com.light.aiszzy.xkw.xkwTextbook.entity.dto.XkwTextbookDto;
import com.light.aiszzy.xkw.xkwTextbook.mapper.XkwTextbookMapper;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.dto.XkwTextbookCatalogDto;
import com.light.aiszzy.xkw.xkwTextbookCatalog.mapper.XkwTextbookCatalogMapper;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.dto.XkwTextbookVersionsDto;
import com.light.aiszzy.xkw.xkwTextbookVersions.mapper.XkwTextbookVersionsMapper;
import com.light.contants.AISzzyConstants;
import com.light.contants.ConstantsInteger;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.WarningException;
import com.light.core.utils.StringUtils;
import com.light.redis.component.DictTranslationComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 作业本接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Slf4j
@Service
public class HomeworkBookServiceImpl extends ServiceImpl<HomeworkBookMapper, HomeworkBookDto> implements IHomeworkBookService {

    @Resource
    private HomeworkMapper homeworkMapper;

    @Resource
    private HomeworkBookMapper homeworkBookMapper;

    @Resource
    private HomeworkBookCatalogServiceImpl homeworkBookCatalogService;

    @Resource
    private HomeworkBookUserServiceImpl homeworkBookUserService;

    @Resource
    private HomeworkServiceImpl homeworkService;

    @Resource
    private HomeworkQuestionServiceImpl homeworkQuestionService;

    @Resource
    private HomeworkBookCatalogInfoServiceImpl homeworkBookCatalogInfoService;

    @Resource
    private PracticeBookServiceImpl practiceBookService;

    @Resource
    private PracticeBookCatalogServiceImpl practiceBookCatalogService;

    @Resource
    private PracticeBookQuestionServiceImpl practiceBookQuestionService;

    @Autowired
    private DictTranslationComponent dictTranslationComponent;

    @Autowired
    private XkwCoursesMapper xkwCoursesMapper;

    @Autowired
    private XkwTextbookVersionsMapper xkwTextbookVersionsMapper;

    @Autowired
    private XkwTextbookCatalogMapper xkwTextbookCatalogMapper;

    @Autowired
    private XkwTextbookMapper xkwTextbookMapper;

    @Autowired
    private QuestionMapper questionMapper;

    @Override
    public List<HomeworkBookVo> getHomeworkBookListByCondition(HomeworkBookConditionBo condition) {
        return dictTranslationComponent.translationList(homeworkBookMapper.getHomeworkBookListByCondition(condition));
    }

    @Override
    public Map getHomeworkBookGroupListByCondition(HomeworkBookConditionBo condition) {
        List<HomeworkBookVo> homeworkBookVos = dictTranslationComponent.translationList(homeworkBookMapper.getHomeworkBookListByCondition(condition));
        Map<Integer, List<HomeworkBookVo>> collect = homeworkBookVos.stream().collect(Collectors.groupingBy(HomeworkBookVo::getSubject));
        return collect;
    }

    @Override
    public List<HomeworkBookVo> getSchoolNotAddHomeworkBookListByCondition(HomeworkBookConditionBo condition) {
        return dictTranslationComponent.translationList(homeworkBookMapper.getSchoolNotAddHomeworkBookListByCondition(condition));
    }

    @Override
    public AjaxResult addHomeworkBook(HomeworkBookBo homeworkBookBo) {
        HomeworkBookDto homeworkBook = new HomeworkBookDto();
        BeanUtils.copyProperties(homeworkBookBo, homeworkBook);
        homeworkBook.setIsDelete(StatusEnum.NOTDELETE.getCode());
        homeworkBook.setOid(IdUtil.simpleUUID());
        if (save(homeworkBook)) {
            return AjaxResult.success(homeworkBook);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateHomeworkBook(HomeworkBookBo homeworkBookBo) {
        LambdaQueryWrapper<HomeworkBookDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkBookDto::getOid, homeworkBookBo.getOid());
        HomeworkBookDto homeworkBook = getOne(lqw);
        Long id = homeworkBook.getId();
        if (homeworkBook == null) {
            return AjaxResult.fail("保存失败");
        }
        BeanUtils.copyProperties(homeworkBookBo, homeworkBook);
        homeworkBook.setId(id);
        if (updateById(homeworkBook)) {
            return AjaxResult.success(homeworkBook);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCurrentBook(HomeworkBookBo homeworkBookBo) {
        HomeworkBookUserDto dto = new HomeworkBookUserDto();
        List<HomeworkBookUserDto> list = homeworkBookUserService.list(new LambdaQueryWrapper<HomeworkBookUserDto>()
                .eq(HomeworkBookUserDto::getOrgCode, homeworkBookBo.getOrgCode())
                .eq(HomeworkBookUserDto::getCreateBy, homeworkBookBo.getCreateBy())
                .eq(HomeworkBookUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
        if(CollectionUtil.isNotEmpty(list)) {
            dto = list.get(0);
        }else{
            dto.setOid(IdUtil.simpleUUID());
        }
        dto.setOrgCode(homeworkBookBo.getOrgCode());
        dto.setHomeworkBookOid(homeworkBookBo.getHomeworkBookOid());
        homeworkBookUserService.saveOrUpdate(dto);
        return AjaxResult.success();
    }

    @Override
    public HomeworkBookVo getDetail(String oid) {
        HomeworkBookConditionBo bo = new HomeworkBookConditionBo();
        bo.setOid(oid);
        bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<HomeworkBookVo> homeworkBookListByCondition = getHomeworkBookListByCondition(bo);
        HomeworkBookVo homeworkBookVo = new HomeworkBookVo();
        if (CollectionUtil.isNotEmpty(homeworkBookListByCondition)) {
            BeanUtils.copyProperties(homeworkBookListByCondition.get(0), homeworkBookVo);
            HomeworkConditionBo childHomeworkbo = new HomeworkConditionBo();
            childHomeworkbo.setHasBind("yes");
            childHomeworkbo.setHomeworkBookOid(oid);
            childHomeworkbo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            List<HomeworkVo> homeworkChildListByCondition = homeworkMapper.getHomeworkListByCondition(childHomeworkbo);
            homeworkBookVo.setHasHomework(false);
            homeworkBookVo.setHasCheckHomework(false);
            homeworkBookVo.setHasCatalog(false);
            if (CollectionUtil.isNotEmpty(homeworkChildListByCondition)) {
                homeworkBookVo.setHasHomework(true);
                Boolean b = homeworkChildListByCondition.stream().anyMatch(obj -> obj.getStatus().equals(StatusEnum.YES.getCode()));
                homeworkBookVo.setHasCheckHomework(b);
            }
            List<HomeworkBookCatalogDto> homeworkBookCatalogDtos = homeworkBookCatalogService.list(new LambdaUpdateWrapper<HomeworkBookCatalogDto>()
                    .eq(HomeworkBookCatalogDto::getHomeworkBookOid, oid)
                    .eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
            homeworkBookVo.setHasCatalog(CollectionUtil.isNotEmpty(homeworkBookCatalogDtos));
            XkwTextbookVersionsDto xkwTextbookVersionsDto = xkwTextbookVersionsMapper.selectOne(new LambdaUpdateWrapper<XkwTextbookVersionsDto>().eq(XkwTextbookVersionsDto::getId, homeworkBookVo.getTextBookVersionId()));

            if (xkwTextbookVersionsDto != null) {
                homeworkBookVo.setTextBookVersionName(xkwTextbookVersionsDto.getName());
            }
        }
        return homeworkBookVo;
    }

    @Override
    public AjaxResult downloadBookZip(String oid) {
        Map<String, Object> result = new HashMap<String, Object>(4);

        LambdaQueryWrapper<HomeworkBookDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkBookDto::getOid, oid);
        HomeworkBookDto one = getOne(lqw);
        result.put("name", one.getName());

        List<String> pathArr = new ArrayList();
        List<String> streamArr = new ArrayList();
        List<Map> catalogAndHomeworkForDownload = homeworkBookMapper.getCatalogAndHomeworkForDownload(oid);
        if (CollectionUtil.isNotEmpty(catalogAndHomeworkForDownload)) {
            for (Map map : catalogAndHomeworkForDownload) {
                String name = map.get("name").toString();
                String answerName = map.get("name").toString();
                String homeworkName = map.get("homeworkName").toString();
                String parentOid = map.get("parentOid").toString();
                String pdfUrl = map.get("pdfUrl").toString();
                String answerUrl = map.get("answerUrl").toString();
                name = name + "/" + homeworkName + "." + FileUtil.extName(pdfUrl);
                answerName = answerName + "/" + homeworkName + "-答案解析." + FileUtil.extName(answerUrl);

                while (StringUtils.isNotEmpty(parentOid) && !parentOid.equals("0")) {
                    LambdaQueryWrapper<HomeworkBookCatalogDto> cataloglqw = new LambdaQueryWrapper<>();
                    cataloglqw.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
                    cataloglqw.eq(HomeworkBookCatalogDto::getOid, parentOid);

                    HomeworkBookCatalogDto homeworkBookCatalogDto = homeworkBookCatalogService.getOne(cataloglqw);
                    name = homeworkBookCatalogDto.getName() + "/" + name;
                    answerName = homeworkBookCatalogDto.getName() + "/" + answerName;
                    parentOid = homeworkBookCatalogDto.getParentOid();
                }
                pathArr.add(name);
                pathArr.add(answerName);
                streamArr.add(pdfUrl);
                streamArr.add(answerUrl);
            }
        }
        result.put("pathArr", String.join(",", pathArr));
        result.put("streamArr", String.join(",", streamArr));

        return AjaxResult.success(result);

    }

    @Override
    public AjaxResult downloadBookPdf(String oid) {
        Map<String, Object> result = new HashMap<String, Object>(4);

        LambdaQueryWrapper<HomeworkBookDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkBookDto::getOid, oid);
        HomeworkBookDto one = getOne(lqw);
        result.put("name", one.getName());
        List<String> pathArr = new ArrayList();

        List<String> allPdf = new ArrayList();
        List<String> allAnswerPdf = new ArrayList();
        List<Map> catalogAndHomeworkForDownload = homeworkBookMapper.getCatalogAndHomeworkForDownload(oid);
        if (CollectionUtil.isNotEmpty(catalogAndHomeworkForDownload)) {
            for (Map map : catalogAndHomeworkForDownload) {
                String pdfUrl = map.get("pdfUrl").toString();
                String answerUrl = map.get("answerUrl").toString();
                if (StringUtils.isNotEmpty(pdfUrl)) {
                    allPdf.add(pdfUrl);
                }
                if (StringUtils.isNotEmpty(answerUrl)) {
                    allPdf.add(answerUrl);
                }
            }
        }
        pathArr.add(one.getName() + ".pdf");
        pathArr.add(one.getName() + "-答案解析.docx");
        result.put("pathArr", String.join(",", pathArr));
        result.put("pdfUrl", String.join(",", allPdf));
        result.put("answerPdfUrl", String.join(",", allAnswerPdf));

        return AjaxResult.success(result);
    }

    @Override
    public AjaxResult textbookVersions(String grade, String subject) {
        Integer gradeId = Integer.valueOf(grade);
        Integer stageId = 0;
        if (gradeId < 7) {
            stageId = 2;
        } else if (gradeId > 9) {
            stageId = 4;
        } else {
            stageId = 3;
        }
        //29手动维护的政治，转换成学科网的道德法制
        if (subject.equals("30")) {
            subject = "7";
        }
        XkwCoursesDto xkwCoursesDto = xkwCoursesMapper.selectOne(new LambdaUpdateWrapper<XkwCoursesDto>()
                .eq(XkwCoursesDto::getSubjectId, subject)
                .eq(XkwCoursesDto::getStageId, stageId));
        if (xkwCoursesDto == null) {
            return AjaxResult.fail("查询课程错误");
        }
        List<XkwTextbookVersionsDto> xkwTextbookVersionsDtos = xkwTextbookVersionsMapper.selectList(new LambdaUpdateWrapper<XkwTextbookVersionsDto>()
                .eq(XkwTextbookVersionsDto::getCourseId, xkwCoursesDto.getId()));
        return AjaxResult.success(xkwTextbookVersionsDtos);
    }

    @Override
    public AjaxResult textbook(String versionId, String gradeId) {
        LambdaUpdateWrapper<XkwTextbookDto> lqw = new LambdaUpdateWrapper<>();
        if (StringUtils.isNotEmpty(gradeId)) {
            lqw.eq(XkwTextbookDto::getGradeId, gradeId);
        }
        lqw.eq(XkwTextbookDto::getVersionId, versionId);
        List<XkwTextbookDto> xkwTextbookDtos = xkwTextbookMapper.selectList(lqw);
        return AjaxResult.success(xkwTextbookDtos);
    }

    @Override
    public AjaxResult textbookCatalog(String oid) {
        List<HomeworkBookCatalogDto> homeworkBookCatalogDtos = homeworkBookCatalogService.list(new LambdaUpdateWrapper<HomeworkBookCatalogDto>()
                .eq(HomeworkBookCatalogDto::getHomeworkBookOid, oid)
                .eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

        if (CollectionUtil.isNotEmpty(homeworkBookCatalogDtos)) {
            return AjaxResult.fail("目录已经存在，无法导入");
        }
        HomeworkBookDto one = getOne(new LambdaUpdateWrapper<HomeworkBookDto>()
                .eq(HomeworkBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(HomeworkBookDto::getOid, oid));
        if (one == null) {
            return AjaxResult.fail("保存失败");
        }
        List<XkwTextbookCatalogDto> xkwTextbookCatalogDtos = xkwTextbookCatalogMapper.selectList(new LambdaUpdateWrapper<XkwTextbookCatalogDto>()
                .eq(XkwTextbookCatalogDto::getParentId, 0L)
                .eq(XkwTextbookCatalogDto::getTextbookId, one.getTextBookId()));
        if (CollectionUtil.isNotEmpty(xkwTextbookCatalogDtos)) {
            List<HomeworkBookCatalogDto> arr = new ArrayList<>();
            for (XkwTextbookCatalogDto dto : xkwTextbookCatalogDtos) {
                HomeworkBookCatalogDto catalogDto = new HomeworkBookCatalogDto();
                catalogDto.setParentOid("0");
                catalogDto.setOid(IdUtil.simpleUUID());
                catalogDto.setName(dto.getName());
                catalogDto.setHomeworkBookOid(one.getOid());
                homeworkBookCatalogService.save(catalogDto);
                catalogDto.setOrderNum(catalogDto.getId());
                arr.add(catalogDto);
                saveCatalog(one, catalogDto.getOid(), dto.getId());
            }

            if (CollectionUtil.isNotEmpty(arr)) {
                homeworkBookCatalogService.updateBatchById(arr);
            }
        }
        return AjaxResult.success("保存成功");
    }

    @Override
    public AjaxResult saveTextbookCatalog(String oid, String textBookId) {
        List<HomeworkBookCatalogDto> homeworkBookCatalogDtos = homeworkBookCatalogService.list(new LambdaUpdateWrapper<HomeworkBookCatalogDto>()
                .eq(HomeworkBookCatalogDto::getHomeworkBookOid, oid)
                .eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

        if (CollectionUtil.isNotEmpty(homeworkBookCatalogDtos)) {
            return AjaxResult.fail("目录已经存在，无法导入");
        }
        HomeworkBookDto one = getOne(new LambdaUpdateWrapper<HomeworkBookDto>()
                .eq(HomeworkBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(HomeworkBookDto::getOid, oid));
        if (one == null) {
            return AjaxResult.fail("保存失败");
        }
        one.setTextBookId(Long.valueOf(textBookId));
        updateById(one);
        List<XkwTextbookCatalogDto> xkwTextbookCatalogDtos = xkwTextbookCatalogMapper.selectList(new LambdaUpdateWrapper<XkwTextbookCatalogDto>()
                .eq(XkwTextbookCatalogDto::getParentId, 0L)
                .eq(XkwTextbookCatalogDto::getTextbookId, one.getTextBookId()));
        if (CollectionUtil.isNotEmpty(xkwTextbookCatalogDtos)) {
            List<HomeworkBookCatalogDto> arr = new ArrayList<>();
            for (XkwTextbookCatalogDto dto : xkwTextbookCatalogDtos) {
                HomeworkBookCatalogDto catalogDto = new HomeworkBookCatalogDto();
                catalogDto.setParentOid("0");
                catalogDto.setOid(IdUtil.simpleUUID());
                catalogDto.setName(dto.getName());
                catalogDto.setHomeworkBookOid(one.getOid());
                homeworkBookCatalogService.save(catalogDto);
                catalogDto.setOrderNum(catalogDto.getId());
                arr.add(catalogDto);
                saveCatalog(one, catalogDto.getOid(), dto.getId());
            }

            if (CollectionUtil.isNotEmpty(arr)) {
                homeworkBookCatalogService.updateBatchById(arr);
            }
        }
        return AjaxResult.success("保存成功");
    }

    @Override
    public AjaxResult checkTextbookCatalog(String oid) {
        HomeworkBookDto one = getOne(new LambdaUpdateWrapper<HomeworkBookDto>()
                .eq(HomeworkBookDto::getOid, oid));
        List<XkwTextbookCatalogDto> xkwTextbookCatalogDtos = xkwTextbookCatalogMapper.selectList(new LambdaUpdateWrapper<XkwTextbookCatalogDto>()
                .eq(XkwTextbookCatalogDto::getTextbookId, one.getTextBookId()));

        return AjaxResult.success(CollectionUtil.isNotEmpty(xkwTextbookCatalogDtos));
    }

    @Override
    @Transactional
    public AjaxResult<HomeworkBookVo> copyHomeworkBook(String homeworkBookOid) {
        LambdaQueryWrapper<HomeworkBookDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkBookDto::getOid, homeworkBookOid);
        HomeworkBookDto one = getOne(lqw);
        List<HomeworkBookCatalogDto> list = homeworkBookCatalogService.list(new LambdaUpdateWrapper<HomeworkBookCatalogDto>()
                .eq(HomeworkBookCatalogDto::getHomeworkBookOid, homeworkBookOid)
                .eq(HomeworkBookCatalogDto::getParentOid, "0")
                .eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .orderByAsc(HomeworkBookCatalogDto::getOrderNum));
        if (CollectionUtil.isNotEmpty(list)) {
            List<HomeworkBookCatalogDto> arr = new ArrayList<>();
            for (HomeworkBookCatalogDto dto : list) {
                HomeworkBookCatalogDto catalogDto = new HomeworkBookCatalogDto();
                BeanUtils.copyProperties(dto, catalogDto);
                catalogDto.setOid(IdUtil.simpleUUID());
                catalogDto.setId(null);
                catalogDto.setCreateBy(null);
                catalogDto.setUpdateBy(null);
                catalogDto.setCreateTime(null);
                catalogDto.setUpdateTime(null);
                homeworkBookCatalogService.save(catalogDto);
                copyHomeWork(dto.getOid());
                catalogDto.setOrderNum(catalogDto.getId());
                arr.add(catalogDto);
                copyHomeWorkCatalog(one, catalogDto.getOid());
            }

            if (CollectionUtil.isNotEmpty(arr)) {
                homeworkBookCatalogService.updateBatchById(arr);
            }
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<HomeworkBookVo> practiceBookToHomeworkBook(String practiceBookOid, String userCode, String orgCode) {
        // 校验
        if (StringUtils.isEmpty(practiceBookOid)) {
            throw new WarningException("教辅OID不能为空");
        }
        if (StringUtils.isEmpty(userCode)) {
            throw new WarningException("用户Code不能为空");
        }

        // 查询教辅信息
        PracticeBookVo practiceBookVo = Optional.ofNullable(practiceBookService.getDetail(practiceBookOid)).orElseThrow(() -> new WarningException("未查到教辅信息"));

        // 查询目录信息
        List<PracticeBookCatalogVo> practiceBookCatalogVoList = Optional.ofNullable(practiceBookCatalogService.queryByPracticeBookOid(practiceBookOid)).orElseThrow(() -> new WarningException("未查到教辅目录信息"));

        // 查询题目信息
        if (practiceBookQuestionService.queryCountByPracticeBookOid(practiceBookOid) < 1) {
            throw new WarningException("未查到教辅题目信息");
        }

        // 组装作业本信息
        HomeworkBookDto homeworkBookDto = assembleHomeworkBookBo(practiceBookVo, userCode, orgCode);
        // 组装作业本目录信息map，key为教辅目录OID
        Map<String, HomeworkBookCatalogDto> homeworkBookCatalogMap = assembleHomeworkBookCatalog(practiceBookCatalogVoList, homeworkBookDto);
        // 组装作业信息
        Map<HomeworkDto, List<HomeworkQuestionDto>> homeworkMap = queryAssembleHomeworkAndQuestions(practiceBookOid, homeworkBookCatalogMap, homeworkBookDto);

        // 过滤作业目录信息，仅保留没有作业的目录信息，组装作业&目录信息关联数据
        List<HomeworkBookCatalogInfoDto> homeworkBookCatalogInfoDtoList = fiterHomeworkCatalogDtoList(homeworkBookCatalogMap, homeworkMap);

        // 补充作业数量
        homeworkBookDto.setNum(homeworkMap.keySet().size());
        // 保存作业本数据到DB
        save(homeworkBookDto);

        // 保存作业目录数据到DB
        homeworkBookCatalogService.saveBatch(new ArrayList<>(homeworkBookCatalogMap.values()));

        // 保存作业数据
        homeworkService.saveBatch(new ArrayList<>(homeworkMap.keySet()));

        // 保存作业题目数据
        homeworkQuestionService.saveBatch(homeworkMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));

        // 保存目录关联作业信息
        homeworkBookCatalogInfoService.saveBatch(homeworkBookCatalogInfoDtoList);

        return AjaxResult.success();
    }

    /**
     * 教辅转作业本
     *
     * @param practiceBookVo 教辅信息
     * @param userCode       用户 Code
     * @param orgCode        学校 Code
     * @return 作业本信息
     */
    private HomeworkBookDto assembleHomeworkBookBo(PracticeBookVo practiceBookVo, String userCode, String orgCode) {
        HomeworkBookDto homeworkBookDto = new HomeworkBookDto();
        BeanUtils.copyProperties(practiceBookVo, homeworkBookDto);
        homeworkBookDto.setId(null);
        homeworkBookDto.setCreateBy(userCode);
        homeworkBookDto.setCreateTime(null);
        homeworkBookDto.setUpdateBy(null);
        homeworkBookDto.setUpdateTime(null);
        homeworkBookDto.setIsDelete(StatusEnum.NOTDELETE.getCode());

        homeworkBookDto.setOid(IdUtil.simpleUUID());
        homeworkBookDto.setName(practiceBookVo.getName() + "作业本");
        homeworkBookDto.setOrgCode(orgCode);
        homeworkBookDto.setYear(DateUtil.getDateStr(DateUtil.DATE_FORMAT_YYYY));
//        homeworkBookDto.setTextBookId();
//        homeworkBookDto.setExerciseType();
        homeworkBookDto.setCoverUrl(practiceBookVo.getCoverImage());
        homeworkBookDto.setNum(ConstantsInteger.NUM_0);
        homeworkBookDto.setStatus(StatusEnum.DISABLE.getCode());
        homeworkBookDto.setHasNoConfirm(StatusEnum.YES.getCode());
        homeworkBookDto.setIsCompleted(StatusEnum.NO.getCode());
        homeworkBookDto.setSourceType(AISzzyConstants.HomeWorkSourceType.PRACTICEBOOK.getCode());
        homeworkBookDto.setSourceOid(practiceBookVo.getOid());

        return homeworkBookDto;
    }

    /**
     * 教辅目录转作业本目录
     *
     * @param practiceBookCatalogVoList 教辅目录列表
     * @param homeworkBookDto           作业本信息
     * @return 作业本目录信息，key为教辅目录OID
     */
    private Map<String, HomeworkBookCatalogDto> assembleHomeworkBookCatalog(List<PracticeBookCatalogVo> practiceBookCatalogVoList, HomeworkBookDto homeworkBookDto) {
        // 教辅目录id->作业本目录id映射关系
        Map<String, String> idMapping = new HashMap<>();
        idMapping.put("0", "0");
        practiceBookCatalogVoList.forEach((practiceBookCatalogVo) -> {
            idMapping.put(practiceBookCatalogVo.getOid(), IdUtil.simpleUUID());
        });

        // 教辅目录id到作业本目录id映射关系
        Map<String, HomeworkBookCatalogDto> homeworkBookCatalogMap = new HashMap<>();
        practiceBookCatalogVoList.forEach((practiceBookCatalogVo) -> {
            HomeworkBookCatalogDto homeworkBookCatalogDto = new HomeworkBookCatalogDto();
            homeworkBookCatalogDto.setOid(idMapping.get(practiceBookCatalogVo.getOid()));
            homeworkBookCatalogDto.setHomeworkBookOid(homeworkBookDto.getOid());
            // 获取映射的父级目录id
            homeworkBookCatalogDto.setParentOid(idMapping.get(practiceBookCatalogVo.getParentOid()));
            homeworkBookCatalogDto.setName(practiceBookCatalogVo.getName());
            homeworkBookCatalogDto.setOrderNum(Long.valueOf(practiceBookCatalogVo.getOrderNum()));
            homeworkBookCatalogDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
            homeworkBookCatalogMap.put(practiceBookCatalogVo.getOid(), homeworkBookCatalogDto);
        });

        return homeworkBookCatalogMap;
    }

    /**
     * 教辅题目转作业&题目
     *
     * @param practiceBookOid        教辅id
     * @param homeworkBookCatalogMap <教辅目录id, List<作业题目>>
     * @return map
     */
    private Map<HomeworkDto, List<HomeworkQuestionDto>> queryAssembleHomeworkAndQuestions(String practiceBookOid, Map<String, HomeworkBookCatalogDto> homeworkBookCatalogMap, HomeworkBookDto homeworkBookDto) {
        // 作业信息->作业题目信息
        Map<HomeworkDto, List<HomeworkQuestionDto>> homeworkMap = new HashMap<>();

        // 根据教辅教辅id查询题目列表，查询出来的题目所在目录为叶子节点目录
        List<QuestionWithPracticeBo> questionWithPracticeBoList = questionMapper.getQuestionWithPracticeListByPracticeBookOid(practiceBookOid);
        // 过滤数据，并根据教辅目录OID分组，输出map <key 教辅目录id，value 题目列表>
        Map<String, List<QuestionWithPracticeBo>> collect = Optional.ofNullable(questionWithPracticeBoList).orElseThrow(() -> new WarningException("没有找到对应的题目")).stream().filter(p -> p.getPracticeBookCatalogOid() != null).collect(Collectors.groupingBy(QuestionWithPracticeBo::getPracticeBookCatalogOid));
        if (collect == null || collect.size() == 0) {
            throw new WarningException("没有找到对应的题目");
        }

        // 组装Map<作业，题目>
        collect.forEach((key, value) -> {
            HomeworkBookCatalogDto homeworkBookCatalogDto = homeworkBookCatalogMap.get(key);
            if (homeworkBookCatalogDto == null) {
                throw new WarningException("没有找到对应的目录");
            }

            // key作业信息
            HomeworkDto homeworkDto = new HomeworkDto();
            // 赋值
            homeworkDto.setOid(IdUtil.simpleUUID());
            homeworkDto.setOrgCode(homeworkBookDto.getOrgCode());
            homeworkDto.setHomeworkName(homeworkBookCatalogDto.getName());
            homeworkDto.setSubject(homeworkBookDto.getSubject());
            homeworkDto.setGrade(homeworkBookDto.getGrade());
            homeworkDto.setTerm(homeworkBookDto.getTerm());
            homeworkDto.setYear(homeworkBookDto.getYear());
            homeworkDto.setIsUse(StatusEnum.YES.getCode());
            homeworkDto.setHomeworkBookOid(homeworkBookDto.getOid());
            homeworkDto.setQuestionNum(value.size());
            homeworkDto.setStatus(StatusEnum.NO.getCode());
            homeworkDto.setGenerateRuleType(AISzzyConstants.HomeWorkGenerateRuleType.PRACTICEBOOK.getCode());
            homeworkDto.setSourceType(AISzzyConstants.HomeWorkSourceType.PRACTICEBOOK.getCode());
            homeworkDto.setSourceOid(key);

            // value 作业题目list
            List<HomeworkQuestionDto> homeworkQuestionDtoList = new ArrayList<>(value.size());
            for (int i = 0; i < value.size(); i++) {
                QuestionWithPracticeBo questionWithPracticeBo = value.get(i);
                HomeworkQuestionDto homeworkQuestionDto = BeanUtil.toBean(questionWithPracticeBo, HomeworkQuestionDto.class);
                homeworkQuestionDto.setId(null);
                homeworkQuestionDto.setCreateTime(null);
                homeworkQuestionDto.setCreateBy(null);
                homeworkQuestionDto.setUpdateTime(null);
                homeworkQuestionDto.setUpdateBy(null);
                homeworkQuestionDto.setOid(IdUtil.simpleUUID());
                homeworkQuestionDto.setQuestionOid(questionWithPracticeBo.getOid());
                homeworkQuestionDto.setHomeworkOid(homeworkDto.getOid());
                homeworkQuestionDto.setQuesOrderNum(i + 1L);
                homeworkQuestionDto.setSubject(homeworkDto.getSubject());
                homeworkQuestionDto.setGrade(homeworkDto.getGrade());
                homeworkQuestionDto.setYear(homeworkDto.getYear());

                homeworkQuestionDtoList.add(homeworkQuestionDto);
            }

            homeworkMap.put(homeworkDto, homeworkQuestionDtoList);
        });

        return homeworkMap;
    }


    /**
     * 过滤叶子节点作业目录
     *
     * @param homeworkBookCatalogMap 作业目录
     * @param homeworkMap            作业
     * @return 作业&目录关联信息list
     */
    private List<HomeworkBookCatalogInfoDto> fiterHomeworkCatalogDtoList(Map<String, HomeworkBookCatalogDto> homeworkBookCatalogMap, Map<HomeworkDto, List<HomeworkQuestionDto>> homeworkMap) {
        // 作业&目录关联信息list
        List<HomeworkBookCatalogInfoDto> homeworkBookCatalogInfoDtoList = new ArrayList<>();
        //  过滤存在作业目录
        homeworkMap.keySet().forEach(key -> {
            // 移除叶子节点目录
            HomeworkBookCatalogDto lastLevelDto = homeworkBookCatalogMap.remove(key.getSourceOid());
            if (lastLevelDto == null) {
                log.warn("作业关联的目录OID:[{}]未查到目录信息", key.getSourceOid());
                return;
            }
            HomeworkBookCatalogInfoDto homeworkBookCatalogInfoDto = new HomeworkBookCatalogInfoDto();
            homeworkBookCatalogInfoDto.setOid(IdUtil.simpleUUID());
            // 设置作业所在目录id，此处设置为叶子节点目录的父级目录id
            homeworkBookCatalogInfoDto.setHomeworkBookCatalogOid(lastLevelDto.getParentOid());
            homeworkBookCatalogInfoDto.setHomeworkOid(key.getOid());
            homeworkBookCatalogInfoDto.setOrderNum(Math.toIntExact(lastLevelDto.getOrderNum()));
            homeworkBookCatalogInfoDtoList.add(homeworkBookCatalogInfoDto);
        });

        // 作业目录关联信息list
        return homeworkBookCatalogInfoDtoList;
    }

    public void saveCatalog(HomeworkBookDto one, String catalogParentOid, Long parentId) {
        List<XkwTextbookCatalogDto> xkwTextbookCatalogDtos = xkwTextbookCatalogMapper.selectList(new LambdaUpdateWrapper<XkwTextbookCatalogDto>()
                .eq(XkwTextbookCatalogDto::getParentId, parentId)
                .eq(XkwTextbookCatalogDto::getTextbookId, one.getTextBookId()));
        if (CollectionUtil.isNotEmpty(xkwTextbookCatalogDtos)) {
            List<HomeworkBookCatalogDto> arr = new ArrayList<>();
            for (XkwTextbookCatalogDto dto : xkwTextbookCatalogDtos) {
                HomeworkBookCatalogDto catalogDto = new HomeworkBookCatalogDto();
                catalogDto.setParentOid(catalogParentOid);
                catalogDto.setOid(IdUtil.simpleUUID());
                catalogDto.setName(dto.getName());
                catalogDto.setHomeworkBookOid(one.getOid());
                homeworkBookCatalogService.save(catalogDto);
                catalogDto.setOrderNum(catalogDto.getId());
                arr.add(catalogDto);
                saveCatalog(one, catalogDto.getOid(), dto.getId());
            }
            if (CollectionUtil.isNotEmpty(arr)) {
                homeworkBookCatalogService.updateBatchById(arr);
            }
        }
    }

    public void copyHomeWorkCatalog(HomeworkBookDto one, String catalogParentOid) {
        List<HomeworkBookCatalogDto> list = homeworkBookCatalogService.list(new LambdaUpdateWrapper<HomeworkBookCatalogDto>()
                .eq(HomeworkBookCatalogDto::getHomeworkBookOid, one.getOid())
                .eq(HomeworkBookCatalogDto::getParentOid, catalogParentOid)
                .eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .orderByAsc(HomeworkBookCatalogDto::getOrderNum));
        if (CollectionUtil.isNotEmpty(list)) {
            List<HomeworkBookCatalogDto> arr = new ArrayList<>();
            for (HomeworkBookCatalogDto dto : list) {
                HomeworkBookCatalogDto catalogDto = new HomeworkBookCatalogDto();
                BeanUtils.copyProperties(dto, catalogDto);
                catalogDto.setOid(IdUtil.simpleUUID());
                catalogDto.setId(null);
                catalogDto.setCreateBy(null);
                catalogDto.setUpdateBy(null);
                catalogDto.setCreateTime(null);
                catalogDto.setUpdateTime(null);
                homeworkBookCatalogService.save(catalogDto);
                copyHomeWork(dto.getOid());
                catalogDto.setOrderNum(catalogDto.getId());
                arr.add(catalogDto);
                copyHomeWorkCatalog(one, catalogDto.getOid());
            }

            if (CollectionUtil.isNotEmpty(arr)) {
                homeworkBookCatalogService.updateBatchById(arr);
            }
        }
    }

    public void copyHomeWork(String catalogOid) {
        List<HomeworkBookCatalogInfoDto> list = homeworkBookCatalogInfoService.list(new LambdaUpdateWrapper<HomeworkBookCatalogInfoDto>()
                .eq(HomeworkBookCatalogInfoDto::getHomeworkBookCatalogOid, catalogOid)
                .eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .orderByAsc(HomeworkBookCatalogInfoDto::getOrderNum));
        if (CollectionUtil.isNotEmpty(list)) {
            List<HomeworkBookCatalogInfoDto> arr = new ArrayList<>();
            for (HomeworkBookCatalogInfoDto dto : list) {
                AjaxResult<HomeworkVo> homeworkVoAjaxResult = homeworkService.copyHomework(dto.getHomeworkOid());
                if (homeworkVoAjaxResult.getSuccess()) {
                    HomeworkVo homeworkVo = homeworkVoAjaxResult.getData();
                    HomeworkBookCatalogInfoDto homeworkBookCatalogInfoDto = new HomeworkBookCatalogInfoDto();
                    homeworkBookCatalogInfoDto.setHomeworkBookCatalogOid(catalogOid);
                    homeworkBookCatalogInfoDto.setHomeworkOid(homeworkVo.getHomeworkBookOid());
                    arr.add(homeworkBookCatalogInfoDto);
                }
            }
            if (CollectionUtil.isNotEmpty(arr)) {
                homeworkBookCatalogInfoService.saveBatch(arr);
                arr.forEach(obj -> {
                    obj.setOrderNum(obj.getId().intValue());
                });
                homeworkBookCatalogInfoService.updateBatchById(arr);
            }
        }
    }


}