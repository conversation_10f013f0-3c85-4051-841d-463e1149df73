package com.light.aiszzy.practiceBook.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.practiceBook.entity.dto.PracticeBookReviewDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookReviewVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookReviewService;
import com.light.aiszzy.practiceBook.mapper.PracticeBookReviewMapper;
import com.light.core.entity.AjaxResult;
/**
 * 教辅信息审核接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Service
public class PracticeBookReviewServiceImpl extends ServiceImpl<PracticeBookReviewMapper, PracticeBookReviewDto> implements IPracticeBookReviewService {

	@Resource
	private PracticeBookReviewMapper practiceBookReviewMapper;
	
    @Override
	public List<PracticeBookReviewVo> getPracticeBookReviewListByCondition(PracticeBookReviewConditionBo condition) {
        return practiceBookReviewMapper.getPracticeBookReviewListByCondition(condition);
	}

	@Override
	public AjaxResult addPracticeBookReview(PracticeBookReviewBo practiceBookReviewBo) {
		PracticeBookReviewDto practiceBookReview = new PracticeBookReviewDto();
		BeanUtils.copyProperties(practiceBookReviewBo, practiceBookReview);
		practiceBookReview.setIsDelete(StatusEnum.NOTDELETE.getCode());
		practiceBookReview.setOid(IdUtil.simpleUUID());
		if(save(practiceBookReview)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updatePracticeBookReview(PracticeBookReviewBo practiceBookReviewBo) {
		LambdaQueryWrapper<PracticeBookReviewDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(PracticeBookReviewDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(PracticeBookReviewDto::getOid, practiceBookReviewBo.getOid());
		PracticeBookReviewDto practiceBookReview = getOne(lqw);
		Long id = practiceBookReview.getId();
		if(practiceBookReview == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(practiceBookReviewBo, practiceBookReview);
		practiceBookReview.setId(id);
		if(updateById(practiceBookReview)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public PracticeBookReviewVo getDetail(String oid) {
		LambdaQueryWrapper<PracticeBookReviewDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(PracticeBookReviewDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(PracticeBookReviewDto::getOid, oid);
		PracticeBookReviewDto practiceBookReview = getOne(lqw);
	    PracticeBookReviewVo practiceBookReviewVo = new PracticeBookReviewVo();
		if(practiceBookReview != null){
			BeanUtils.copyProperties(practiceBookReview, practiceBookReviewVo);
		}
		return practiceBookReviewVo;
	}

}