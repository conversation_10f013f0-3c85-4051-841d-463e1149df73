package com.light.aiszzy.resourcesQuestion.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.resourcesQuestion.entity.dto.ResourcesQuestionDto;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionConditionBo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionVo;

/**
 * 资源库题目表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface ResourcesQuestionMapper extends BaseMapper<ResourcesQuestionDto> {

	List<ResourcesQuestionVo> getResourcesQuestionListByCondition(ResourcesQuestionConditionBo condition);

}
