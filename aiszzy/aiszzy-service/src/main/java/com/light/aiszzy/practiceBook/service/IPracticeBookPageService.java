package com.light.aiszzy.practiceBook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookPageDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 教辅目录每页图片记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IPracticeBookPageService extends IService<PracticeBookPageDto> {

    List<PracticeBookPageVo> getPracticeBookPageListByCondition(PracticeBookPageConditionBo condition);

	AjaxResult addPracticeBookPage(PracticeBookPageBo practiceBookPageBo);

	AjaxResult updatePracticeBookPage(PracticeBookPageBo practiceBookPageBo);

    PracticeBookPageVo getDetail(String oid);

	/**
	 * 增加页面题目数量
	 * @param pageOid 页面OID
	 * @return
	 */
	AjaxResult incrementQuestionNum(String pageOid);

	/**
	 * 增加页面完成题目数量
	 * @param pageOid 页面OID
	 * @return
	 */
	AjaxResult incrementFinishQuestionNum(String pageOid);

	/**
	 * 减少页面完成题目数量
	 * @param pageOid 页面OID
	 * @return
	 */
	AjaxResult decrementFinishQuestionNum(String pageOid);

	/**
	 * 减少页面题目数量
	 * @param pageOid 页面OID
	 * @return
	 */
	AjaxResult decrementQuestionNum(String pageOid);

	/**
	 *  保存教辅每页数据
	 * @param practiceBookOid the practiceBook oid 教辅 OID
	 * @param list the practice book page 分页数据
	 * @return {@link AjaxResult }
	 */
	AjaxResult saveBatchByPracticeBookOid(String practiceBookOid, List<PracticeBookPageBo> list);

	/**
	 * 根据教辅 OID 删除数据
	 * @param practiceBookOid the practice book oid  教辅 OID
	 * @return boolean
	 */
	boolean deleteByPracticeBookOid(String practiceBookOid);

	/**
	 * 根据 OID 查询数据
	 * @param oid 页码 OID
	 * @return {@link PracticeBookPageVo }
	 */
	PracticeBookPageVo queryByOid(String oid);

	/**
	 *  更新页码对应题目信息
	 * @param oid the oid
	 * @param questionJson the question json
	 * @return boolean
	 */
	boolean updateQuestionJson(String oid, String questionJson);

	/**
	 * 同时更新页码题目JSON信息和减少题目数量
	 * @param oid 页面OID
	 * @param questionJson 题目JSON信息
	 * @return boolean
	 */
	boolean updateQuestionJsonAndDecrementNum(String oid, String questionJson);

	/**
	 * 同时更新页码题目JSON信息和增加题目数量
	 * @param oid 页面OID
	 * @param questionJson 题目JSON信息
	 * @return boolean
	 */
	boolean updateQuestionJsonAndAddNum(String oid, String questionJson);
}

