package com.light.aiszzy.xkw.xkwCourses.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.xkw.xkwCourses.entity.dto.XkwCoursesDto;
import com.light.aiszzy.xkw.xkwCourses.entity.vo.XkwCoursesVo;
import com.light.aiszzy.xkw.xkwCourses.mapper.XkwCoursesMapper;
import com.light.aiszzy.xkw.xkwCourses.service.IXkwCoursesService;
import com.light.contants.AISzzyConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 课程接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Service
public class XkwCoursesServiceImpl extends ServiceImpl<XkwCoursesMapper, XkwCoursesDto> implements IXkwCoursesService {

	@Resource
	private XkwCoursesMapper xkwCoursesMapper;


	@Override
	public XkwCoursesVo queryByGradeAndSubject(Integer grade, String subject) {
		String xkwSubject = AISzzyConstants.getXkwSubject(subject);
		Integer stageId = AISzzyConstants.getXkwStageByGrade(grade);
		return this.queryByStageAndSubject(stageId, xkwSubject);
	}

	@Override
	public XkwCoursesVo queryByStageAndSubject(Integer stage, String subject) {
		QueryWrapper<XkwCoursesDto> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(XkwCoursesDto::getSubjectId, subject);
		wrapper.lambda().eq(XkwCoursesDto::getStageId, stage);
		XkwCoursesDto xkwCoursesDto = this.baseMapper.selectOne(wrapper);
		if(xkwCoursesDto == null){
			return null;
		}
		return BeanUtil.toBean(xkwCoursesDto, XkwCoursesVo.class);
	}
}