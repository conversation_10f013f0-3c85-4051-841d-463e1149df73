package com.light.aiszzy.xkw.xkwTextbookCatalog.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogBo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogConditionBo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.dto.XkwTextbookCatalogDto;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.vo.XkwTextbookCatalogVo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.mapper.XkwTextbookCatalogMapper;
import com.light.aiszzy.xkw.xkwTextbookCatalog.service.IXkwTextbookCatalogService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 教材目录接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Service
public class XkwTextbookCatalogServiceImpl extends ServiceImpl<XkwTextbookCatalogMapper, XkwTextbookCatalogDto> implements IXkwTextbookCatalogService {

	@Resource
	private XkwTextbookCatalogMapper xkwTextbookCatalogMapper;
	
    @Override
	public List<XkwTextbookCatalogVo> getXkwTextbookCatalogListByCondition(XkwTextbookCatalogConditionBo condition) {
        return xkwTextbookCatalogMapper.getXkwTextbookCatalogListByCondition(condition);
	}



}