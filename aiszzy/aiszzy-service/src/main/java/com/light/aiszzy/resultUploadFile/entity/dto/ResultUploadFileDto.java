package com.light.aiszzy.resultUploadFile.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 扫描上传图片
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("result_upload_file")
public class ResultUploadFileDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 通过设备信息获取的学校CODE
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 存放地址1
	 */
	@TableField("path_one")
	private String pathOne;

	/**
	 * 存放地址2
	 */
	@TableField("path_two")
	private String pathTwo;

	/**
	 * 设备号，硬件序列号
	 */
	@TableField("hardware_code")
	private String hardwareCode;

	/**
	 * 是否识别处理 0未处理，1已经处理，2处理失败
	 */
	@TableField("is_deal")
	private Integer isDeal;
	/**
	 * 处理情况描述
	 */
	@TableField("deal_detail")
	private String dealDetail;
	/**
	 * 处理结果
	 */
	@TableField("deal_result")
	private String dealResult;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
