package com.light.aiszzy.xkw.xkwTextbookVersions.service.impl;

import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsConditionBo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.dto.XkwTextbookVersionsDto;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo;
import com.light.aiszzy.xkw.xkwTextbookVersions.mapper.XkwTextbookVersionsMapper;
import com.light.aiszzy.xkw.xkwTextbookVersions.service.IXkwTextbookVersionsService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

/**
 * 教材版本接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
@Service
public class XkwTextbookVersionsServiceImpl extends ServiceImpl<XkwTextbookVersionsMapper, XkwTextbookVersionsDto> implements IXkwTextbookVersionsService {

	@Resource
	private XkwTextbookVersionsMapper xkwTextbookVersionsMapper;

    @Override
	public List<XkwTextbookVersionsVo> getXkwTextbookVersionsListByCondition(XkwTextbookVersionsConditionBo condition) {
        return xkwTextbookVersionsMapper.getXkwTextbookVersionsListByCondition(condition);
	}

}