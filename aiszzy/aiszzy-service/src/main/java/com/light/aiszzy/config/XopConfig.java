package com.light.aiszzy.config;

import com.xkw.xop.client.XopHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class XopConfig {

    @Value("${xop.config.appId:108301739943716600}")
    private String appId;

    @Value("${xop.config.appSecret:BLF8XfjBwQqjLorx2ATyfTXs9WBB1A5y}")
    private String appSecret;

    @Bean
    XopHttpClient xopHttpClient() {
        XopHttpClient xopClient = new XopHttpClient.Builder()
                .appId(appId).secret(appSecret)
                .timeout(10).maxConnectionPerRoute(10)
                .build();
        return xopClient;
    }

    @Bean(name = "taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(10);
        // 最大线程数
        executor.setMaxPoolSize(20);
        // 队列容量
        executor.setQueueCapacity(100);
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix("task-executor-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化
        executor.initialize();
        return executor;
    }
}
