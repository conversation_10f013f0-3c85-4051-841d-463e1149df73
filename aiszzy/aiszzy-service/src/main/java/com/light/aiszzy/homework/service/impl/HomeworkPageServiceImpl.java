package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkPageDto;
import com.light.aiszzy.homework.entity.bo.HomeworkPageConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkPageBo;
import com.light.aiszzy.homework.entity.vo.HomeworkPageVo;
import com.light.aiszzy.homework.service.IHomeworkPageService;
import com.light.aiszzy.homework.mapper.HomeworkPageMapper;
import com.light.core.entity.AjaxResult;
/**
 * 作业每页题目坐标信息接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@Service
public class HomeworkPageServiceImpl extends ServiceImpl<HomeworkPageMapper, HomeworkPageDto> implements IHomeworkPageService {

	@Resource
	private HomeworkPageMapper homeworkPageMapper;
	
    @Override
	public List<HomeworkPageVo> getHomeworkPageListByCondition(HomeworkPageConditionBo condition) {
        return homeworkPageMapper.getHomeworkPageListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkPage(HomeworkPageBo homeworkPageBo) {
		HomeworkPageDto homeworkPage = new HomeworkPageDto();
		BeanUtils.copyProperties(homeworkPageBo, homeworkPage);
		homeworkPage.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkPage.setOid(IdUtil.simpleUUID());
		if(save(homeworkPage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkPage(HomeworkPageBo homeworkPageBo) {
		LambdaQueryWrapper<HomeworkPageDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkPageDto::getOid, homeworkPageBo.getOid());
		HomeworkPageDto homeworkPage = getOne(lqw);
		Long id = homeworkPage.getId();
		if(homeworkPage == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkPageBo, homeworkPage);
		homeworkPage.setId(id);
		if(updateById(homeworkPage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkPageVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkPageDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkPageDto::getOid, oid);
		HomeworkPageDto homeworkPage = getOne(lqw);
	    HomeworkPageVo homeworkPageVo = new HomeworkPageVo();
		if(homeworkPage != null){
			BeanUtils.copyProperties(homeworkPage, homeworkPageVo);
		}
		return homeworkPageVo;
	}

}