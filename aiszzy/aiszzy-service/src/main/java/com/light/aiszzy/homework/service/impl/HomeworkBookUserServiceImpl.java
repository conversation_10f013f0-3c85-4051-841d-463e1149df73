package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import com.light.redis.component.DictTranslationComponent;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserConditionBo;
import com.light.aiszzy.homework.entity.dto.HomeworkBookUserDto;
import com.light.aiszzy.homework.entity.vo.HomeworkBookUserVo;
import com.light.aiszzy.homework.mapper.HomeworkBookUserMapper;
import com.light.aiszzy.homework.service.IHomeworkBookUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 老师使用作业本接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-18 14:20:30
 */
@Service
public class HomeworkBookUserServiceImpl extends ServiceImpl<HomeworkBookUserMapper, HomeworkBookUserDto> implements IHomeworkBookUserService {

    @Resource
    private HomeworkBookUserMapper homeworkBookUserMapper;

    @Autowired
    private DictTranslationComponent dictTranslationComponent;

    @Override
    public List<HomeworkBookVo> getHomeworkBookUserListByCondition(HomeworkBookUserConditionBo condition) {
        return dictTranslationComponent.translationList(homeworkBookUserMapper.getHomeworkBookUserListByCondition(condition));
    }

    @Override
    public AjaxResult addHomeworkBookUser(HomeworkBookUserBo homeworkBookUserBo) {
        HomeworkBookUserDto homeworkBookUser = new HomeworkBookUserDto();
        BeanUtils.copyProperties(homeworkBookUserBo, homeworkBookUser);
        homeworkBookUser.setIsDelete(StatusEnum.NOTDELETE.getCode());
        homeworkBookUser.setOid(IdUtil.simpleUUID());
        homeworkBookUser.setIsCurrent(2L);
        if (save(homeworkBookUser)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult addBatchHomeworkBookUser(HomeworkBookUserBo homeworkBookUserBo) {
        if (StringUtils.isEmpty(homeworkBookUserBo.getHomeworkBookOids())) {
            return AjaxResult.fail("参数错误");
        }
        List<HomeworkBookUserDto> arr = new ArrayList<>();
        for (String homeworkBookOid : homeworkBookUserBo.getHomeworkBookOids().split(",")) {
            HomeworkBookUserDto homeworkBookUser = new HomeworkBookUserDto();
            BeanUtils.copyProperties(homeworkBookUserBo, homeworkBookUser);
            homeworkBookUser.setIsDelete(StatusEnum.NOTDELETE.getCode());
            homeworkBookUser.setOid(IdUtil.simpleUUID());
            homeworkBookUser.setHomeworkBookOid(homeworkBookOid);
            homeworkBookUser.setIsCurrent(2L);
            arr.add(homeworkBookUser);
        }
        if (CollectionUtil.isNotEmpty(arr)) {
            if (saveBatch(arr)) {
                return AjaxResult.success("保存成功");
            } else {
                return AjaxResult.fail("保存失败");
            }
        }
        return AjaxResult.fail("保存失败");
    }

    @Override
    public AjaxResult updateHomeworkBookUser(HomeworkBookUserBo homeworkBookUserBo) {
        LambdaUpdateWrapper<HomeworkBookUserDto> uqw = new LambdaUpdateWrapper<>();
        uqw.eq(HomeworkBookUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        uqw.eq(HomeworkBookUserDto::getCreateBy, homeworkBookUserBo.getCreateBy());
        uqw.eq(HomeworkBookUserDto::getOrgCode, homeworkBookUserBo.getOrgCode());
        uqw.eq(HomeworkBookUserDto::getIsCurrent, 1L);
        uqw.set(HomeworkBookUserDto::getIsCurrent, 2L);

        update(null, uqw);
        LambdaUpdateWrapper<HomeworkBookUserDto> uqwCurrent = new LambdaUpdateWrapper<>();
        uqwCurrent.eq(HomeworkBookUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        uqwCurrent.eq(HomeworkBookUserDto::getCreateBy, homeworkBookUserBo.getCreateBy());
        uqwCurrent.eq(HomeworkBookUserDto::getHomeworkBookOid, homeworkBookUserBo.getHomeworkBookOid());
        uqwCurrent.eq(HomeworkBookUserDto::getOrgCode, homeworkBookUserBo.getOrgCode());
        uqwCurrent.set(HomeworkBookUserDto::getIsCurrent, 1L);
        update(null, uqwCurrent);
        return AjaxResult.success("保存成功");
    }

    @Override
    public Map<String, Object> getDetail(String oid) {
        LambdaQueryWrapper<HomeworkBookUserDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkBookUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkBookUserDto::getOid, oid);
        HomeworkBookUserDto homeworkBookUser = getOne(lqw);
        Map<String, Object> result = new HashMap<String, Object>(4);
        result.put("homeworkBookUserVo", homeworkBookUser == null ? new HomeworkBookUserVo() : homeworkBookUser);
        return result;
    }

    @Override
    public AjaxResult deleteUse(String homeworkBookOid, String userCode, String orgCode) {
        homeworkBookUserMapper.delete(new LambdaUpdateWrapper<HomeworkBookUserDto>()
                .eq(HomeworkBookUserDto::getCreateBy, userCode)
                .eq(HomeworkBookUserDto::getOrgCode, orgCode)
                .eq(HomeworkBookUserDto::getHomeworkBookOid, homeworkBookOid));
        return AjaxResult.success("删除成功");
    }

}