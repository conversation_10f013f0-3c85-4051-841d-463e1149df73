package com.light.aiszzy.device.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.device.entity.dto.DeviceBindRecordDto;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordConditionBo;
import com.light.aiszzy.device.entity.vo.DeviceBindRecordVo;

/**
 * 设备表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface DeviceBindRecordMapper extends BaseMapper<DeviceBindRecordDto> {

	List<DeviceBindRecordVo> getDeviceBindRecordListByCondition(DeviceBindRecordConditionBo condition);

	DeviceBindRecordVo getDetailByCondition(DeviceBindRecordConditionBo condition);
}
