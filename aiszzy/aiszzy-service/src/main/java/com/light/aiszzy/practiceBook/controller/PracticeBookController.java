package com.light.aiszzy.practiceBook.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookService;

import com.light.aiszzy.practiceBook.api.PracticeBookApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 教辅信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "教辅信息表接口")
public class PracticeBookController implements PracticeBookApi {

    @Autowired
    private IPracticeBookService practiceBookService;


    public AjaxResult<PageInfo<PracticeBookVo>> getPracticeBookPageListByCondition(@RequestBody PracticeBookConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize());
        List<PracticeBookVo> list = practiceBookService.getPracticeBookListByCondition(condition);

        PageInfo<PracticeBookVo> pageInfo = new PageInfo<>(list);
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<PracticeBookVo>> getPracticeBookListByCondition(@RequestBody PracticeBookConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(practiceBookService.getPracticeBookListByCondition(condition));
    }

    public AjaxResult addPracticeBook(@Validated @RequestBody PracticeBookBo practiceBookBo) {
        return practiceBookService.addPracticeBook(practiceBookBo);
    }

    public AjaxResult updatePracticeBook(@Validated @RequestBody PracticeBookBo practiceBookBo) {
        if (null == practiceBookBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return practiceBookService.updatePracticeBook(practiceBookBo);
    }

    public AjaxResult<PracticeBookVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(practiceBookService.getDetail(oid));
    }

    @Override
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
        return practiceBookService.deleteByOid(oid);
    }

    @Override
    public AjaxResult updateCatalogPathByOid(@RequestBody PracticeBookBo practiceBookBo) {
        return this.practiceBookService.updateCatalogPathByOid(practiceBookBo.getOid(), practiceBookBo.getCatalogFilePath());
    }

    @Override
    public AjaxResult<Void> updateFileInfoByOid(@RequestBody PracticeBookBo practiceBookBo) {
        String oid = practiceBookBo.getOid();
        Integer fileType = practiceBookBo.getFileType();
        String fileUrl = practiceBookBo.getFileUrl();
        boolean  b = this.practiceBookService.updateFileInfoByOid(oid, fileType, fileUrl);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult<Void> updateQuestionNumInfoByOid(@RequestBody PracticeBookBo practiceBookBo) {
        String oid = practiceBookBo.getOid();
        Long totalQuestionNum = practiceBookBo.getTotalQuestionNum();
        Long finishQuestionNum = practiceBookBo.getFinishQuestionNum();
        boolean  b = this.practiceBookService.updateQuestionNumInfoByOid(oid, totalQuestionNum, finishQuestionNum);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult updateIsHighShotsByOid(@RequestBody PracticeBookBo practiceBookBo) {
        Integer isHighShots = practiceBookBo.getIsHighShots();
        this.practiceBookService.updateIsHighShotsByOid(practiceBookBo.getOid(), isHighShots);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult commit(@RequestBody PracticeBookBo practiceBookBo) {
        String oid = practiceBookBo.getOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("教辅 OID不能为空");
        }
        return this.practiceBookService.commit(oid);
    }

    @Override
    public AjaxResult review(@RequestBody PracticeBookBo practiceBookBo) {
        String oid = practiceBookBo.getOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("教辅 OID不能为空");
        }
        Integer reviewStatus = practiceBookBo.getReviewStatus();
        if(reviewStatus == null) {
            return AjaxResult.fail("教辅状态不能为空");
        }
        return this.practiceBookService.review(oid, reviewStatus, practiceBookBo.getReviewComment());
    }

    @Override
    public AjaxResult changeStatus(@RequestBody PracticeBookBo practiceBookBo) {
        String oid = practiceBookBo.getOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("教辅 OID不能为空");
        }
        Integer status = practiceBookBo.getStatus();
        if(status == null) {
            return AjaxResult.fail("教辅状态不能为空");
        }
        return this.practiceBookService.updateStatusByOid(oid, status);
    }
}
