package com.light.aiszzy.userPaper.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.userPaper.entity.dto.UserPaperQuestionDto;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionConditionBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 资源库试卷表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface UserPaperQuestionMapper extends BaseMapper<UserPaperQuestionDto> {

	List<UserPaperQuestionVo> getUserPaperQuestionListByCondition(UserPaperQuestionConditionBo condition);

    Integer selectMaxOrderByUserPaperPageOid(@Param("userPaperPageOid") String userPaperPageOid);


    List<UserPaperQuestionVo> selectByUserPaperOid(@Param("userPaperOid") String userPaperOid);

    List<UserPaperQuestionVo> selectByUserPaperPageOid(@Param("userPaperPageOid") String userPaperPageOid);

}
