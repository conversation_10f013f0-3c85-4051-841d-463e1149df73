package com.light.aiszzy.schoolResourcesQuestion.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.schoolResourcesQuestion.entity.dto.SchoolResourcesQuestionDto;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionConditionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 资源库题目表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface SchoolResourcesQuestionMapper extends BaseMapper<SchoolResourcesQuestionDto> {

	List<SchoolResourcesQuestionVo> getSchoolResourcesQuestionListByCondition(SchoolResourcesQuestionConditionBo condition);

	SchoolResourcesQuestionVo selectByOid(@Param("oid") String oid);
}
