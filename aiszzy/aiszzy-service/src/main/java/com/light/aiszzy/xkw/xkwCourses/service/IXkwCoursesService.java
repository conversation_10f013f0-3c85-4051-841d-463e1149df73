package com.light.aiszzy.xkw.xkwCourses.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.xkw.xkwCourses.entity.dto.XkwCoursesDto;
import com.light.aiszzy.xkw.xkwCourses.entity.vo.XkwCoursesVo;

import java.util.List;

/**
 * 课程接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
public interface IXkwCoursesService extends IService<XkwCoursesDto> {


    /**
     * 根据年级，学科获取数据
     *
     * @param grade   the grade 年级
     * @param subject the subject 学科
     * @return {@link List }<{@link XkwCoursesVo }>
     */
    XkwCoursesVo queryByGradeAndSubject(Integer grade, String subject);


    /**
     *  根据学段、学科获取数据
     * @param stage 学段
     * @param subject 学科
     * @return {@link XkwCoursesVo }
     */
    XkwCoursesVo queryByStageAndSubject(Integer stage, String subject);


}

