package com.light.aiszzy.resourcesQuestion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.resourcesQuestion.entity.dto.ResourcesQuestionSimilarRelationDto;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationConditionBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionSimilarRelationVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 资源库题目相似题接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IResourcesQuestionSimilarRelationService extends IService<ResourcesQuestionSimilarRelationDto> {

    List<ResourcesQuestionSimilarRelationVo> getResourcesQuestionSimilarRelationListByCondition(ResourcesQuestionSimilarRelationConditionBo condition);

	AjaxResult addResourcesQuestionSimilarRelation(ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo);

	AjaxResult updateResourcesQuestionSimilarRelation(ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo);

    ResourcesQuestionSimilarRelationVo getDetail(String oid);

    /**
     *  保存原题相似题的关联信息
     * @param oid the resource question oid 原题 OID
     * @param similarQuestionsOidList the resources question similar question oid 相似题 OID集合
     * @return boolean
     */
    boolean saveResourcesQuestionSimilarRef(String oid, List<String> similarQuestionsOidList);
}

