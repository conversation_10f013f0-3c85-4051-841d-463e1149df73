package com.light.aiszzy.practiceBook.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 教辅目录每页图片记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("practice_book_page")
public class PracticeBookPageDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 二维码使用，8位，字母加数字，尝试10次，重复返回报错
	 */
	@TableField("practice_book_code")
	private String practiceBookCode;

	/**
	 * 教辅OID
	 */
	@TableField("practice_book_oid")
	private String practiceBookOid;

	/**
	 * 页码
	 */
	@TableField("page_no")
	private Integer pageNo;

	/**
	 * 图片地址
	 */
	@TableField("image_url")
	private String imageUrl;

	/**
	 * 修改后框题个数（实际）
	 */
	@TableField("question_num")
	private Long questionNum;


	/**
	 * 完成题目数量
	 */
	@TableField("finish_question_num")
	private Long finishQuestionNum;

	/**
	 * 解析框题个数
	 */
	@TableField("analysis_question_num")
	private Long analysisQuestionNum;

	/**
	 * 解析结果
	 */
	@TableField("analysis_json")
	private String analysisJson;

	/**
	 * 题目信息，题号，题目oid，坐标等
	 */
	@TableField("question_json")
	private String questionJson;

	/**
	 * 整页标注是否完成  0：未完成 1：完成
	 */
	@TableField("status")
	private Long status;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
