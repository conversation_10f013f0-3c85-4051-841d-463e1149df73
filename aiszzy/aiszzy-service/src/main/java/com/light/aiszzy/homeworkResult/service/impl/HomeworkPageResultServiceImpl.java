package com.light.aiszzy.homeworkResult.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkPageResultService;
import com.light.aiszzy.homeworkResult.mapper.HomeworkPageResultMapper;
import com.light.core.entity.AjaxResult;
/**
 * 扫描结构每页处理结果实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@Service
public class HomeworkPageResultServiceImpl extends ServiceImpl<HomeworkPageResultMapper, HomeworkPageResultDto> implements IHomeworkPageResultService {

	@Resource
	private HomeworkPageResultMapper homeworkPageResultMapper;
	
    @Override
	public List<HomeworkPageResultVo> getHomeworkPageResultListByCondition(HomeworkPageResultConditionBo condition) {
        return homeworkPageResultMapper.getHomeworkPageResultListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo) {
		HomeworkPageResultDto homeworkPageResult = new HomeworkPageResultDto();
		BeanUtils.copyProperties(homeworkPageResultBo, homeworkPageResult);
		homeworkPageResult.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkPageResult.setOid(IdUtil.simpleUUID());
		if(save(homeworkPageResult)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo) {
		LambdaQueryWrapper<HomeworkPageResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkPageResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkPageResultDto::getOid, homeworkPageResultBo.getOid());
		HomeworkPageResultDto homeworkPageResult = getOne(lqw);
		Long id = homeworkPageResult.getId();
		if(homeworkPageResult == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkPageResultBo, homeworkPageResult);
		homeworkPageResult.setId(id);
		if(updateById(homeworkPageResult)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkPageResultVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkPageResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkPageResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkPageResultDto::getOid, oid);
		HomeworkPageResultDto homeworkPageResult = getOne(lqw);
	    HomeworkPageResultVo homeworkPageResultVo = new HomeworkPageResultVo();
		if(homeworkPageResult != null){
			BeanUtils.copyProperties(homeworkPageResult, homeworkPageResultVo);
		}
		return homeworkPageResultVo;
	}

	@Override
	public AjaxResult dealNoStudent(HomeworkPageResultBo bo) {
		return null;
	}

	@Override
	public AjaxResult dealRepeat(HomeworkPageResultBo bo) {
		return null;
	}

}