package com.light.aiszzy.xkw.xkwPaperInfo.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 题目信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xkw_paper_info")
public class XkwPaperInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 试卷id
	 */
	@TableField("paper_id")
	private String paperId;

	/**
	 * 副标题
	 */
	@TableField("head_sub_title")
	private String headSubTitle;

	/**
	 * 试卷信息
	 */
	@TableField("head_test_info")
	private String headTestInfo;

	/**
	 * 学生填写部分
	 */
	@TableField("head_student_input")
	private String headStudentInput;

	/**
	 * 试卷标题
	 */
	@TableField("head_main_title")
	private String headMainTitle;

	/**
	 * 提示
	 */
	@TableField("head_notice")
	private String headNotice;

	/**
	 * 课程ID
	 */
	@TableField("course_id")
	private Integer courseId;

	/**
	 * 试卷题目
	 */
	@TableField("body_questions")
	private String bodyQuestions;

	/**
	 * 作业oid
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@TableField("is_delete")
	private Long isDelete;

}
