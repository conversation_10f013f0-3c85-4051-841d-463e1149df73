package com.light.aiszzy.resultUploadFile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.resultUploadFile.entity.dto.ResultUploadFileDto;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileConditionBo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileBo;
import com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 扫描上传图片接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
public interface IResultUploadFileService extends IService<ResultUploadFileDto> {

    List<ResultUploadFileVo> getResultUploadFileListByCondition(ResultUploadFileConditionBo condition);

	AjaxResult addResultUploadFile(ResultUploadFileBo resultUploadFileBo);

	AjaxResult updateResultUploadFile(ResultUploadFileBo resultUploadFileBo);

    ResultUploadFileVo getDetail(String oid);

}

