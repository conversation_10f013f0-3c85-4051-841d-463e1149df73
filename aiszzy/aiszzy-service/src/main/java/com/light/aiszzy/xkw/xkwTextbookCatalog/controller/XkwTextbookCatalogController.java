package com.light.aiszzy.xkw.xkwTextbookCatalog.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.api.XkwTextbookCatalogApi;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogConditionBo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.vo.XkwTextbookCatalogVo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.service.IXkwTextbookCatalogService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 教材目录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@RestController
@Validated
@Api(value = "", tags = "教材目录接口")
public class XkwTextbookCatalogController implements XkwTextbookCatalogApi {

    @Autowired
    private IXkwTextbookCatalogService xkwTextbookCatalogService;

    public AjaxResult<PageInfo<XkwTextbookCatalogVo>> getXkwTextbookCatalogPageListByCondition(@RequestBody XkwTextbookCatalogConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<XkwTextbookCatalogVo> pageInfo = new PageInfo<>(xkwTextbookCatalogService.getXkwTextbookCatalogListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<XkwTextbookCatalogVo>> getXkwTextbookCatalogListByCondition(@RequestBody XkwTextbookCatalogConditionBo condition) {
        return AjaxResult.success(xkwTextbookCatalogService.getXkwTextbookCatalogListByCondition(condition));
    }


}
