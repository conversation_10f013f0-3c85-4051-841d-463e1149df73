package com.light.aiszzy.userPaper.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.question.entity.dto.QuestionDto;
import com.light.aiszzy.question.mapper.QuestionMapper;
import com.light.aiszzy.question.service.IQuestionService;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionVo;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionService;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo;
import com.light.aiszzy.schoolResourcesQuestion.service.ISchoolResourcesQuestionService;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionConditionBo;
import com.light.aiszzy.userPaper.entity.dto.UserPaperQuestionDto;
import com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperVo;
import com.light.aiszzy.userPaper.mapper.UserPaperQuestionMapper;
import com.light.aiszzy.userPaper.service.IUserPaperPageService;
import com.light.aiszzy.userPaper.service.IUserPaperQuestionService;
import com.light.aiszzy.userPaper.service.IUserPaperService;
import com.light.aiszzy.xkw.xkwCourses.entity.dto.XkwCoursesDto;
import com.light.aiszzy.xkw.xkwCourses.mapper.XkwCoursesMapper;
import com.light.contants.AISzzyConstants;
import com.light.contants.ConstantsInteger;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.WarningException;
import com.light.enums.InsideSourceTypeEnum;
import com.light.enums.MarkStatusEnum;
import com.light.utils.ImgUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 资源库试卷表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Service
public class UserPaperQuestionServiceImpl extends ServiceImpl<UserPaperQuestionMapper, UserPaperQuestionDto> implements IUserPaperQuestionService {

	@Resource
	private UserPaperQuestionMapper userPaperQuestionMapper;

	@Resource
	private IUserPaperService userPaperService;
	@Resource
	private XkwCoursesMapper xkwCoursesMapper;

	@Resource
	private IUserPaperPageService userPaperPageService;

	@Resource
	private IResourcesQuestionService resourcesQuestionService;

	@Resource
	private ISchoolResourcesQuestionService schoolResourcesQuestionService;

    @Resource
	private QuestionMapper questionMapper;

    @Override
	public List<UserPaperQuestionVo> getUserPaperQuestionListByCondition(UserPaperQuestionConditionBo condition) {
        return userPaperQuestionMapper.getUserPaperQuestionListByCondition(condition);
	}

	@Override
	public AjaxResult<UserPaperQuestionVo> addUserPaperQuestion(UserPaperQuestionBo userPaperQuestionBo) {


		String userPaperPageOid = userPaperQuestionBo.getUserPaperPageOid();
		UserPaperPageVo detail = this.userPaperPageService.getDetail(userPaperPageOid);
		if(detail == null){
			throw new WarningException("页码数据不存在");
		}

		long orderNum = 1;
		// 如果下一个题目 OID 为空 将插入到末尾
		String nextUserPaperQuestionOid = userPaperQuestionBo.getNextUserPaperQuestionOid();
		if(StrUtil.isEmpty(nextUserPaperQuestionOid)) {
			// 获取最大序号值
			Integer maxOrderNum = this.baseMapper.selectMaxOrderByUserPaperPageOid(userPaperPageOid);
			orderNum = maxOrderNum + 1;
		}else {
			UserPaperQuestionVo userPaperQuestionVo = Optional.ofNullable(this.queryByOid(nextUserPaperQuestionOid)).orElseThrow(()-> new WarningException("题目信息存在"));
			orderNum = userPaperQuestionVo.getOrderNum() - 1;
		}

		userPaperQuestionBo.setUserPaperOid(detail.getUserPaperOid());
		userPaperQuestionBo.setOid(IdUtil.simpleUUID());
		userPaperQuestionBo.setPageNum(detail.getPageNo());


		UserPaperQuestionDto userPaperQuestionDto = new UserPaperQuestionDto();
		BeanUtils.copyProperties(userPaperQuestionBo, userPaperQuestionDto);
		userPaperQuestionDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
		userPaperQuestionDto.setOid(IdUtil.simpleUUID());
		userPaperQuestionDto.setOrderNum(orderNum);

		this.save(userPaperQuestionDto);

		return AjaxResult.success(BeanUtil.toBean(userPaperQuestionDto, UserPaperQuestionVo.class));

	}

	@Override
	public AjaxResult updateUserPaperQuestion(UserPaperQuestionBo userPaperQuestionBo) {
		LambdaQueryWrapper<UserPaperQuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(UserPaperQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(UserPaperQuestionDto::getOid, userPaperQuestionBo.getOid());
		UserPaperQuestionDto userPaperQuestion = getOne(lqw);
		Long id = userPaperQuestion.getId();
		if(userPaperQuestion == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(userPaperQuestionBo, userPaperQuestion);
		userPaperQuestion.setId(id);
		if(updateById(userPaperQuestion)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public UserPaperQuestionVo getDetail(String oid) {
		LambdaQueryWrapper<UserPaperQuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(UserPaperQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(UserPaperQuestionDto::getOid, oid);
		UserPaperQuestionDto userPaperQuestion = getOne(lqw);
	    UserPaperQuestionVo userPaperQuestionVo = new UserPaperQuestionVo();
		if(userPaperQuestion != null){
			BeanUtils.copyProperties(userPaperQuestion, userPaperQuestionVo);
		}
		return userPaperQuestionVo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean delAndSaveBatchByUserPaperPageOid(String userPaperPageOid, List<UserPaperQuestionBo> userPaperQuestionList) {
		// 根据页码表 OID 删除原有数据
		this.deleteByUserPaperPageOid(userPaperPageOid);

		List<UserPaperQuestionDto> questionBoList = userPaperQuestionList.stream().map(x -> {
			x.setOid(IdUtil.fastSimpleUUID());
			x.setUserPaperPageOid(userPaperPageOid);
			return BeanUtil.toBean(x, UserPaperQuestionDto.class);
		}).collect(Collectors.toList());

		this.saveBatch(questionBoList);
		return false;
	}

	private boolean deleteByUserPaperPageOid(String userPaperPageOid) {
		LambdaUpdateWrapper<UserPaperQuestionDto> lqw = new LambdaUpdateWrapper<>();
		lqw.set(UserPaperQuestionDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		lqw.eq(UserPaperQuestionDto::getUserPaperPageOid, userPaperPageOid);
		return this.update(lqw);
	}


	@Override
	public UserPaperQuestionVo queryByOid(String oid) {
		LambdaQueryWrapper<UserPaperQuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(UserPaperQuestionDto::getOid, oid);
		lqw.eq(UserPaperQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		UserPaperQuestionDto userPaperQuestionDto = this.baseMapper.selectOne(lqw);
		if(userPaperQuestionDto == null){
			return null;
		}
		return BeanUtil.toBean(userPaperQuestionDto, UserPaperQuestionVo.class);
	}

	@Override
	public UserPaperQuestionVo processAndUpdateQuestionByPositionImg(String oid, String position) {
		// 获取题目信息
		UserPaperQuestionVo vo = Optional.ofNullable(this.queryByOid(oid))
				.orElseThrow(() -> new WarningException("题目信息不存在"));
		return this.processAndUpdateQuestionByPositionImg(vo, position);
	}

	@Override
	public UserPaperQuestionVo processAndUpdateQuestionByPositionImg(UserPaperQuestionVo vo, String position) {
		String userPaperOid = vo.getUserPaperOid();
		UserPaperVo userPaperVo = Optional.ofNullable(this.userPaperService.getDetail(userPaperOid))
				.orElseThrow(() -> new WarningException("所属校本信息不存在"));

		String userPaperPageOid = vo.getUserPaperPageOid();
		UserPaperPageVo pageVo = this.userPaperPageService.getDetail(userPaperPageOid);

		String base64Img = ImgUtil.cutQuestionPageImg2Base64(pageVo.getImageUrl(), position);

		// 获取课程
		Integer stage = AISzzyConstants.getXkwStageByGrade(userPaperVo.getGrade());
		String subject = AISzzyConstants.getXkwSubject(userPaperVo.getSubject().toString());
		Integer xkwCourseId = Optional.ofNullable(xkwCoursesMapper.selectOne(new LambdaUpdateWrapper<XkwCoursesDto>()
						.eq(XkwCoursesDto::getSubjectId, subject)
						.eq(XkwCoursesDto::getStageId, stage)))
				.map(XkwCoursesDto::getId)
				.map(Long::intValue)
				.orElse(null);


		// 获取题目数据
		String schoolResourceQuestionOid = null;
		ResourcesQuestionVo resourcesQuestionVo = this.resourcesQuestionService.queryAndSaveXkwQuestionsByImg(xkwCourseId, base64Img);
		if(resourcesQuestionVo != null) {
			SchoolResourcesQuestionBo questionBo = BeanUtil.toBean(resourcesQuestionVo, SchoolResourcesQuestionBo.class);
			questionBo.setOid(IdUtil.fastSimpleUUID());
			questionBo.setOrgCode(vo.getOrgCode());
			this.schoolResourcesQuestionService.addSchoolResourcesQuestion(questionBo);
			schoolResourceQuestionOid = questionBo.getOid();
			vo.setSchoolResourcesQuestion(BeanUtil.toBean(questionBo, SchoolResourcesQuestionVo.class));
			vo.setSchoolResourceQuestionOid(schoolResourceQuestionOid);
		}

		// 更新题目题目信息
		UpdateWrapper<UserPaperQuestionDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperQuestionDto::getId, vo.getId());
		updateWrapper.lambda().set(UserPaperQuestionDto::getPosition, position);
		updateWrapper.lambda().set(UserPaperQuestionDto::getSchoolResourceQuestionOid, schoolResourceQuestionOid);
		if(resourcesQuestionVo != null) {
			updateWrapper.lambda().set(UserPaperQuestionDto::getThirdOutId, resourcesQuestionVo.getThirdOutId());
			updateWrapper.lambda().set(UserPaperQuestionDto::getThirdSourceType, resourcesQuestionVo.getThirdSourceType());
		}
		this.update(updateWrapper);

		vo.setPosition(position);
		return vo;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult cancelUserPaperQuestion(UserPaperQuestionBo bo) {
		String oid = bo.getOid();
		if(StrUtil.isEmpty(oid)) {
			return AjaxResult.fail("OID 不能为空");
		}
		UserPaperQuestionVo userPaperQuestionVo = Optional.ofNullable(this.queryByOid(oid)).orElseThrow(()-> new WarningException("题目信息不存在"));

		// 删除题目信息
		UpdateWrapper<UserPaperQuestionDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperQuestionDto::getOid, oid);
		updateWrapper.lambda().set(UserPaperQuestionDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		this.update(updateWrapper);

		// 更新页码题目数量
		this.userPaperPageService.addQuestionNumByOid(userPaperQuestionVo.getUserPaperPageOid(), -1);

		return AjaxResult.success();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult markUserPaperQuestion(String oid) {
		if(StrUtil.isEmpty(oid)) {
			return AjaxResult.fail("OID 不能为空");
		}
		// 1.查询校本题目信息
		UserPaperQuestionVo userPaperQuestionVo = Optional.ofNullable(this.queryByOid(oid)).orElseThrow(() -> new WarningException("题目信息不存在"));

		if(MarkStatusEnum.COMPLETED.getCode().equals(userPaperQuestionVo.getMarkStatus())) {
			return AjaxResult.fail("该题目已完成标注");
		}
		if(StrUtil.isEmpty(userPaperQuestionVo.getUserPaperOid())) {
			return AjaxResult.fail("该题目所属校本OID 不能为空");
		}
		if(StrUtil.isEmpty(userPaperQuestionVo.getUserPaperPageOid())) {
			return AjaxResult.fail("该题目所属校本页码OID 不能为空");
		}
		if(StrUtil.isEmpty(userPaperQuestionVo.getSchoolResourceQuestionOid())){
			return AjaxResult.fail("该题目所属学校资源题目OID 不能为空");
		}

		// 2.更新题目标注状态为已完成
		UpdateWrapper<UserPaperQuestionDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperQuestionDto::getOid, oid);
		updateWrapper.lambda().set(UserPaperQuestionDto::getMarkStatus, MarkStatusEnum.COMPLETED.getCode());
		// 更新标注状态
		if(!this.update(updateWrapper)) {
			throw new WarningException("标注题目状态失败");
		}

		// 3.更新校本页和校本完成框体数量+1
		if(!this.userPaperPageService.addFinishQuestionNumByOid(userPaperQuestionVo.getUserPaperPageOid(), ConstantsInteger.NUM_1)) {
			throw new WarningException("更新所属页或校本完成题目数量失败");
		}

		// 4.组装question题目信息&保存
		SchoolResourcesQuestionVo schoolResourcesQuestionVo = this.schoolResourcesQuestionService.getDetail(userPaperQuestionVo.getSchoolResourceQuestionOid());
		QuestionDto questionDto = BeanUtil.toBean(schoolResourcesQuestionVo, QuestionDto.class);
		questionDto.setOid(IdUtil.fastSimpleUUID());
		questionDto.setId(null);
		questionDto.setCreateBy(null);
		questionDto.setUpdateBy(null);
		questionDto.setCreateTime(null);
		questionDto.setUpdateTime(null);
		questionDto.setInsideLinkOid(schoolResourcesQuestionVo.getOid());
		questionDto.setInsideSourceType(InsideSourceTypeEnum.SCHOOL_RESOURCE_QUESTION.getCode());
        questionDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
		// 插入question表
        this.questionMapper.insert(questionDto);

		// 5.回填school_resources_question字段questionId
		schoolResourcesQuestionVo.setQuestionOid(questionDto.getOid());
		if(!this.schoolResourcesQuestionService.updateSchoolResourcesQuestion(BeanUtil.toBean(schoolResourcesQuestionVo,SchoolResourcesQuestionBo.class)).getSuccess()){
			throw new WarningException("更新学校资源题目信息失败");
		}

		return AjaxResult.success();
	}

	@Override
	public boolean deleteByUserPaperOid(String userPaperOid) {

		UpdateWrapper<UserPaperQuestionDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperQuestionDto::getUserPaperOid, userPaperOid);
		updateWrapper.lambda().set(UserPaperQuestionDto::getIsDelete, StatusEnum.ISDELETE.getCode());

		return this.update(updateWrapper);
	}

	@Override
	public List<UserPaperQuestionVo> queryByUserPaperOid(String userPaperOid) {
		return this.baseMapper.selectByUserPaperOid(userPaperOid);
	}

	@Override
	public List<UserPaperQuestionVo> queryByUserPaperPageOid(String userPaperPageOid) {
		return this.baseMapper.selectByUserPaperPageOid(userPaperPageOid);
	}
}