package com.light.aiszzy.resourcesQuestion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionSimilarRelationService;
import com.light.aiszzy.xkwOpen.service.XkwApi;
import com.light.beans.*;
import com.light.aiszzy.xkwOpen.service.XkwService;
import com.light.enums.ThirdSourceTypeEnum;
import com.light.enums.XkwDifficultyLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.resourcesQuestion.entity.dto.ResourcesQuestionDto;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionConditionBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionBo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionVo;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionService;
import com.light.aiszzy.resourcesQuestion.mapper.ResourcesQuestionMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资源库题目表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
@Slf4j
public class ResourcesQuestionServiceImpl extends ServiceImpl<ResourcesQuestionMapper, ResourcesQuestionDto>
    implements IResourcesQuestionService {

    @Resource
    private ResourcesQuestionMapper resourcesQuestionMapper;

    @Resource
    private IResourcesQuestionSimilarRelationService similarRelationService;

    @Resource
    private XkwService xkwService;

    @Resource
    private XkwApi xkwApi;

    @Override
    public List<ResourcesQuestionVo> getResourcesQuestionListByCondition(ResourcesQuestionConditionBo condition) {
        return resourcesQuestionMapper.getResourcesQuestionListByCondition(condition);
    }

    @Override
    public AjaxResult addResourcesQuestion(ResourcesQuestionBo resourcesQuestionBo) {
        ResourcesQuestionDto resourcesQuestion = new ResourcesQuestionDto();
        BeanUtils.copyProperties(resourcesQuestionBo, resourcesQuestion);
        resourcesQuestion.setIsDelete(StatusEnum.NOTDELETE.getCode());
        resourcesQuestion.setOid(IdUtil.simpleUUID());
        if (save(resourcesQuestion)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateResourcesQuestion(ResourcesQuestionBo resourcesQuestionBo) {
        LambdaQueryWrapper<ResourcesQuestionDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ResourcesQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ResourcesQuestionDto::getOid, resourcesQuestionBo.getOid());
        ResourcesQuestionDto resourcesQuestion = getOne(lqw);
        Long id = resourcesQuestion.getId();
        if (resourcesQuestion == null) {
            return AjaxResult.fail("保存失败");
        }
        BeanUtils.copyProperties(resourcesQuestionBo, resourcesQuestion);
        resourcesQuestion.setId(id);
        if (updateById(resourcesQuestion)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ResourcesQuestionVo getDetail(String oid) {
        LambdaQueryWrapper<ResourcesQuestionDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ResourcesQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ResourcesQuestionDto::getOid, oid);
        ResourcesQuestionDto resourcesQuestion = getOne(lqw);
        ResourcesQuestionVo resourcesQuestionVo = new ResourcesQuestionVo();
        if (resourcesQuestion != null) {
            BeanUtils.copyProperties(resourcesQuestion, resourcesQuestionVo);
        }
        return resourcesQuestionVo;
    }

    @Override
    public ResourcesQuestionBo getByThirdOutIdAndSourceType(String thirdOutId, String thirdSourceType) {
        LambdaQueryWrapper<ResourcesQuestionDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ResourcesQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ResourcesQuestionDto::getThirdOutId, thirdOutId);
        lqw.eq(ResourcesQuestionDto::getThirdSourceType, thirdSourceType);
        ResourcesQuestionDto resourcesQuestion = getOne(lqw);

        if (resourcesQuestion != null) {
            ResourcesQuestionBo resourcesQuestionBo = new ResourcesQuestionBo();
            BeanUtils.copyProperties(resourcesQuestion, resourcesQuestionBo);
            return resourcesQuestionBo;
        }
        return null;
    }

    @Override
    public String getOidByThirdOutId(com.light.enums.ThirdSourceTypeEnum thirdSourceType, String thirdOutId) {
        if (thirdSourceType == null || StringUtils.isBlank(thirdOutId)) {
            return null;
        }

        // 复用现有方法，符合DDD单一职责原则
        ResourcesQuestionBo resourcesQuestion = getByThirdOutIdAndSourceType(thirdOutId, thirdSourceType.getCode());

        return resourcesQuestion != null ? resourcesQuestion.getOid() : null;
    }

    @Override
    public List<ResourcesQuestionBo> saveSimilarQuestionsFromXkw(List<SimilarRecommendVo> similarQuestions) {
        List<ResourcesQuestionBo> savedQuestions = new ArrayList<>();

        if (CollUtil.isEmpty(similarQuestions)) {
            return savedQuestions;
        }

        // 批量查询已存在的题目，避免在循环中逐个查询数据库
        List<String> thirdOutIds =
            similarQuestions.stream().map(SimilarRecommendVo::getId).distinct().collect(Collectors.toList());

        List<ResourcesQuestionBo> existingQuestions =
            getBoListByThirdOutIds(thirdOutIds, ThirdSourceTypeEnum.XKW.getCode());
        Map<String, ResourcesQuestionBo> existingQuestionMap =
            existingQuestions.stream().collect(Collectors.toMap(ResourcesQuestionBo::getThirdOutId, q -> q));

        for (SimilarRecommendVo similarQuestion : similarQuestions) {
            try {
                // 从Map中检查是否已存在
                ResourcesQuestionBo existingQuestion = existingQuestionMap.get(similarQuestion.getId());
                if (existingQuestion != null) {
                    // 题目已存在，将已存在的题目添加到返回列表中
                    savedQuestions.add(existingQuestion);
                    log.info("题目已存在，已添加到返回列表: thirdOutId={}", similarQuestion.getId());
                    continue;
                }

                // 创建ResourcesQuestionBo对象
                ResourcesQuestionBo resourcesQuestionBo = buildResourcesQuestionFromSimilar(similarQuestion);

                // 保存到数据库
                AjaxResult saveResult = addResourcesQuestion(resourcesQuestionBo);
                if (saveResult.isSuccess()) {
                    savedQuestions.add(resourcesQuestionBo);
                    log.info("成功保存学科网相似题目: thirdOutId={}", similarQuestion.getId());
                } else {
                    log.error("保存学科网相似题目失败: thirdOutId={}, error={}", similarQuestion.getId(), saveResult.getMsg());
                }

            } catch (Exception e) {
                log.error("保存题目到resource_question表失败: thirdOutId={}", similarQuestion.getId(), e);
            }
        }

        return savedQuestions;
    }

    /**
     * 从学科网相似题目构建ResourcesQuestionBo对象
     */
    private ResourcesQuestionBo buildResourcesQuestionFromSimilar(SimilarRecommendVo similarQuestion) {
        ResourcesQuestionBo resourcesQuestionBo = new ResourcesQuestionBo();
        resourcesQuestionBo.setThirdOutId(similarQuestion.getId());
        resourcesQuestionBo.setThirdSourceType(ThirdSourceTypeEnum.XKW.getCode());
        resourcesQuestionBo.setQuestionJson(JSON.toJSONString(similarQuestion));

        // 设置其他字段
        if (similarQuestion.getCourse() != null) {
            resourcesQuestionBo.setSubject(similarQuestion.getCourse().getId());
        }
        if (similarQuestion.getDifficulty_level() != null) {
            resourcesQuestionBo.setDifficultId(similarQuestion.getDifficulty_level());
        }
        if (similarQuestion.getType() != null) {
            resourcesQuestionBo.setQuestionTypeId(similarQuestion.getType().getId());
            resourcesQuestionBo.setQuestionTypeName(similarQuestion.getType().getName());
        }

        // 设置题目内容
        if (StringUtils.isNotBlank(similarQuestion.getStem())) {
            resourcesQuestionBo.setQuesBody(similarQuestion.getStem());
            resourcesQuestionBo.setQuesBodyType(1L); // HTML文字
        }

        // 设置答案内容
        if (StringUtils.isNotBlank(similarQuestion.getAnswer())) {
            resourcesQuestionBo.setQuesAnswer(similarQuestion.getAnswer());
            resourcesQuestionBo.setQuesAnswerType(1L); // HTML文字
        }

        // 设置解析内容
        if (StringUtils.isNotBlank(similarQuestion.getExplanation())) {
            resourcesQuestionBo.setAnalysisAnswer(similarQuestion.getExplanation());
            resourcesQuestionBo.setAnalysisAnswerType(1L); // HTML文字
        }

        // 解析知识点
        if (CollectionUtils.isNotEmpty(similarQuestion.getKpoint_ids())) {
            String knowledgePointsIdStr = Joiner.on(",").skipNulls().join(similarQuestion.getKpoint_ids());
            resourcesQuestionBo.setKnowledgePointsId(knowledgePointsIdStr);
        }

        return resourcesQuestionBo;
    }

    @Override
    public List<ResourcesQuestionBo> queryAndSaveXkwSimilarQuestions(String questionText, Integer xkwCourseId,
        List<Integer> difficultyLevels, Integer everyTypeQuestionCount) {
        try {
            // 1. 调用学科网服务查询相似题目
            List<SimilarRecommendVo> similarQuestions =
                xkwApi.querySimilarQuestions(questionText, xkwCourseId, difficultyLevels, everyTypeQuestionCount);

            if (similarQuestions == null || similarQuestions.isEmpty()) {
                log.info("未查询到学科网相似题目，查询条件：questionText={}, xkwCourseId={}, difficultyLevels={}, questionCount={}",
                    questionText, xkwCourseId, difficultyLevels, everyTypeQuestionCount);
                return new ArrayList<>();
            }

            // 2. 保存查询到的题目到resource_question表
            List<ResourcesQuestionBo> savedQuestions = saveSimilarQuestionsFromXkw(similarQuestions);

            log.info("成功查询并保存学科网题目，查询到{}个题目，成功保存{}个题目", similarQuestions.size(), savedQuestions.size());

            return savedQuestions;

        } catch (Exception e) {
            log.error("查询并保存学科网题目失败：questionText={}, xkwCourseId={}, difficultyLevels={}, questionCount={}",
                questionText, xkwCourseId, difficultyLevels, everyTypeQuestionCount, e);
            throw new RuntimeException("查询并保存学科网题目失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ResourcesQuestionVo> queryAndSaveXkwQuestionsByImg(Integer xkwCourseId, String imgBase64,
        Integer count) {

        // 从学科网获取主题干信息
        TikuBo bo = new TikuBo();
        bo.setCount(count);
        bo.setImage_base64(imgBase64);
        bo.setCourse_id(xkwCourseId);
        XkwApiResponseVo<List<XkwQuestionVo>> data = this.xkwApi.questionBffTextSearch(bo);
        if (data == null) {
            return Collections.emptyList();
        }

        // 保存并存储主题感信息
        List<XkwQuestionVo> questions = data.getData();
        if(CollUtil.isEmpty(questions)) {
            return Collections.emptyList();
        }
        List<ResourcesQuestionVo> questionVoList = questions.stream()
                .map(this::buildResourcesQuestionFromXkwQuestion)
                .peek(x-> x.setImageOcrText(data.getOrDefault("image_ocr_text","").toString()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(questionVoList)) {
            return Collections.emptyList();
        }

        // 获取原有题目信息，如有不进行新增
        List<String> xkwIdList = questionVoList.stream()
                .map(ResourcesQuestionVo::getThirdOutId).distinct().collect(Collectors.toList());

        List<ResourcesQuestionDto> resourcesQuestionDtos =
            this.queryDtoListByThirdOutId(xkwIdList, ThirdSourceTypeEnum.XKW.getCode());
        Map<String, ResourcesQuestionDto> thirdOutIdMap =
            resourcesQuestionDtos.stream().collect(Collectors.toMap(ResourcesQuestionDto::getThirdOutId, x -> x));

        // 排除数据库中已经存在的题目信息
        List<ResourcesQuestionDto> questionDTOList =
            questionVoList.stream().filter(x -> !thirdOutIdMap.containsKey(x.getThirdOutId()))
                .map(x -> BeanUtil.toBean(x, ResourcesQuestionDto.class)).peek(x -> x.setOid(IdUtil.fastSimpleUUID()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(questionDTOList)) {
            this.saveBatch(questionDTOList);
        }

        return questionVoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResourcesQuestionVo queryAndSaveXkwQuestionsByImg(Integer xkwCourseId, String imgBase64) {
        List<ResourcesQuestionVo> questionVoList = this.queryAndSaveXkwQuestionsByImg(xkwCourseId, imgBase64, 1);
        if (CollUtil.isEmpty(questionVoList)) {
            return null;
        }
        return questionVoList.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResourcesQuestionVo queryAndSaveQuestionWithSimilarRelationsByImage(Integer xkwCourseId, String imgBase64) {

        // 获取题目数据
        ResourcesQuestionVo resourcesQuestionVo = this.queryAndSaveXkwQuestionsByImg(xkwCourseId, imgBase64);

        if (resourcesQuestionVo == null) {
            return null;
        }

        // 3. 查询并保存学科网题目到resource_question表
        List<ResourcesQuestionBo> similarQuestions =
                this.queryAndSaveXkwSimilarQuestions(
                    resourcesQuestionVo.getQuesBody(), xkwCourseId,
                     XkwService.RecommendStrategy.DEFAULT.getDifficulties().stream()
                             .map(XkwDifficultyLevelEnum::getCode).collect(Collectors.toList()),
                    1);
        if (CollUtil.isEmpty(similarQuestions)) {
            return resourcesQuestionVo;
        }

        // 保存题目与相似题关联信息
        List<String> similarQuestionsOidList =
            similarQuestions.stream().map(ResourcesQuestionBo::getOid).collect(Collectors.toList());
        this.similarRelationService.saveResourcesQuestionSimilarRef(resourcesQuestionVo.getOid(),
            similarQuestionsOidList);

        List<ResourcesQuestionVo> similarQuestionVoList = similarQuestions.stream()
            .map(x -> BeanUtil.toBean(x, ResourcesQuestionVo.class)).collect(Collectors.toList());
        // 设置相似题
        resourcesQuestionVo.setSimilarQuestionList(similarQuestionVoList);

        return resourcesQuestionVo;
    }

    public List<ResourcesQuestionDto> queryDtoListByThirdOutId(List<String> thirdOutId, String thirdSourceType) {
        if (CollUtil.isEmpty(thirdOutId)) {
            return Collections.emptyList();
        }
        QueryWrapper<ResourcesQuestionDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ResourcesQuestionDto::getThirdOutId, thirdOutId);
        queryWrapper.lambda().eq(ResourcesQuestionDto::getThirdSourceType, thirdSourceType);
        queryWrapper.lambda().eq(ResourcesQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ResourcesQuestionBo> getBoListByThirdOutIds(List<String> thirdOutIds, String thirdSourceType) {
        if (CollUtil.isEmpty(thirdOutIds)) {
            return Collections.emptyList();
        }

        List<ResourcesQuestionDto> dtoList = queryDtoListByThirdOutId(thirdOutIds, thirdSourceType);
        if (CollUtil.isEmpty(dtoList)) {
            return Collections.emptyList();
        }

        return dtoList.stream().map(dto -> {
            ResourcesQuestionBo bo = new ResourcesQuestionBo();
            BeanUtils.copyProperties(dto, bo);
            return bo;
        }).collect(Collectors.toList());
    }

    /**
     * 从学科网相似题目构建ResourcesQuestionBo对象
     */
    private ResourcesQuestionVo buildResourcesQuestionFromXkwQuestion(XkwQuestionVo questionVo) {
        ResourcesQuestionVo resourcesQuestionBo = new ResourcesQuestionVo();
        resourcesQuestionBo.setThirdOutId(questionVo.getId());
        resourcesQuestionBo.setThirdSourceType(ThirdSourceTypeEnum.XKW.getCode());
        resourcesQuestionBo.setQuestionJson(JSON.toJSONString(questionVo));

        // 设置其他字段
        if (questionVo.getCourse() != null) {
            resourcesQuestionBo.setSubject(questionVo.getCourse().getId());
        }
        if (questionVo.getDifficulty_level() != null) {
            resourcesQuestionBo.setDifficultId(questionVo.getDifficulty_level());
        }
        if (questionVo.getType() != null) {
            resourcesQuestionBo.setQuestionTypeId(questionVo.getType().getId());
            resourcesQuestionBo.setQuestionTypeName(questionVo.getType().getName());
        }

        // 设置题目内容
        if (StringUtils.isNotBlank(questionVo.getStem())) {
            resourcesQuestionBo.setQuesBody(questionVo.getStem());
            resourcesQuestionBo.setQuesBodyType(1L); // HTML文字
        }

        // 设置答案内容
        if (StringUtils.isNotBlank(questionVo.getAnswer())) {
            resourcesQuestionBo.setQuesAnswer(questionVo.getAnswer());
            resourcesQuestionBo.setQuesAnswerType(1L); // HTML文字
        }

        // 设置解析内容
        if (StringUtils.isNotBlank(questionVo.getExplanation())) {
            resourcesQuestionBo.setAnalysisAnswer(questionVo.getExplanation());
            resourcesQuestionBo.setAnalysisAnswerType(1L); // HTML文字
        }

        // 解析知识点
        if (CollectionUtils.isNotEmpty(questionVo.getKpoint_ids())) {
            String knowledgePointsIdStr = Joiner.on(",").skipNulls().join(questionVo.getKpoint_ids());
            resourcesQuestionBo.setKnowledgePointsId(knowledgePointsIdStr);
        }
        if(CollUtil.isNotEmpty(questionVo.getCatalog_ids())) {
            String catalogIds = Joiner.on(",").skipNulls().join(questionVo.getCatalog_ids());
            resourcesQuestionBo.setChapterId(catalogIds);
        }

        return resourcesQuestionBo;
    }

    @Override
    public AjaxResult updateQuestionIdByThirdOutId(String thirdSourceType, String thirdOutId, String questionOid) {
        try {
            // 直接根据thirdOutId和thirdSourceType条件更新questionOid字段
            LambdaUpdateWrapper<ResourcesQuestionDto> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ResourcesQuestionDto::getThirdOutId, thirdOutId)
                .eq(ResourcesQuestionDto::getThirdSourceType, thirdSourceType)
                .eq(ResourcesQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .set(ResourcesQuestionDto::getQuestionOid, questionOid);

            boolean updateResult = update(updateWrapper);
            if (!updateResult) {
                return AjaxResult.fail("更新资源题目questionOid字段失败，可能记录不存在");
            }

            log.info("成功更新资源题目questionOid字段，thirdOutId={}, thirdSourceType={}, questionOid={}", thirdOutId,
                thirdSourceType, questionOid);

            return AjaxResult.success("更新成功");

        } catch (Exception e) {
            log.error("更新资源题目questionOid字段失败：thirdOutId={}, thirdSourceType={}, questionOid={}", thirdOutId,
                thirdSourceType, questionOid, e);
            return AjaxResult.fail("更新失败：" + e.getMessage());
        }
    }

}