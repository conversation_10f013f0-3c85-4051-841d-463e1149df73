package com.light.aiszzy.resultUploadFile.deal;

import com.light.aiszzy.resultUploadFile.entity.dto.ResultUploadFileDto;

/**
 * 扫描上传图片处理接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-28 09:37:01
 */
public interface IResultUploadFileDealService {

    /**
     * 处理扫描上传图片
     * 1、获取所有待处理图片
     * 2、识别图片二维码，查询作业、作业页、题目等信息，A4还是A3
     * 3、图片旋转处理,上传处理后的图片到服务器
     * 4、根据作业页的题目坐标信息截取图片
     * 5、图片处理，保留图中红色批改痕迹
     * 6、调用豆包接口判断题目是否正确
     * 7、无批改痕迹的，客观题：则将图片保存到list中，调用OCR识别手写体
     * 7、无批改痕迹的，主观题：默认为错误
     * 8、客观题：根据OCR识别结果与作业题答案匹配，一致则正确，否则错误
     * 9、保存处理结果，更新相关库表
     */
    void dealResultUploadFile();

}

