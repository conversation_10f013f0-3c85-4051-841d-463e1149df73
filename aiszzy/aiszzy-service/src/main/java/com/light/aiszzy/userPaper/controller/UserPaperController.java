package com.light.aiszzy.userPaper.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.userPaper.entity.bo.UserPaperConditionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperVo;
import com.light.aiszzy.userPaper.service.IUserPaperService;

import com.light.aiszzy.userPaper.api.UserPaperApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 用户上传试卷
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "用户上传试卷接口")
public class UserPaperController implements UserPaperApi {

    @Autowired
    private IUserPaperService userPaperService;

    public AjaxResult<PageInfo<UserPaperVo>> getUserPaperPageListByCondition(@RequestBody UserPaperConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),condition.getOrderBy());
        PageInfo<UserPaperVo> pageInfo = new PageInfo<>(userPaperService.getUserPaperListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<UserPaperVo>> getUserPaperListByCondition(@RequestBody UserPaperConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(userPaperService.getUserPaperListByCondition(condition));
    }

    public AjaxResult addUserPaper(@Validated @RequestBody UserPaperBo userPaperBo) {
        return userPaperService.addUserPaper(userPaperBo);
    }

    public AjaxResult updateUserPaper(@Validated @RequestBody UserPaperBo userPaperBo) {
        if (null == userPaperBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return userPaperService.updateUserPaper(userPaperBo);
    }

    public AjaxResult<UserPaperVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(userPaperService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            UserPaperBo userPaperBo = new UserPaperBo();
            userPaperBo.setOid(oid);
            userPaperBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return userPaperService.updateUserPaper(userPaperBo);
    }

    @Override
    public AjaxResult updatePublishByOid(@RequestParam("oid") String oid,@RequestParam("isPublish")  Integer isPublish) {
        return AjaxResult.success(this.userPaperService.updatePublishByOid(oid, isPublish));
    }

    @Override
    public AjaxResult deleteByOidAndUserOid(@PathVariable("oid") String oid, @PathVariable("userOid") String userOid) {
        boolean b = this.userPaperService.deleteByOidAndUserOid(oid, userOid);
        return AjaxResult.success(b);
    }
}
