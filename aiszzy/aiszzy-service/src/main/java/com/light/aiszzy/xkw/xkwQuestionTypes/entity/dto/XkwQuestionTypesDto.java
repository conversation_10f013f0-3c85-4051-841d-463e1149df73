package com.light.aiszzy.xkw.xkwQuestionTypes.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 试题类型
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-03 14:34:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xkw_question_types")
public class XkwQuestionTypesDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private String id;

	/**
	 * 	课程ID
	 */
	@TableField("course_id")
	private Long courseId;

	/**
	 * 题型名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 父题型
	 */
	@TableField("parent_id")
	private Long parentId;

	/**
	 * 排序值
	 */
	@TableField("ordinal")
	private Long ordinal;

	/**
	 * 是否客观题
	 */
	@TableField("objective")
	private String objective;

}
