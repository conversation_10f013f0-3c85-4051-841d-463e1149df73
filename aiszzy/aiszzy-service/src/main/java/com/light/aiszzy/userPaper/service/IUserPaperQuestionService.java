package com.light.aiszzy.userPaper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.userPaper.entity.dto.UserPaperQuestionDto;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionConditionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo;
import com.light.core.entity.AjaxResult;
import com.light.core.exception.WarningException;

import java.util.List;

/**
 * 资源库试卷表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface IUserPaperQuestionService extends IService<UserPaperQuestionDto> {

    List<UserPaperQuestionVo> getUserPaperQuestionListByCondition(UserPaperQuestionConditionBo condition);

	AjaxResult<UserPaperQuestionVo> addUserPaperQuestion(UserPaperQuestionBo userPaperQuestionBo);

	AjaxResult updateUserPaperQuestion(UserPaperQuestionBo userPaperQuestionBo);

    UserPaperQuestionVo getDetail(String oid);


    boolean delAndSaveBatchByUserPaperPageOid(String userPaperPageOid, List<UserPaperQuestionBo> userPaperQuestionList);

    UserPaperQuestionVo queryByOid(String oid);

    /**
     * 根据传入的 position 字段截取图片，转换为 Base64 格式，获取题目信息，并更新至 UserPaperQuestion 表。
     * <p>
     * 该方法的主要流程如下：
     * 1. 使用 Base64 数据查询题目信息，并获取相似题目信息。
     * 2. 将查询到的题目信息及相似题信息转换为所需格式。
     * 3. 更新题目数据（包括题目 JSON 和相似度）到 practiceBookQuestion 表中。
     *
     * @param vo       校本题目信息
     * @param position bse64截图的位置信息 x_y_宽_高，用于截取图片信息
     * @throws WarningException 当题目信息或所属教辅信息不存在时抛出。
     */
    UserPaperQuestionVo processAndUpdateQuestionByPositionImg(UserPaperQuestionVo vo, String position);

    /**
     * 根据传入的 position 字段截取图片，转换为 Base64 格式，获取题目信息，并更新至 practiceBookQuestion 表。
     * <p>
     * 该方法的主要流程如下：
     * 1. 使用 Base64 数据查询题目信息，并获取相似题目信息。
     * 2. 将查询到的题目信息及相似题信息转换为所需格式。
     * 3. 更新题目数据（包括题目 JSON 和相似度）到 practiceBookQuestion 表中。
     *
     * @param oid      题目 OID
     * @param position bse64截图的位置信息 x_y_宽_高，用于截取图片信息
     * @throws WarningException 当题目信息或所属教辅信息不存在时抛出。
     */
    UserPaperQuestionVo processAndUpdateQuestionByPositionImg(String oid, String position);


    /**
     *  取消滑题
     * @param bo the user paper question bo
     * @return {@link AjaxResult }
     */
    AjaxResult cancelUserPaperQuestion(UserPaperQuestionBo bo);

    /**
     * 标记校本题目
     * @param oid 校本题目oid
     * @return {@link AjaxResult}
     */
    AjaxResult markUserPaperQuestion(String oid);

    /**
     *  根据校本 OID 删除数据
     * @param userPaperOid 校本 OID
     * @return boolean
     */
    boolean deleteByUserPaperOid(String userPaperOid);


    /**
     *  根据校本 OID 获取校本题目信息
     * @param userPaperOid the user paper oid 校本 OID
     * @return {@link List }<{@link UserPaperQuestionVo }>
     */
    List<UserPaperQuestionVo> queryByUserPaperOid(String userPaperOid);

    /**
     * 根据校本页码 OID 获取题目列表
     * @param userPaperPageOid 页码 OID
     * @return {@link List }<{@link UserPaperQuestionVo }>
     */
    List<UserPaperQuestionVo> queryByUserPaperPageOid(String userPaperPageOid);
}

