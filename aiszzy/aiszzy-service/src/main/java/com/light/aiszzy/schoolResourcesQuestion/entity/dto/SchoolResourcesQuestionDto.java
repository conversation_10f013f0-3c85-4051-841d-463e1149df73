package com.light.aiszzy.schoolResourcesQuestion.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 资源库题目表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("school_resources_question")
public class SchoolResourcesQuestionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 学校CODE
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 题型id
	 */
	@TableField("question_type_id")
	private String questionTypeId;

	/**
	 * 题型名称
	 */
	@TableField("question_type_name")
	private String questionTypeName;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 年份
	 */
	@TableField("year")
	private String year;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 难度
	 */
	@TableField("difficult_id")
	private Integer difficultId;

	/**
	 * 题目url或文字
	 */
	@TableField("ques_body")
	private String quesBody;

	/**
	 * 公共题干url或文字
	 */
	@TableField("public_ques")
	private String publicQues;

	/**
	 * 答案url或文字
	 */
	@TableField("ques_answer")
	private String quesAnswer;

	/**
	 * 解析url或文字
	 */
	@TableField("analysis_answer")
	private String analysisAnswer;

	/**
	 * 题目展示类型  0：图片url  1：html文字 
	 */
	@TableField("ques_body_type")
	private Long quesBodyType;

	/**
	 * 公共题干展示类型  0：图片url  1：html文字
	 */
	@TableField("public_ques_type")
	private Long publicQuesType;

	/**
	 * 答案展示类型  0：图片url  1：html文字 
	 */
	@TableField("ques_answer_type")
	private Long quesAnswerType;

	/**
	 * 解析展示类型  0：图片url  1：html文字 
	 */
	@TableField("analysis_answer_type")
	private Long analysisAnswerType;


	/**
	 * 知识点
	 */
	@TableField("knowledge_points_id")
	private String knowledgePointsId;

	/**
	 * 章节ID
	 */
	@TableField("chapter_id")
	private String chapterId;

	/**
	 * 节ID
	 */
	@TableField("section_id")
	private String sectionId;

	/**
	 * 来源(学科网xkw，好未来hwl)
	 */
	@TableField("third_source_type")
	private String thirdSourceType;

	/**
	 * 外部id
	 */
	@TableField("third_out_id")
	private String thirdOutId;

	/**
	 * 关联question题目oid
	 */
	@TableField("question_oid")
	private String questionOid;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
