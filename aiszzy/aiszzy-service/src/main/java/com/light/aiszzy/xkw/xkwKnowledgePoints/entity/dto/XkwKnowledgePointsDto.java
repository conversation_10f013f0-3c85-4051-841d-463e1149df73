package com.light.aiszzy.xkw.xkwKnowledgePoints.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 知识树
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xkw_knowledge_points")
public class XkwKnowledgePointsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 知识点名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 课程ID
	 */
	@TableField("course_id")
	private Long courseId;

	/**
	 * 节点深度，一级节点的深度为1，二级节点的深度为2，以此类推。
	 */
	@TableField("depth")
	private String depth;

	/**
	 * root节点的ID
	 */
	@TableField("root_id")
	private String rootId;

	/**
	 * 父节点ID
	 */
	@TableField("parent_id")
	private String parentId;

	/**
	 * 适用于精简版
	 */
	@TableField("for_lite")
	private String forLite;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 一级知识点的标签，仅当节点为一级知识点时有效，分为：STANDARD|课标,CONTEST|竞赛,PREPARE|学段衔接,NEXT-GEN|新一代
	 */
	@TableField("tag")
	private String tag;

	/**
	 * 	节点类型，可用值：NODE、KNOWLEDGE_POINT、TESTING_POINT，分别代表普通节点、知识点、考点
	 */
	@TableField("type")
	private String type;

	/**
	 * 排序值
	 */
	@TableField("ordinal")
	private Long ordinal;

}
