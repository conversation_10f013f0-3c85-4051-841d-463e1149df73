package com.light.aiszzy.schoolBook.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.schoolBook.entity.dto.SchoolBookRecordDto;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordConditionBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookRecordVo;

/**
 * 教辅或作业本开通记录详情表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface SchoolBookRecordMapper extends BaseMapper<SchoolBookRecordDto> {

	List<SchoolBookRecordVo> getSchoolBookRecordListByCondition(SchoolBookRecordConditionBo condition);

}
