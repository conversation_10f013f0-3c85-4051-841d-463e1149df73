package com.light.aiszzy.xkw.xkwQuestionDifficulties.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.xkw.xkwCourses.service.IXkwCoursesService;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.dto.XkwQuestionDifficultiesDto;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.mapper.XkwQuestionDifficultiesMapper;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.service.IXkwQuestionDifficultiesService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 试题难度等级接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Service
public class XkwQuestionDifficultiesServiceImpl extends ServiceImpl<XkwQuestionDifficultiesMapper, XkwQuestionDifficultiesDto> implements IXkwQuestionDifficultiesService {


	@Resource
	private IXkwCoursesService xkwCoursesService;



}