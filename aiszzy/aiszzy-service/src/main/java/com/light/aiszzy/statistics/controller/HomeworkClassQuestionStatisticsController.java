package com.light.aiszzy.statistics.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.service.IHomeworkClassQuestionStatisticsService;
import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;

import com.light.aiszzy.statistics.api.HomeworkClassQuestionStatisticsApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 作业班级题目正确率
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@RestController
@Validated
@Api(value = "", tags = "作业班级题目正确率接口")
public class HomeworkClassQuestionStatisticsController implements HomeworkClassQuestionStatisticsApi {

    @Autowired
    private IHomeworkClassQuestionStatisticsService homeworkClassQuestionStatisticsService;

    public AjaxResult<PageInfo<HomeworkClassQuestionStatisticsVo>> getHomeworkClassQuestionStatisticsPageListByCondition(@RequestBody HomeworkClassQuestionStatisticsConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<HomeworkClassQuestionStatisticsVo> pageInfo = new PageInfo<>(homeworkClassQuestionStatisticsService.getHomeworkClassQuestionStatisticsListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkClassQuestionStatisticsVo>> getHomeworkClassQuestionStatisticsListByCondition(@RequestBody HomeworkClassQuestionStatisticsConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkClassQuestionStatisticsService.getHomeworkClassQuestionStatisticsListByCondition(condition));
    }

    public AjaxResult addHomeworkClassQuestionStatistics(@Validated @RequestBody HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo) {
        return homeworkClassQuestionStatisticsService.addHomeworkClassQuestionStatistics(homeworkClassQuestionStatisticsBo);
    }

    public AjaxResult updateHomeworkClassQuestionStatistics(@Validated @RequestBody HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo) {
        if (null == homeworkClassQuestionStatisticsBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkClassQuestionStatisticsService.updateHomeworkClassQuestionStatistics(homeworkClassQuestionStatisticsBo);
    }

    public AjaxResult<HomeworkClassQuestionStatisticsVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkClassQuestionStatisticsService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo = new HomeworkClassQuestionStatisticsBo();
            homeworkClassQuestionStatisticsBo.setOid(oid);
            homeworkClassQuestionStatisticsBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkClassQuestionStatisticsService.updateHomeworkClassQuestionStatistics(homeworkClassQuestionStatisticsBo);
    }
}
