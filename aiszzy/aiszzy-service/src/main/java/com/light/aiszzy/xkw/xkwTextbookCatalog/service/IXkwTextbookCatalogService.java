package com.light.aiszzy.xkw.xkwTextbookCatalog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogBo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogConditionBo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.dto.XkwTextbookCatalogDto;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.vo.XkwTextbookCatalogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 教材目录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
public interface IXkwTextbookCatalogService extends IService<XkwTextbookCatalogDto> {

    List<XkwTextbookCatalogVo> getXkwTextbookCatalogListByCondition(XkwTextbookCatalogConditionBo condition);


}

