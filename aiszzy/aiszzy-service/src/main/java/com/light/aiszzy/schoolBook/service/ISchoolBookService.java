package com.light.aiszzy.schoolBook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.schoolBook.entity.dto.SchoolBookDto;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookBo;
import com.light.aiszzy.schoolBook.entity.bo.BatchSchoolBookBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookVo;
import com.light.aiszzy.schoolBook.entity.vo.PracticeBookWithSchoolVo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookWithSchoolCountVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookExportVo;
import com.light.core.entity.AjaxResult;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 教辅或作业本开通记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface ISchoolBookService extends IService<SchoolBookDto> {

    List<SchoolBookVo> getSchoolBookListByCondition(SchoolBookConditionBo condition);

    AjaxResult addSchoolBook(SchoolBookBo schoolBookBo);

    /**
     * 批量开通教辅或作业本
     *
     * @param batchSchoolBookBo 批量开通请求对象
     * @return 批量开通结果
     * <AUTHOR>
     * @date 2025-07-18
     */
    AjaxResult batchAddSchoolBook(BatchSchoolBookBo batchSchoolBookBo);

    AjaxResult updateSchoolBook(SchoolBookBo schoolBookBo);

    SchoolBookVo getDetail(String oid);

    /**
     * 批量查询教辅的总开通学校数量
     * @param practiceBookOids 教辅OID集合
     * @return key: 教辅OID, value: 总开通学校数量
     * <AUTHOR>
     * @date 2025-07-12
     */
    Map<String, Integer> getTotalSchoolCountByPracticeBookOids(List<String> practiceBookOids);

    /**
     * 批量查询教辅的活跃学校数量（有效期内）
     * @param practiceBookOids 教辅OID集合
     * @return key: 教辅OID, value: 活跃学校数量
     * <AUTHOR>
     * @date 2025-07-12
     */
    Map<String, Integer> getActiveSchoolCountByPracticeBookOids(List<String> practiceBookOids);

    /**
     * 教辅或作业开通列表：查询教辅列表并聚合学校开通数量信息
     *
     * @param condition 教辅查询条件
     * @return 包含学校开通数量信息的教辅分页列表
     * <AUTHOR>
     * @date 2025-07-12
     */
    AjaxResult<PageInfo<PracticeBookWithSchoolCountVo>>
        getPracticeBookPageListWithSchoolCount(PracticeBookConditionBo condition);

    /**
     * 导出教辅开通列表
     *
     * @param condition 教辅查询条件
     * @return 教辅开通列表导出数据
     * <AUTHOR>  
     * @date 2025-07-24
     */
    AjaxResult<List<PracticeBookExportVo>>
        exportPracticeBookWithSchoolCount(PracticeBookConditionBo condition);

    /**
     * 查询教辅列表与学校开通信息关联
     *
     * @param condition 查询条件，支持org_code和book_oid等条件
     * @return 教辅与学校关联信息分页列表
     * <AUTHOR>
     * @date 2025-07-19
     */
    AjaxResult<PageInfo<PracticeBookWithSchoolVo>>
        getPracticeBookWithSchoolPageListByCondition(SchoolBookConditionBo condition);

}
