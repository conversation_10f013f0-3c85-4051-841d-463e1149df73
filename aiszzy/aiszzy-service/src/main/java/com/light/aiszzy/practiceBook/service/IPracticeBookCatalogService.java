package com.light.aiszzy.practiceBook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookCatalogDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 教辅目录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IPracticeBookCatalogService extends IService<PracticeBookCatalogDto> {

    List<PracticeBookCatalogVo> getPracticeBookCatalogListByCondition(PracticeBookCatalogConditionBo condition);

	AjaxResult addPracticeBookCatalog(PracticeBookCatalogBo practiceBookCatalogBo);

	AjaxResult updatePracticeBookCatalog(PracticeBookCatalogBo practiceBookCatalogBo);

    PracticeBookCatalogVo getDetail(String oid);

    /**
     *  根据教辅 OID 删除原有目录并保存 目录集合
     * @param practiceBookOid the practice book oid 教辅 OID
     * @param catalogBOList the catalog bo 目录集合
     * @return {@link AjaxResult }<{@link Void }>
     */
    AjaxResult<Void> delAndSaveBatchByPracticeBookOid(String practiceBookOid, List<PracticeBookCatalogBo> catalogBOList);

    /**
     *  根据 OID 删除目录信息
     * @param oid the catalog oid
     * @return boolean
     */
    boolean deleteByOid(String oid);


    /**
     *  根据教辅 OID 删除数据
     * @param practiceBookOid the practice book oid 教辅 OID
     * @return boolean
     */
    boolean deleteByPracticeBookOid(String practiceBookOid);

    /**
     * 根据教辅 OId 获取章节目录数量
     *
     * @param practiceBookOid the practice book oid  教辅 OID
     * @return {@link Long }
     */
    Integer queryCountByPracticeBookOid(String practiceBookOid);

    /**
     * 同级别按照集合顺序 重置 order num
     * @param catalogOidList  {catalogOidList 目录OID集合}
     * @return {@link AjaxResult }<{@link Void }>
     */
    AjaxResult<Void> resetOrderNum(List<String> catalogOidList);


    /**
     *  根据目录 OID集合获取数据
     * @param catalogList the catalog list
     * @return {@link List }<{@link PracticeBookCatalogVo }>
     */
    List<PracticeBookCatalogVo> queryByOidList(List<String> catalogList);

    /**
     *  根据教辅 OID 获取目录树形结构
     * @param practiceBookOid the practice book oid 教辅 OID
     * @return {@link List }<{@link PracticeBookCatalogVo }>
     */
    List<PracticeBookCatalogVo> queryTreeListByPracticeBookOid(String practiceBookOid);

    /**
     *  根据教辅 OID 获取目录数据
     * @param practiceBookOid the practice book oid
     * @return {@link List }<{@link PracticeBookCatalogVo }>
     */
    List<PracticeBookCatalogVo> queryByPracticeBookOid(String practiceBookOid);
}

