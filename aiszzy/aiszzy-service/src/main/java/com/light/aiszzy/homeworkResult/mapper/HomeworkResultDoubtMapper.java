package com.light.aiszzy.homeworkResult.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDoubtDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtConditionBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultDoubtVo;

/**
 * 扫描结构每页处理结果Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkResultDoubtMapper extends BaseMapper<HomeworkResultDoubtDto> {

	List<HomeworkResultDoubtVo> getHomeworkResultDoubtListByCondition(HomeworkResultDoubtConditionBo condition);

}
