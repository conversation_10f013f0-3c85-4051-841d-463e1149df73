package com.light.aiszzy.resultUploadFile.bo;

import com.light.contants.AISzzyConstants;
import lombok.Data;

/**
 * 处理结果的过程信息
 */
@Data
public class DealResultBo {

    /**
     * 是否处理失败
     */
    private boolean dealFailed = false;
    /**
     * 处理情况描述
     */
    private String dealDetail;
    /**
     * 是否二维码在第一页
     */
    private Boolean qrCodeFirstFlag;
    /**
     * 二维码信息
     * 类型#作业编号#页码
     * eg：1#0oxl3rg4#1
     */
    private String qrDeCode;
    /**
     * 二维码中类型
     */
    private String type;
    /**
     * 二维码中作业编号
     */
    private String homeworkCode;
    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 作业纸尺寸
     */
    private String paperPageSize;
    /**
     * 旋转后图片1的，文件服务器地址，有值说明已上传
     */
    private String destBufferedImageOneUrl;
    /**
     * 旋转后图片1信息，仅保留红色像素，文件服务器地址，有值说明已上传
     */
    private String destBufferedImageOneOnlyRedUrl;
    /**
     * 旋转后图片2的，文件服务器地址，有值说明已上传
     */
    private String destBufferedImageTwoUrl;
    /**
     * 旋转后图片2信息，仅保留红色像素，文件服务器地址，有值说明已上传
     */
    private String destBufferedImageTwoOnlyRedUrl;

}
