package com.light.aiszzy.homeworkResult.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 学生题目答案表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IHomeworkResultAnswerService extends IService<HomeworkResultAnswerDto> {

    List<HomeworkResultAnswerVo> getHomeworkResultAnswerListByCondition(HomeworkResultAnswerConditionBo condition);

    AjaxResult addHomeworkResultAnswer(HomeworkResultAnswerBo homeworkResultAnswerBo);

    AjaxResult updateHomeworkResultAnswer(HomeworkResultAnswerBo homeworkResultAnswerBo);

    HomeworkResultAnswerVo getDetail(String oid);

}

