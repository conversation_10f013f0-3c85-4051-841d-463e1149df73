package com.light.aiszzy.device.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import com.light.security.service.CurrentUserService;
import com.light.user.account.entity.vo.LoginAccountVo;
import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.device.entity.bo.DeviceConditionBo;
import com.light.aiszzy.device.entity.bo.DeviceBo;
import com.light.aiszzy.device.entity.vo.DeviceVo;
import com.light.aiszzy.device.service.IDeviceService;

import com.light.aiszzy.device.api.DeviceApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 设备表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@RestController
@Validated
@Api(value = "", tags = "设备表接口")
public class DeviceController implements DeviceApi {

    @Autowired
    private IDeviceService deviceService;
    @Resource
    private CurrentUserService currentUserService;

    public AjaxResult<PageInfo<DeviceVo>> getDevicePageListByCondition(@RequestBody DeviceConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),
            com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(), condition.getClass()));
        PageInfo<DeviceVo> pageInfo = new PageInfo<>(deviceService.getDeviceListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
            condition.getPageSize());
    }

    public AjaxResult<List<DeviceVo>> getDeviceListByCondition(@RequestBody DeviceConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(deviceService.getDeviceListByCondition(condition));
    }

    public AjaxResult addDevice(@Validated @RequestBody DeviceBo deviceBo) {
        return deviceService.addDevice(deviceBo);
    }

    public AjaxResult updateDevice(@Validated @RequestBody DeviceBo deviceBo) {
        if (null == deviceBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return deviceService.updateDevice(deviceBo);
    }

    public AjaxResult<DeviceVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(deviceService.getDetail(oid));
    }

    @Override
    public AjaxResult<DeviceVo> getDetailByHardwareCode(String hardwareCode) {
        return AjaxResult.success(deviceService.getDetailByHardwareCode(hardwareCode));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
        DeviceBo deviceBo = new DeviceBo();
        deviceBo.setOid(oid);
        deviceBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return deviceService.updateDevice(deviceBo);
    }

    @Override
    public AjaxResult<DeviceVo> getDetailByActivationCodeAndHardwareCode(String activationCode, String hardwareCode) {
        DeviceVo detailByActivationCode =
            deviceService.getDetailByActivationCodeAndHardwareCode(activationCode, hardwareCode);
        return AjaxResult.success(detailByActivationCode);
    }

    @Override
    public AjaxResult<DeviceVo> getDetailByActivationCodeAndHardwareCodeForActivated(String activationCode, String hardwareCode) {
        DeviceVo detailByActivationCode =
            deviceService.getDetailByActivationCodeAndHardwareCodeForActivated(activationCode, hardwareCode);
        return AjaxResult.success(detailByActivationCode);
    }

    @Override
    public AjaxResult activateDevice(@Validated @RequestBody DeviceBo deviceBo) {
        if (null == deviceBo.getActivationCode()) {
            return AjaxResult.fail("软件激活码不能为空");
        }
        return deviceService.activateDevice(deviceBo);
    }
}
