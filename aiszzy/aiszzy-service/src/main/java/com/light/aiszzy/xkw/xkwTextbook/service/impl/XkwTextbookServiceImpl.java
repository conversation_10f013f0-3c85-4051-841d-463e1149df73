package com.light.aiszzy.xkw.xkwTextbook.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookConditionBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.dto.XkwTextbookDto;
import com.light.aiszzy.xkw.xkwTextbook.entity.vo.XkwTextbookVo;
import com.light.aiszzy.xkw.xkwTextbook.mapper.XkwTextbookMapper;
import com.light.aiszzy.xkw.xkwTextbook.service.IXkwTextbookService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 教材接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Service
public class XkwTextbookServiceImpl extends ServiceImpl<XkwTextbookMapper, XkwTextbookDto> implements IXkwTextbookService {

	@Resource
	private XkwTextbookMapper xkwTextbookMapper;
	
    @Override
	public List<XkwTextbookVo> getXkwTextbookListByCondition(XkwTextbookConditionBo condition) {
        return xkwTextbookMapper.getXkwTextbookListByCondition(condition);
	}



}