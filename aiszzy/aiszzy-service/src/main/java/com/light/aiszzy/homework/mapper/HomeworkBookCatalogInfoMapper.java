package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogInfoDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogInfoVo;

/**
 * 作业本目录关联作业Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkBookCatalogInfoMapper extends BaseMapper<HomeworkBookCatalogInfoDto> {

	List<HomeworkBookCatalogInfoVo> getHomeworkBookCatalogInfoListByCondition(HomeworkBookCatalogInfoConditionBo condition);

}
