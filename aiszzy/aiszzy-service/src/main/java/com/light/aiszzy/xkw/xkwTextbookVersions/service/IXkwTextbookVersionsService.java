package com.light.aiszzy.xkw.xkwTextbookVersions.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsConditionBo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.dto.XkwTextbookVersionsDto;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo;

import java.util.List;
import java.util.Map;

/**
 * 教材版本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
public interface IXkwTextbookVersionsService extends IService<XkwTextbookVersionsDto> {

    List<XkwTextbookVersionsVo> getXkwTextbookVersionsListByCondition(XkwTextbookVersionsConditionBo condition);

}

