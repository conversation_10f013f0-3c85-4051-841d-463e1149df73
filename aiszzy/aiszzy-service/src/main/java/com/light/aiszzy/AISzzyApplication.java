package com.light.aiszzy;

import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;
import com.alibaba.cloud.nacos.registry.NacosRegistration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.server.ConfigurableWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;

@EnableFeignClients(basePackages = {"com.light"})
@EnableDiscoveryClient
@MapperScan("com.light.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.light"})
public class AISzzyApplication extends SpringBootServletInitializer {

    @Value("${server.port}")
    private String port;

    @Autowired
    private Environment env;

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(AISzzyApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(AISzzyApplication.class);
    }

    public String protocol = Thread.currentThread().getContextClassLoader().getResource("").getProtocol();

    @Bean
    public WebServerFactoryCustomizer<ConfigurableWebServerFactory> myCustomizer() {
        if (protocol.equals("jar")) {
            return null;
        }
        return factory -> factory.setPort(6180);
    }

    @Autowired
    private NacosRegistration registration;

    @Autowired
    private NacosAutoServiceRegistration nacosAutoServiceRegistration;

    /**
     * 将服务注册到nacos
     */
    @PostConstruct
    public void nacosServerRegister() {
        if (env.getProperty("spring.profiles.active").equals("dev")) {
            return;
        }
        if (!protocol.equals("jar") && registration != null) {
            try {
                Integer tomcatPort = new Integer(port);
                //设置端口号
                registration.setPort(tomcatPort);
                //将服务注册到nacos
                nacosAutoServiceRegistration.start();
            } catch (Exception e) {
            }
        }
    }

}
