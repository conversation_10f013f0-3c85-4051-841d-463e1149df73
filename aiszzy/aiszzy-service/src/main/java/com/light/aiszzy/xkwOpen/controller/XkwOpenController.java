package com.light.aiszzy.xkwOpen.controller;

import javax.annotation.Resource;

import com.light.aiszzy.xkwOpen.service.XkwApi;
import com.light.beans.OcrBo;
import com.light.beans.SimilarBo;
import com.light.beans.TikuBo;
import com.light.aiszzy.xkwOpen.service.XkwService;
import com.light.aiszzy.xkwOpen.api.XkwOpenApi;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 学科网open平台相关
 *
 * <AUTHOR>
 * @date 2025/3/28 14:15
 */
@RestController
@RequestMapping("/xkw-open")
@Slf4j
@Api(value = "学科网open接口")
public class XkwOpenController implements XkwOpenApi {
    @Resource
    private XkwService xkwService;

    @Resource
    private XkwApi xkwApi;

    /**
     * 题库搜索-海量版
     * 使用Optional和Stream API优化数据处理
     *
     * @param tikuBo 搜索参数
     * @return 搜索结果
     */
    @PostMapping("/tiku-search")
    public AjaxResult tikuSearch(@RequestBody TikuBo tikuBo) {
        return xkwService.tikuSearch(tikuBo);
    }

    /**
     * 相似题推荐
     * 使用Java8 Stream API优化并发处理和结果收集
     *
     * @return
     */
    @PostMapping("/similar-recommend")
    public AjaxResult similarRecommend(@RequestBody SimilarBo similarBo) {
        return xkwService.similarRecommend(similarBo);
    }

    @Override
    @PostMapping("/ocr")
    public AjaxResult ocr(@RequestBody OcrBo ocrBo) {
        String ocr = this.xkwApi.ocr(ocrBo.getImage_base64());
        return AjaxResult.success(ocr);
    }
}
