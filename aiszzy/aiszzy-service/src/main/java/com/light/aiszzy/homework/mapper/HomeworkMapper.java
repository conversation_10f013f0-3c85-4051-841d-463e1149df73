package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkDto;
import com.light.aiszzy.homework.entity.bo.HomeworkConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;

/**
 * 作业表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface HomeworkMapper extends BaseMapper<HomeworkDto> {

	List<HomeworkVo> getHomeworkListByCondition(HomeworkConditionBo condition);

	List<HomeworkVo> getHomeworkResultPageListByCondition(HomeworkConditionBo condition);

	List<HomeworkVo> listForBind(HomeworkConditionBo condition);

	List<HomeworkVo> listForTeacher(HomeworkConditionBo condition);

}
