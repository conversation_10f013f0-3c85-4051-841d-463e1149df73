package com.light.aiszzy.homeworkResult.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo;

/**
 * 扫描结构每页处理结果Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
public interface HomeworkPageResultMapper extends BaseMapper<HomeworkPageResultDto> {

	List<HomeworkPageResultVo> getHomeworkPageResultListByCondition(HomeworkPageResultConditionBo condition);

}
