package com.light.aiszzy.xkw.xkwTextbookVersions.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 教材版本
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-21 16:06:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xkw_textbook_versions")
public class XkwTextbookVersionsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 教材版本ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 	课程ID
	 */
	@TableField("course_id")
	private Long courseId;

	/**
	 * 启用年份
	 */
	@TableField("year")
	private Long year;

	/**
	 * 排序值
	 */
	@TableField("ordinal")
	private Long ordinal;

	/**
	 * 教材版本名称
	 */
	@TableField("name")
	private String name;

}
