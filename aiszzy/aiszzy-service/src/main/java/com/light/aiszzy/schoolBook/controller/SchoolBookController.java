package com.light.aiszzy.schoolBook.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.light.aiszzy.practiceBook.entity.vo.PracticeBookExportVo;
import com.light.aiszzy.schoolBook.entity.bo.BatchSchoolBookBo;
import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.schoolBook.entity.bo.SchoolBookConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookVo;
import com.light.aiszzy.schoolBook.entity.vo.PracticeBookWithSchoolVo;
import com.light.aiszzy.schoolBook.service.ISchoolBookService;

import com.light.aiszzy.schoolBook.api.SchoolBookApi;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookWithSchoolCountVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 教辅或作业本开通记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@RestController
@Validated
@Api(value = "", tags = "教辅或作业本开通记录表接口")
public class SchoolBookController implements SchoolBookApi {

    @Autowired
    private ISchoolBookService schoolBookService;

    public AjaxResult<PageInfo<SchoolBookVo>>
        getSchoolBookPageListByCondition(@RequestBody SchoolBookConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),
            com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(), condition.getClass()));
        PageInfo<SchoolBookVo> pageInfo = new PageInfo<>(schoolBookService.getSchoolBookListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
            condition.getPageSize());
    }

    public AjaxResult<List<SchoolBookVo>> getSchoolBookListByCondition(@RequestBody SchoolBookConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(schoolBookService.getSchoolBookListByCondition(condition));
    }

    public AjaxResult addSchoolBook(@Validated @RequestBody SchoolBookBo schoolBookBo) {
        return schoolBookService.addSchoolBook(schoolBookBo);
    }

    @Override
    public AjaxResult batchAddSchoolBook(BatchSchoolBookBo batchSchoolBookBo) {
        return schoolBookService.batchAddSchoolBook(batchSchoolBookBo);
    }

    public AjaxResult updateSchoolBook(@Validated @RequestBody SchoolBookBo schoolBookBo) {
        if (null == schoolBookBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return schoolBookService.updateSchoolBook(schoolBookBo);
    }

    public AjaxResult<SchoolBookVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(schoolBookService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
        SchoolBookBo schoolBookBo = new SchoolBookBo();
        schoolBookBo.setOid(oid);
        schoolBookBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return schoolBookService.updateSchoolBook(schoolBookBo);
    }

    public AjaxResult<PageInfo<PracticeBookWithSchoolCountVo>>
        getPracticeBookPageListWithSchoolCount(@RequestBody PracticeBookConditionBo condition) {
        return schoolBookService.getPracticeBookPageListWithSchoolCount(condition);
    }

    @Override
    public AjaxResult<PageInfo<PracticeBookWithSchoolVo>>
        getPracticeBookWithSchoolPageListByCondition(@RequestBody SchoolBookConditionBo condition) {
        return schoolBookService.getPracticeBookWithSchoolPageListByCondition(condition);
    }

    @Override
    public AjaxResult<List<PracticeBookExportVo>>
        exportPracticeBookWithSchoolCount(@RequestBody PracticeBookConditionBo condition) {
        return schoolBookService.exportPracticeBookWithSchoolCount(condition);
    }

}
