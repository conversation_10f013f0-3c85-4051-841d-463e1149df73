package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserConditionBo;
import com.light.aiszzy.homework.entity.dto.HomeworkBookUserDto;

import java.util.List;
import java.util.Map;

/**
 * 老师使用作业本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-18 14:20:30
 */
public interface IHomeworkBookUserService extends IService<HomeworkBookUserDto> {

    List<HomeworkBookVo> getHomeworkBookUserListByCondition(HomeworkBookUserConditionBo condition);

    AjaxResult addHomeworkBookUser(HomeworkBookUserBo homeworkBookUserBo);

    AjaxResult addBatchHomeworkBookUser(HomeworkBookUserBo homeworkBookUserBo);

    AjaxResult updateHomeworkBookUser(HomeworkBookUserBo homeworkBookUserBo);

    Map<String, Object> getDetail(String oid);

    AjaxResult deleteUse(String homeworkBookOid, String userCode, String orgCode);

}

