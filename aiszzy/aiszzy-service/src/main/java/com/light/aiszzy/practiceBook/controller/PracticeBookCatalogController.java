package com.light.aiszzy.practiceBook.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookService;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogExcel;
import io.swagger.annotations.Api;

import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookCatalogService;

import com.light.aiszzy.practiceBook.api.PracticeBookCatalogApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 教辅目录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "教辅目录表接口")
public class PracticeBookCatalogController implements PracticeBookCatalogApi {

    @Autowired
    private IPracticeBookCatalogService practiceBookCatalogService;

    @Resource
    private IPracticeBookService practiceBookService;

    public AjaxResult<PageInfo<PracticeBookCatalogVo>> getPracticeBookCatalogPageListByCondition(@RequestBody PracticeBookCatalogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<PracticeBookCatalogVo> pageInfo = new PageInfo<>(practiceBookCatalogService.getPracticeBookCatalogListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<PracticeBookCatalogVo>> getPracticeBookCatalogListByCondition(@RequestBody PracticeBookCatalogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(practiceBookCatalogService.getPracticeBookCatalogListByCondition(condition));
    }

    public AjaxResult addPracticeBookCatalog(@Validated @RequestBody PracticeBookCatalogBo practiceBookCatalogBo) {
        String practiceBookOid = practiceBookCatalogBo.getPracticeBookOid();
        if(StrUtil.isBlank(practiceBookOid)){
            return AjaxResult.fail("教辅 OID 不能为空");
        }
        return practiceBookCatalogService.addPracticeBookCatalog(practiceBookCatalogBo);
    }

    public AjaxResult updatePracticeBookCatalog(@Validated @RequestBody PracticeBookCatalogBo practiceBookCatalogBo) {
        if (null == practiceBookCatalogBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return practiceBookCatalogService.updatePracticeBookCatalog(practiceBookCatalogBo);
    }

    public AjaxResult<PracticeBookCatalogVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(practiceBookCatalogService.getDetail(oid));
    }

    @Override
    public AjaxResult delete(@RequestParam("oid") String oid) {
        boolean b = this.practiceBookCatalogService.deleteByOid(oid);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult<Void> saveExcelDataByPracticeBookOid(@PathVariable("practiceBookOid") String practiceBookOid, @RequestBody List<PracticeBookCatalogExcel> catalogExcels) {
        if(CollUtil.isEmpty(catalogExcels)){
            return AjaxResult.fail("目录数据不能为空");
        }

        PracticeBookVo practiceBookVo = this.practiceBookService.queryByOid(practiceBookOid);
        if(practiceBookVo == null) {
            return AjaxResult.fail("教辅不存在");
        }
        List<PracticeBookCatalogExcel> excelList = catalogExcels.stream()
                .filter(x -> StrUtil.isNotEmpty(x.getLevel()))
                .peek(x-> {
                    x.setOid(IdUtil.fastSimpleUUID());
                }).collect(Collectors.toList());

        // 初始化父级 OID
        this.initParentOid(excelList);

        List<PracticeBookCatalogBo> catalogBOList = excelList.stream().map(x -> x.toBookCatalogBo(practiceBookOid)).collect(Collectors.toList());
        return this.practiceBookCatalogService.delAndSaveBatchByPracticeBookOid(practiceBookOid, catalogBOList);
    }


    /**
     *  初始化父级 OID
     * @param dataList the data list
     */
    public void initParentOid(List<PracticeBookCatalogExcel> dataList) {
        // 用于保存最近的父级节点
        Map<Integer, MutablePair<String, String>> map = new HashMap<>();
        Map<String, Integer> orderNumMap = new HashMap<>();
        for (int i = 0; i < dataList.size(); i++) {
            PracticeBookCatalogExcel data = dataList.get(i);
            int level = Integer.parseInt(data.getLevel().trim()); // 转换 level 为整数
            if(level == 1){
                map.clear();
            }

            MutablePair<String, String> orDefault = map.getOrDefault(level - 1 , MutablePair.of("0", "0"));
            data.setParentOid(orDefault.left);
            String superiorsOids = orDefault.right.concat(",").concat(orDefault.left);
            if(level == 1){
                superiorsOids = "0";
            }
            data.setSuperiorsOids(superiorsOids);

            map.put(level, MutablePair.of(data.getOid(), data.getSuperiorsOids()));

            Integer orderNum = orderNumMap.getOrDefault(orDefault.left, 1);
            data.setOrderNum(orderNum);
            orderNumMap.put(orDefault.left, orderNum + 1);

        }

    }

    /**
     *  同级别按照集合顺序 重置 order num
     * @param practiceBookCatalogBo the practice book catalog bo {catalogOidList 目录Oid集合}
     * @return {@link AjaxResult }<{@link Void }>
     */
    @Override
    public AjaxResult<Void> resetOrderNumByParentOid(@RequestBody PracticeBookCatalogBo practiceBookCatalogBo) {
        List<String> catalogOidList = practiceBookCatalogBo.getCatalogOidList();
        if(CollUtil.isEmpty(catalogOidList)){
            return AjaxResult.fail("目录 OID 不能为空");
        }
        return this.practiceBookCatalogService.resetOrderNum( catalogOidList);
    }

    /**
     * 根据教辅 OID 获取教辅目录
     * @param practiceBookOid the practice book oid
     * @return {@link AjaxResult }<{@link List }<{@link PracticeBookCatalogVo }>>
     */
    @Override
    public AjaxResult<List<PracticeBookCatalogVo>> queryTreeByPracticeBookOid(@PathVariable("practiceBookOid") String practiceBookOid) {
        List<PracticeBookCatalogVo> treeList = this.practiceBookCatalogService.queryTreeListByPracticeBookOid(practiceBookOid);
        return AjaxResult.success(treeList);
    }
}
