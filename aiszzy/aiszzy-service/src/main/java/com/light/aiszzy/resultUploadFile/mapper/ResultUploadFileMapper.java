package com.light.aiszzy.resultUploadFile.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.resultUploadFile.entity.dto.ResultUploadFileDto;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileConditionBo;
import com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo;

/**
 * 扫描上传图片Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
public interface ResultUploadFileMapper extends BaseMapper<ResultUploadFileDto> {

	List<ResultUploadFileVo> getResultUploadFileListByCondition(ResultUploadFileConditionBo condition);

}
