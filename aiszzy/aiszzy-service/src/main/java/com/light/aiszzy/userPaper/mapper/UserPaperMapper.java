package com.light.aiszzy.userPaper.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.userPaper.entity.dto.UserPaperDto;
import com.light.aiszzy.userPaper.entity.bo.UserPaperConditionBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperVo;

/**
 * 用户上传试卷Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface UserPaperMapper extends BaseMapper<UserPaperDto> {

	List<UserPaperVo> getUserPaperListByCondition(UserPaperConditionBo condition);

}
