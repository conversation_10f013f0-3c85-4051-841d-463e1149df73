package com.light.aiszzy.userPaper.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.userPaper.entity.dto.UserPaperPageDto;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageConditionBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo;

/**
 * 用户上传每页图片表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface UserPaperPageMapper extends BaseMapper<UserPaperPageDto> {

	List<UserPaperPageVo> getUserPaperPageListByCondition(UserPaperPageConditionBo condition);

	List<UserPaperPageVo> selectByUserPaperOid(String userPaperOid);
}
