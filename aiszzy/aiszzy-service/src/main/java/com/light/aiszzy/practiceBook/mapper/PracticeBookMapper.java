package com.light.aiszzy.practiceBook.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;

/**
 * 教辅信息表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface PracticeBookMapper extends BaseMapper<PracticeBookDto> {

	List<PracticeBookVo> getPracticeBookListByCondition(PracticeBookConditionBo condition);

}
