package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkPageDto;
import com.light.aiszzy.homework.entity.bo.HomeworkPageConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkPageBo;
import com.light.aiszzy.homework.entity.vo.HomeworkPageVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 作业每页题目坐标信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
public interface IHomeworkPageService extends IService<HomeworkPageDto> {

    List<HomeworkPageVo> getHomeworkPageListByCondition(HomeworkPageConditionBo condition);

	AjaxResult addHomeworkPage(HomeworkPageBo homeworkPageBo);

	AjaxResult updateHomeworkPage(HomeworkPageBo homeworkPageBo);

    HomeworkPageVo getDetail(String oid);

}

