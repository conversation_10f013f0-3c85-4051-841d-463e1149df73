package com.light.aiszzy.userPaper.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.light.aiszzy.userPaper.service.IUserPaperPageService;
import com.light.core.exception.WarningException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.userPaper.entity.dto.UserPaperDto;
import com.light.aiszzy.userPaper.entity.bo.UserPaperConditionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperVo;
import com.light.aiszzy.userPaper.service.IUserPaperService;
import com.light.aiszzy.userPaper.mapper.UserPaperMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户上传试卷接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class UserPaperServiceImpl extends ServiceImpl<UserPaperMapper, UserPaperDto> implements IUserPaperService {

	@Resource
	private UserPaperMapper userPaperMapper;

	@Resource
	private IUserPaperPageService userPaperPageService;
	
    @Override
	public List<UserPaperVo> getUserPaperListByCondition(UserPaperConditionBo condition) {
        return userPaperMapper.getUserPaperListByCondition(condition);
	}

	@Override
	public AjaxResult addUserPaper(UserPaperBo userPaperBo) {
		UserPaperDto userPaper = new UserPaperDto();
		BeanUtils.copyProperties(userPaperBo, userPaper);
		userPaper.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (StrUtil.isEmpty(userPaperBo.getOid())) {
			userPaper.setOid(IdUtil.simpleUUID());
        }
		if(save(userPaper)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateUserPaper(UserPaperBo userPaperBo) {
		LambdaQueryWrapper<UserPaperDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(UserPaperDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(UserPaperDto::getOid, userPaperBo.getOid());
		UserPaperDto userPaper = getOne(lqw);
		Long id = userPaper.getId();
		if(userPaper == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(userPaperBo, userPaper);
		userPaper.setId(id);
		if(updateById(userPaper)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public UserPaperVo getDetail(String oid) {
		LambdaQueryWrapper<UserPaperDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(UserPaperDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(UserPaperDto::getOid, oid);
		UserPaperDto userPaper = getOne(lqw);
	    UserPaperVo userPaperVo = new UserPaperVo();
		if(userPaper != null){
			BeanUtils.copyProperties(userPaper, userPaperVo);
		}
		return userPaperVo;
	}

	@Override
	public boolean addTotalQuestionNumByOid(String userPaperOid, long addQuestionNum) {
		UpdateWrapper<UserPaperDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperDto::getOid, userPaperOid);
		updateWrapper.lambda().setSql("question_num = ifnull(question_num, 0) + " + addQuestionNum);
		return this.update(updateWrapper);
	}

	@Override
	public boolean addFinishQuestionNumbByOid(String userPaperOid, int addFinishedNum) {
		UpdateWrapper<UserPaperDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperDto::getOid, userPaperOid);
		updateWrapper.lambda().setSql("finish_question_num = if(ifnull(finish_question_num,0) + " + addFinishedNum + " > question_num,question_num,ifnull(finish_question_num,0) + "+ addFinishedNum +")");
		return this.update(updateWrapper);
	}

	@Override
	public boolean updatePublishByOid(String oid, Integer isPublish) {
		if(StatusEnum.YES.getCode().equals(isPublish)){
			UserPaperVo userPaperVo = this.queryByOid(oid);
            if (!Objects.equals(userPaperVo.getQuestionNum(), userPaperVo.getFinishQuestionNum())) {
                throw new WarningException("未标注完成，无法进行发布");
            }
		}
		UpdateWrapper<UserPaperDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperDto::getOid, oid);
		updateWrapper.lambda().set(UserPaperDto::getIsPublish, isPublish);
		updateWrapper.lambda().set(StatusEnum.YES.getCode().equals(isPublish), UserPaperDto::getPublishTime, new Date());
		return this.update(updateWrapper);
	}


	public UserPaperVo queryByOid(String oid) {
		QueryWrapper<UserPaperDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(UserPaperDto::getOid, oid);
		UserPaperDto userPaper = getOne(queryWrapper);
		if(userPaper == null){
			return null;
		}
		return BeanUtil.toBean(userPaper, UserPaperVo.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByOidAndUserOid(String oid, String userOid) {
		UserPaperVo userPaperVo = this.queryByOidAndUserOid(oid, userOid);
		if(userPaperVo == null){
			throw new WarningException("校本不存在");
		}
        if (userPaperVo.getIsPublish().equals(StatusEnum.YES.getCode())) {
            throw new WarningException("已发布无法进行删除");
        }

		// 删除数据

		return this.deleteByOid(oid);
	}

	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByOid(String oid) {
		UpdateWrapper<UserPaperDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperDto::getOid, oid);
		updateWrapper.lambda().set(UserPaperDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		boolean update = this.update(updateWrapper);

		// 删除页码信息
		this.userPaperPageService.deleteByUserPaperOid(oid);
		return update;
	}



	public UserPaperVo queryByOidAndUserOid(String oid, String userOid) {
		QueryWrapper<UserPaperDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(UserPaperDto::getOid, oid);
		queryWrapper.lambda().eq(UserPaperDto::getUserOid, userOid);
		queryWrapper.lambda().eq(UserPaperDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		UserPaperDto userPaper = getOne(queryWrapper);
		if(userPaper == null){
			return null;
		}
		return BeanUtil.toBean(userPaper, UserPaperVo.class);
	}
}