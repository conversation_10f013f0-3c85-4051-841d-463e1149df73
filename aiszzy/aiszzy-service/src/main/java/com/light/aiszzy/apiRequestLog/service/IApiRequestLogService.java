package com.light.aiszzy.apiRequestLog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.apiRequestLog.entity.dto.ApiRequestLogDto;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogConditionBo;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogBo;
import com.light.aiszzy.apiRequestLog.entity.vo.ApiRequestLogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 第三方请求日志表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IApiRequestLogService extends IService<ApiRequestLogDto> {

    List<ApiRequestLogVo> getApiRequestLogListByCondition(ApiRequestLogConditionBo condition);

	AjaxResult addApiRequestLog(ApiRequestLogBo apiRequestLogBo);

	AjaxResult updateApiRequestLog(ApiRequestLogBo apiRequestLogBo);

    ApiRequestLogVo getDetail(String oid);

}

