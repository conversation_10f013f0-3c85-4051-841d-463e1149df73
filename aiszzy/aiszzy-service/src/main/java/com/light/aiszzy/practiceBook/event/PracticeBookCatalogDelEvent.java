package com.light.aiszzy.practiceBook.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class PracticeBookCatalogDelEvent extends ApplicationEvent {

    private final String practiceBookOid;

    private final String catalogOid;

    public PracticeBookCatalogDelEvent(Object source, String practiceBookOid, String catalogOid) {
        super(source);
        this.practiceBookOid = practiceBookOid;
        this.catalogOid = catalogOid;
    }
}
