package com.light.aiszzy.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.statistics.entity.dto.SchoolGradeQuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.SchoolGradeQuestionStatisticsVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 作业年级题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface ISchoolGradeQuestionStatisticsService extends IService<SchoolGradeQuestionStatisticsDto> {

    List<SchoolGradeQuestionStatisticsVo> getSchoolGradeQuestionStatisticsListByCondition(SchoolGradeQuestionStatisticsConditionBo condition);

	AjaxResult addSchoolGradeQuestionStatistics(SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo);

	AjaxResult updateSchoolGradeQuestionStatistics(SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo);

    SchoolGradeQuestionStatisticsVo getDetail(String oid);

}

