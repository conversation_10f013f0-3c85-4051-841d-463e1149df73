package com.light.aiszzy.device.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.device.entity.dto.DeviceBindRecordDto;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordConditionBo;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordBo;
import com.light.aiszzy.device.entity.vo.DeviceBindRecordVo;
import com.light.aiszzy.device.service.IDeviceBindRecordService;
import com.light.aiszzy.device.mapper.DeviceBindRecordMapper;
import com.light.core.entity.AjaxResult;
/**
 * 设备表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@Service
public class DeviceBindRecordServiceImpl extends ServiceImpl<DeviceBindRecordMapper, DeviceBindRecordDto> implements IDeviceBindRecordService {

	@Resource
	private DeviceBindRecordMapper deviceBindRecordMapper;
	
    @Override
	public List<DeviceBindRecordVo> getDeviceBindRecordListByCondition(DeviceBindRecordConditionBo condition) {
        return deviceBindRecordMapper.getDeviceBindRecordListByCondition(condition);
	}

	@Override
	public AjaxResult addDeviceBindRecord(DeviceBindRecordBo deviceBindRecordBo) {
		DeviceBindRecordDto deviceBindRecord = new DeviceBindRecordDto();
		BeanUtils.copyProperties(deviceBindRecordBo, deviceBindRecord);
		deviceBindRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
		deviceBindRecord.setOid(IdUtil.simpleUUID());
		if(save(deviceBindRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateDeviceBindRecord(DeviceBindRecordBo deviceBindRecordBo) {
		LambdaQueryWrapper<DeviceBindRecordDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(DeviceBindRecordDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(DeviceBindRecordDto::getOid, deviceBindRecordBo.getOid());
		DeviceBindRecordDto deviceBindRecord = getOne(lqw);
		Long id = deviceBindRecord.getId();
		if(deviceBindRecord == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(deviceBindRecordBo, deviceBindRecord);
		deviceBindRecord.setId(id);
		if(updateById(deviceBindRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public DeviceBindRecordVo getDetail(String oid) {
		LambdaQueryWrapper<DeviceBindRecordDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(DeviceBindRecordDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(DeviceBindRecordDto::getOid, oid);
		DeviceBindRecordDto deviceBindRecord = getOne(lqw);
	    DeviceBindRecordVo deviceBindRecordVo = new DeviceBindRecordVo();
		if(deviceBindRecord != null){
			BeanUtils.copyProperties(deviceBindRecord, deviceBindRecordVo);
		}
		return deviceBindRecordVo;
	}

    @Override
    public DeviceBindRecordVo getDetailByCondition(DeviceBindRecordConditionBo condition) {
        return deviceBindRecordMapper.getDetailByCondition(condition);
    }
}