package com.light.aiszzy.homeworkResult.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultErrorDetailDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailConditionBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultErrorDetailVo;

/**
 * 校本作业结果详情表(错误试题)Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface HomeworkResultErrorDetailMapper extends BaseMapper<HomeworkResultErrorDetailDto> {

	List<HomeworkResultErrorDetailVo> getHomeworkResultErrorDetailListByCondition(HomeworkResultErrorDetailConditionBo condition);

}
