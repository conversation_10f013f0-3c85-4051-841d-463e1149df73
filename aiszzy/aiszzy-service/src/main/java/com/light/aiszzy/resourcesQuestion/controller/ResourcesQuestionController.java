package com.light.aiszzy.resourcesQuestion.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionConditionBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionBo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionVo;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionService;

import com.light.aiszzy.resourcesQuestion.api.ResourcesQuestionApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 资源库题目表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "资源库题目表接口")
public class ResourcesQuestionController implements ResourcesQuestionApi {

    @Autowired
    private IResourcesQuestionService resourcesQuestionService;

    public AjaxResult<PageInfo<ResourcesQuestionVo>> getResourcesQuestionPageListByCondition(@RequestBody ResourcesQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<ResourcesQuestionVo> pageInfo = new PageInfo<>(resourcesQuestionService.getResourcesQuestionListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<ResourcesQuestionVo>> getResourcesQuestionListByCondition(@RequestBody ResourcesQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(resourcesQuestionService.getResourcesQuestionListByCondition(condition));
    }

    public AjaxResult addResourcesQuestion(@Validated @RequestBody ResourcesQuestionBo resourcesQuestionBo) {
        return resourcesQuestionService.addResourcesQuestion(resourcesQuestionBo);
    }

    public AjaxResult updateResourcesQuestion(@Validated @RequestBody ResourcesQuestionBo resourcesQuestionBo) {
        if (null == resourcesQuestionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return resourcesQuestionService.updateResourcesQuestion(resourcesQuestionBo);
    }

    public AjaxResult<ResourcesQuestionVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(resourcesQuestionService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            ResourcesQuestionBo resourcesQuestionBo = new ResourcesQuestionBo();
            resourcesQuestionBo.setOid(oid);
            resourcesQuestionBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return resourcesQuestionService.updateResourcesQuestion(resourcesQuestionBo);
    }
}
