package com.light.aiszzy.schoolBook.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.schoolBook.entity.dto.SchoolBookDto;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookConditionBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookVo;
import com.light.aiszzy.schoolBook.entity.vo.PracticeBookWithSchoolVo;
import org.apache.ibatis.annotations.Param;

/**
 * 教辅或作业本开通记录表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface SchoolBookMapper extends BaseMapper<SchoolBookDto> {

	List<SchoolBookVo> getSchoolBookListByCondition(SchoolBookConditionBo condition);

	/**
	 * 批量查询教辅的总开通学校数量
	 * @param practiceBookOids 教辅OID集合
	 * @return 查询结果列表，每个元素包含practiceBookOid和schoolCount字段
	 */
	List<Map<String, Object>> getTotalSchoolCountByPracticeBookOids(@Param("practiceBookOids") List<String> practiceBookOids);

	/**
	 * 批量查询教辅的活跃学校数量（有效期内）
	 * @param practiceBookOids 教辅OID集合
	 * @return 查询结果列表，每个元素包含practiceBookOid和schoolCount字段
	 */
	List<Map<String, Object>> getActiveSchoolCountByPracticeBookOids(@Param("practiceBookOids") List<String> practiceBookOids);

	/**
	 * 查询教辅列表与学校开通信息关联
	 * @param condition 查询条件，支持org_code和book_oid等条件
	 * @return 教辅与学校关联信息列表
	 */
	List<PracticeBookWithSchoolVo> getPracticeBookWithSchoolListByCondition(SchoolBookConditionBo condition);

}
