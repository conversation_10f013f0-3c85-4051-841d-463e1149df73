package com.light.aiszzy.userPaper.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageConditionBo;
import com.light.aiszzy.userPaper.entity.dto.UserPaperPageDto;
import com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo;
import com.light.aiszzy.userPaper.mapper.UserPaperPageMapper;
import com.light.aiszzy.userPaper.service.IUserPaperPageService;
import com.light.aiszzy.userPaper.service.IUserPaperQuestionService;
import com.light.aiszzy.userPaper.service.IUserPaperService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.WarningException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户上传每页图片表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class UserPaperPageServiceImpl extends ServiceImpl<UserPaperPageMapper, UserPaperPageDto> implements IUserPaperPageService {

	@Resource
	private UserPaperPageMapper userPaperPageMapper;

	@Resource
	private IUserPaperQuestionService userPaperQuestionService;

	@Resource
	private IUserPaperService userPaperService;

    @Override
	public List<UserPaperPageVo> getUserPaperPageListByCondition(UserPaperPageConditionBo condition) {
        return userPaperPageMapper.getUserPaperPageListByCondition(condition);
	}

	@Override
	public AjaxResult addUserPaperPage(UserPaperPageBo userPaperPageBo) {
		UserPaperPageDto userPaperPage = new UserPaperPageDto();
		BeanUtils.copyProperties(userPaperPageBo, userPaperPage);
		userPaperPage.setIsDelete(StatusEnum.NOTDELETE.getCode());
		userPaperPage.setOid(IdUtil.simpleUUID());
		if(save(userPaperPage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateUserPaperPage(UserPaperPageBo userPaperPageBo) {
		LambdaQueryWrapper<UserPaperPageDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(UserPaperPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(UserPaperPageDto::getOid, userPaperPageBo.getOid());
		UserPaperPageDto userPaperPage = getOne(lqw);
		Long id = userPaperPage.getId();
		if(userPaperPage == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(userPaperPageBo, userPaperPage);
		userPaperPage.setId(id);
		if(updateById(userPaperPage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public UserPaperPageVo getDetail(String oid) {
		LambdaQueryWrapper<UserPaperPageDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(UserPaperPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(UserPaperPageDto::getOid, oid);
		UserPaperPageDto userPaperPage = getOne(lqw);

		if(userPaperPage != null){
			return BeanUtil.copyProperties(userPaperPage, UserPaperPageVo.class);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByUserPaperOid(String userPaperOid) {
		UpdateWrapper<UserPaperPageDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperPageDto::getUserPaperOid, userPaperOid);
		updateWrapper.lambda().set(UserPaperPageDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		boolean update = this.update(updateWrapper);

		// 删除所有题目数据
		this.userPaperQuestionService.deleteByUserPaperOid(userPaperOid);

		return update;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult saveBatchByUserPaperOid(String userPaperOid, List<UserPaperPageBo> list) {

		// 保存分页数据
		List<UserPaperPageDto> pageDtoList = list.stream().map(x -> {
			x.setOid(IdUtil.simpleUUID());
			UserPaperPageDto userPaperPageDto = BeanUtil.toBean(x, UserPaperPageDto.class);
			userPaperPageDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
			userPaperPageDto.setUserPaperOid(userPaperOid);
			return userPaperPageDto;
		}).collect(Collectors.toList());
		this.saveBatch(pageDtoList);

		long questionNum = pageDtoList.stream().filter(x -> x.getAnalysisQuestionNum() != null).mapToLong(UserPaperPageDto::getAnalysisQuestionNum).sum();
		// 保存题目信息数据
		list.stream().filter(x-> CollUtil.isNotEmpty(x.getUserPaperQuestionList())).forEach(x-> {
			x.getUserPaperQuestionList().forEach(q-> {
				q.setPageNum(x.getPageNo());
				q.setUserPaperOid(userPaperOid);
			});
			this.userPaperQuestionService.delAndSaveBatchByUserPaperPageOid(x.getOid(), x.getUserPaperQuestionList());
		});

		// 增加题目数量
		this.userPaperService.addTotalQuestionNumByOid(userPaperOid, questionNum);
		return AjaxResult.success();
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addQuestionNumByOid(String userPaperPageOid, int addQuestionNum) {

		UserPaperPageVo detail = Optional.ofNullable(this.getDetail(userPaperPageOid)).orElseThrow(()-> new WarningException("页码数据不存在"));
		String userPaperOid = detail.getUserPaperOid();

		// 增加题目数量
		UpdateWrapper<UserPaperPageDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperPageDto::getOid, userPaperPageOid);
		updateWrapper.lambda().setSql("question_num = if(ifnull(question_num,0) + " + addQuestionNum + " < 0,0,ifnull(question_num,0) + "+ addQuestionNum +")");
		boolean update = this.update(updateWrapper);

		this.userPaperService.addTotalQuestionNumByOid(userPaperOid, addQuestionNum);

		return update;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addFinishQuestionNumByOid(String userPaperPageOid, int addFinishedNum) {

		UserPaperPageVo detail = this.getDetail(userPaperPageOid);
		if(detail== null) {
			throw new WarningException("校本页码数据不存在");
		}


		// 增加题目数量
		UpdateWrapper<UserPaperPageDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(UserPaperPageDto::getOid, userPaperPageOid);
		updateWrapper.lambda().setSql("finish_question_num = if(ifnull(finish_question_num,0) + " + addFinishedNum + " > question_num,question_num,ifnull(finish_question_num,0) + "+ addFinishedNum +")");

		// 更新页 & 校本
		return this.update(updateWrapper) && this.userPaperService.addFinishQuestionNumbByOid(detail.getUserPaperOid(), addFinishedNum);
	}


	@Override
	public List<UserPaperPageVo> queryByUserPaperOid(String userPaperOid) {

		return this.baseMapper.selectByUserPaperOid(userPaperOid);
	}
}