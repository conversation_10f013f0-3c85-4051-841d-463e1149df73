package com.light.aiszzy.xkw.xkwTextbook.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 教材
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-21 16:06:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xkw_textbook")
public class XkwTextbookDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 	课程ID
	 */
	@TableField("course_id")
	private Long courseId;

	/**
	 * 排序值
	 */
	@TableField("ordinal")
	private Long ordinal;

	/**
	 * 册别
	 */
	@TableField("volume")
	private String volume;

	/**
	 * 教材版本ID
	 */
	@TableField("version_id")
	private Long versionId;

	/**
	 * 年级ID
	 */
	@TableField("grade_id")
	private Long gradeId;

	/**
	 * 学期,可用值:LAST,NEXT,ALL
	 */
	@TableField("term")
	private String term;

	/**
	 * 教材名称
	 */
	@TableField("name")
	private String name;

}
