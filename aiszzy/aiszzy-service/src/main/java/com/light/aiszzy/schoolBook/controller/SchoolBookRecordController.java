package com.light.aiszzy.schoolBook.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookRecordVo;
import com.light.aiszzy.schoolBook.service.ISchoolBookRecordService;

import com.light.aiszzy.schoolBook.api.SchoolBookRecordApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 教辅或作业本开通记录详情表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@RestController
@Validated
@Api(value = "", tags = "教辅或作业本开通记录详情表接口")
public class SchoolBookRecordController implements SchoolBookRecordApi {

    @Autowired
    private ISchoolBookRecordService schoolBookRecordService;

    public AjaxResult<PageInfo<SchoolBookRecordVo>> getSchoolBookRecordPageListByCondition(@RequestBody SchoolBookRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<SchoolBookRecordVo> pageInfo = new PageInfo<>(schoolBookRecordService.getSchoolBookRecordListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<SchoolBookRecordVo>> getSchoolBookRecordListByCondition(@RequestBody SchoolBookRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(schoolBookRecordService.getSchoolBookRecordListByCondition(condition));
    }

    public AjaxResult addSchoolBookRecord(@Validated @RequestBody SchoolBookRecordBo schoolBookRecordBo) {
        return schoolBookRecordService.addSchoolBookRecord(schoolBookRecordBo);
    }

    public AjaxResult updateSchoolBookRecord(@Validated @RequestBody SchoolBookRecordBo schoolBookRecordBo) {
        if (null == schoolBookRecordBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return schoolBookRecordService.updateSchoolBookRecord(schoolBookRecordBo);
    }

    public AjaxResult<SchoolBookRecordVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(schoolBookRecordService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            SchoolBookRecordBo schoolBookRecordBo = new SchoolBookRecordBo();
            schoolBookRecordBo.setOid(oid);
            schoolBookRecordBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return schoolBookRecordService.updateSchoolBookRecord(schoolBookRecordBo);
    }
}
