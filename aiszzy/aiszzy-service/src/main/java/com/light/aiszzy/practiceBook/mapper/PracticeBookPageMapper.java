package com.light.aiszzy.practiceBook.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookPageDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo;

/**
 * 教辅目录每页图片记录表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface PracticeBookPageMapper extends BaseMapper<PracticeBookPageDto> {

	List<PracticeBookPageVo> getPracticeBookPageListByCondition(PracticeBookPageConditionBo condition);

}
