package com.light.aiszzy.device.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("device_bind_record")
public class DeviceBindRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 设备oid
	 */
	@TableField("device_oid")
	private String deviceOid;

	/**
	 * 当前绑定学校
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 绑定学校所在地区
	 */
	@TableField("org_name")
	private String orgName;

	/**
	 * 学校区域
	 */
	@TableField("org_area_name")
	private String orgAreaName;

	/**
	 * 学校区域CODE
	 */
	@TableField("org_area_code")
	private String orgAreaCode;

	/**
	 * 学校市区
	 */
	@TableField("org_city_name")
	private String orgCityName;

	/**
	 * 学校市区CODE
	 */
	@TableField("org_city_code")
	private String orgCityCode;

	/**
	 * 学校省
	 */
	@TableField("org_province_name")
	private String orgProvinceName;

	/**
	 * 学校省CODE
	 */
	@TableField("org_province_code")
	private String orgProvinceCode;

	/**
	 * 设备状态  0禁用 1闲置 2正常
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 设备MAC地址，设备绑定的时候更新
	 */
	@TableField("device_mac_address")
	private String deviceMacAddress;

	/**
	 * 客户端版本
	 */
	@TableField("client_version")
	private String clientVersion;

	/**
	 * 扩展信息，备用
	 */
	@TableField("extend_info")
	private String extendInfo;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建者真实姓名
	 */
	@TableField("create_by_real_name")
	private String createByRealName;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
