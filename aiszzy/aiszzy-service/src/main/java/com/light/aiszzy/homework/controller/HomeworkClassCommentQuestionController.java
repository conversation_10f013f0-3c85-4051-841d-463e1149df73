package com.light.aiszzy.homework.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassCommentQuestionVo;
import com.light.aiszzy.homework.service.IHomeworkClassCommentQuestionService;

import com.light.aiszzy.homework.api.HomeworkClassCommentQuestionApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 班级作业讲评题目
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@RestController
@Validated
@Api(value = "", tags = "班级作业讲评题目接口")
public class HomeworkClassCommentQuestionController implements HomeworkClassCommentQuestionApi {

    @Autowired
    private IHomeworkClassCommentQuestionService homeworkClassCommentQuestionService;

    public AjaxResult<PageInfo<HomeworkClassCommentQuestionVo>> getHomeworkClassCommentQuestionPageListByCondition(@RequestBody HomeworkClassCommentQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<HomeworkClassCommentQuestionVo> pageInfo = new PageInfo<>(homeworkClassCommentQuestionService.getHomeworkClassCommentQuestionListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkClassCommentQuestionVo>> getHomeworkClassCommentQuestionListByCondition(@RequestBody HomeworkClassCommentQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkClassCommentQuestionService.getHomeworkClassCommentQuestionListByCondition(condition));
    }

    public AjaxResult addHomeworkClassCommentQuestion(@Validated @RequestBody HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo) {
        return homeworkClassCommentQuestionService.addHomeworkClassCommentQuestion(homeworkClassCommentQuestionBo);
    }

    public AjaxResult updateHomeworkClassCommentQuestion(@Validated @RequestBody HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo) {
        if (null == homeworkClassCommentQuestionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkClassCommentQuestionService.updateHomeworkClassCommentQuestion(homeworkClassCommentQuestionBo);
    }

    public AjaxResult<HomeworkClassCommentQuestionVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkClassCommentQuestionService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo = new HomeworkClassCommentQuestionBo();
            homeworkClassCommentQuestionBo.setOid(oid);
            homeworkClassCommentQuestionBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkClassCommentQuestionService.updateHomeworkClassCommentQuestion(homeworkClassCommentQuestionBo);
    }
}
