package com.light.aiszzy.practiceBook.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookReviewDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookReviewVo;

/**
 * 教辅信息审核Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface PracticeBookReviewMapper extends BaseMapper<PracticeBookReviewDto> {

	List<PracticeBookReviewVo> getPracticeBookReviewListByCondition(PracticeBookReviewConditionBo condition);

}
