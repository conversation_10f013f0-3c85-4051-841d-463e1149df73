package com.light.aiszzy.resultUploadFile.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileConditionBo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileBo;
import com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo;
import com.light.aiszzy.resultUploadFile.service.IResultUploadFileService;

import com.light.aiszzy.resultUploadFile.api.ResultUploadFileApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 扫描上传图片
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
@RestController
@Validated
@Api(value = "", tags = "扫描上传图片接口")
public class ResultUploadFileController implements ResultUploadFileApi {

    @Autowired
    private IResultUploadFileService resultUploadFileService;

    public AjaxResult<PageInfo<ResultUploadFileVo>> getResultUploadFilePageListByCondition(@RequestBody ResultUploadFileConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ResultUploadFileVo> pageInfo = new PageInfo<>(resultUploadFileService.getResultUploadFileListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<ResultUploadFileVo>> getResultUploadFileListByCondition(@RequestBody ResultUploadFileConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(resultUploadFileService.getResultUploadFileListByCondition(condition));
    }

    public AjaxResult addResultUploadFile(@Validated @RequestBody ResultUploadFileBo resultUploadFileBo) {
        return resultUploadFileService.addResultUploadFile(resultUploadFileBo);
    }

    public AjaxResult updateResultUploadFile(@Validated @RequestBody ResultUploadFileBo resultUploadFileBo) {
        return resultUploadFileService.updateResultUploadFile(resultUploadFileBo);
    }

    public AjaxResult<ResultUploadFileVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(resultUploadFileService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            ResultUploadFileBo resultUploadFileBo = new ResultUploadFileBo();
            resultUploadFileBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return resultUploadFileService.updateResultUploadFile(resultUploadFileBo);
    }
}
