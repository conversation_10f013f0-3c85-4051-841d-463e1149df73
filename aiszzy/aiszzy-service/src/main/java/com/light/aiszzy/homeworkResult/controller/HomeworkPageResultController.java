package com.light.aiszzy.homeworkResult.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkPageResultService;

import com.light.aiszzy.homeworkResult.api.HomeworkPageResultApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 扫描结构每页处理结果
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@RestController
@Validated
@Api(value = "", tags = "扫描结构每页处理结果")
public class HomeworkPageResultController implements HomeworkPageResultApi {

    @Autowired
    private IHomeworkPageResultService homeworkPageResultService;

    public AjaxResult<PageInfo<HomeworkPageResultVo>> getHomeworkPageResultPageListByCondition(@RequestBody HomeworkPageResultConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<HomeworkPageResultVo> pageInfo = new PageInfo<>(homeworkPageResultService.getHomeworkPageResultListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkPageResultVo>> getHomeworkPageResultListByCondition(@RequestBody HomeworkPageResultConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkPageResultService.getHomeworkPageResultListByCondition(condition));
    }

    @Override
    public AjaxResult dealNoStudent(HomeworkPageResultBo bo) {
        return homeworkPageResultService.dealNoStudent(bo);
    }

    @Override
    public AjaxResult dealRepeat(HomeworkPageResultBo bo) {
        return homeworkPageResultService.dealRepeat(bo);
    }

    public AjaxResult addHomeworkPageResult(@Validated @RequestBody HomeworkPageResultBo homeworkPageResultBo) {
        return homeworkPageResultService.addHomeworkPageResult(homeworkPageResultBo);
    }

    public AjaxResult updateHomeworkPageResult(@Validated @RequestBody HomeworkPageResultBo homeworkPageResultBo) {
        if (null == homeworkPageResultBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkPageResultService.updateHomeworkPageResult(homeworkPageResultBo);
    }

    public AjaxResult<HomeworkPageResultVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkPageResultService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
        HomeworkPageResultBo homeworkPageResultBo = new HomeworkPageResultBo();
        homeworkPageResultBo.setOid(oid);
        homeworkPageResultBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkPageResultService.updateHomeworkPageResult(homeworkPageResultBo);
    }
}
