package com.light.aiszzy.userPaper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.userPaper.entity.dto.UserPaperPageDto;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageConditionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 用户上传每页图片表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IUserPaperPageService extends IService<UserPaperPageDto> {

    List<UserPaperPageVo> getUserPaperPageListByCondition(UserPaperPageConditionBo condition);

	AjaxResult addUserPaperPage(UserPaperPageBo userPaperPageBo);

	AjaxResult updateUserPaperPage(UserPaperPageBo userPaperPageBo);

    UserPaperPageVo getDetail(String oid);

    /**
     *  根据用户 OID
     * @param userPaperOid the use paper oid 校本 OID
     * @return boolean
     */
    boolean deleteByUserPaperOid(String userPaperOid);

    AjaxResult saveBatchByUserPaperOid(String userPaperOid, List<UserPaperPageBo> list);

    /**
     *  添加题目数量
     * @param userPaperPageOid the user paper page oid  校本题目页码 OID
     * @param addQuestionNum the add question num 添加题目数量
     * @return boolean
     */
    boolean addQuestionNumByOid(String userPaperPageOid, int addQuestionNum);

    /**
     * 增加已完成框题数量
     * @param userPaperPageOid the user paper page oid  校本题目页码 OID
     * @param addFinishedNum the add question num 添加新框题目数量
     * @return boolean
     */
    boolean addFinishQuestionNumByOid(String userPaperPageOid, int addFinishedNum);

    /**
     *  查询校本页码数据
     * @param userPaperOid the user paper oid 校本 OID
     * @return {@link List }<{@link UserPaperPageVo }>
     */
    List<UserPaperPageVo> queryByUserPaperOid(String userPaperOid);
}

