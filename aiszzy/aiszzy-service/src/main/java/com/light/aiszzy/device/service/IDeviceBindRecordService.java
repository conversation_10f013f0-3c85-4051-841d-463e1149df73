package com.light.aiszzy.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.device.entity.dto.DeviceBindRecordDto;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordConditionBo;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordBo;
import com.light.aiszzy.device.entity.vo.DeviceBindRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 设备表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface IDeviceBindRecordService extends IService<DeviceBindRecordDto> {

    List<DeviceBindRecordVo> getDeviceBindRecordListByCondition(DeviceBindRecordConditionBo condition);

	AjaxResult addDeviceBindRecord(DeviceBindRecordBo deviceBindRecordBo);

	AjaxResult updateDeviceBindRecord(DeviceBindRecordBo deviceBindRecordBo);

    DeviceBindRecordVo getDetail(String oid);

    DeviceBindRecordVo getDetailByCondition(DeviceBindRecordConditionBo condition);
}

