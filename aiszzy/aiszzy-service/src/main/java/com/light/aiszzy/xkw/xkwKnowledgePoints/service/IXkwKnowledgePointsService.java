package com.light.aiszzy.xkw.xkwKnowledgePoints.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsConditionBo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.dto.XkwKnowledgePointsDto;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo;

import java.util.List;
import java.util.Map;

/**
 * 知识树接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
public interface IXkwKnowledgePointsService extends IService<XkwKnowledgePointsDto> {

    List<XkwKnowledgePointsVo> getXkwKnowledgePointsListByCondition(XkwKnowledgePointsConditionBo condition);

    List<XkwKnowledgePointsVo> getKnowledgeByIds(List<String> ids);

    Map<Long, XkwKnowledgePointsVo> queryMapByIdList(List<Long> knowledgePointIdList);
}

