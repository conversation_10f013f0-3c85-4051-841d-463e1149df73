package com.light.aiszzy.userPaper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.userPaper.entity.dto.UserPaperDto;
import com.light.aiszzy.userPaper.entity.bo.UserPaperConditionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 用户上传试卷接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IUserPaperService extends IService<UserPaperDto> {

    List<UserPaperVo> getUserPaperListByCondition(UserPaperConditionBo condition);

	AjaxResult addUserPaper(UserPaperBo userPaperBo);

	AjaxResult updateUserPaper(UserPaperBo userPaperBo);

    UserPaperVo getDetail(String oid);

    boolean addTotalQuestionNumByOid(String userPaperOid, long addQuestionNum);

    boolean addFinishQuestionNumbByOid(String userPaperOid, int addFinishedNum);

    boolean updatePublishByOid(String oid, Integer isPublish);

    boolean deleteByOidAndUserOid(String oid, String userOid);
}

