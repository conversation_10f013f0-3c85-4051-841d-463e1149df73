package com.light.aiszzy.xkw.xkwQuestionTypes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.xkw.xkwCourses.entity.vo.XkwCoursesVo;
import com.light.aiszzy.xkw.xkwCourses.service.IXkwCoursesService;
import com.light.aiszzy.xkw.xkwQuestionTypes.entity.dto.XkwQuestionTypesDto;
import com.light.aiszzy.xkw.xkwQuestionTypes.entity.vo.XkwQuestionTypesVo;
import com.light.aiszzy.xkw.xkwQuestionTypes.mapper.XkwQuestionTypesMapper;
import com.light.aiszzy.xkw.xkwQuestionTypes.service.IXkwQuestionTypesService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 试题类型接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Service
public class XkwQuestionTypesServiceImpl extends ServiceImpl<XkwQuestionTypesMapper, XkwQuestionTypesDto> implements IXkwQuestionTypesService {

	@Resource
	private XkwQuestionTypesMapper xkwQuestionTypesMapper;

	@Resource
	private IXkwCoursesService xkwCoursesService;


	@Override
	public List<XkwQuestionTypesVo> getByGradeAndSubject(Integer grade, String subject) {
		XkwCoursesVo xkwCoursesVos = this.xkwCoursesService.queryByGradeAndSubject(grade, subject);
		if(xkwCoursesVos == null) {
			return Collections.emptyList();
		}

		QueryWrapper<XkwQuestionTypesDto> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(XkwQuestionTypesDto::getCourseId, xkwCoursesVos.getId());
		List<XkwQuestionTypesDto> xkwQuestionTypesDtos = this.baseMapper.selectList(wrapper);
		if(CollUtil.isEmpty(xkwQuestionTypesDtos)) {
			return Collections.emptyList();
		}
		return xkwQuestionTypesDtos.stream().map(x-> BeanUtil.toBean(x, XkwQuestionTypesVo.class)).collect(Collectors.toList());
	}

	@Override
	public List<XkwQuestionTypesVo> getByStageAndSubject(Integer stage, String subject) {
		XkwCoursesVo xkwCoursesVos = this.xkwCoursesService.queryByStageAndSubject(stage, subject);
		if(xkwCoursesVos == null) {
			return Collections.emptyList();
		}

		QueryWrapper<XkwQuestionTypesDto> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(XkwQuestionTypesDto::getCourseId, xkwCoursesVos.getId());
		List<XkwQuestionTypesDto> xkwQuestionTypesDtos = this.baseMapper.selectList(wrapper);
		if(CollUtil.isEmpty(xkwQuestionTypesDtos)) {
			return Collections.emptyList();
		}
		return xkwQuestionTypesDtos.stream().map(x-> BeanUtil.toBean(x, XkwQuestionTypesVo.class)).collect(Collectors.toList());
	}
}