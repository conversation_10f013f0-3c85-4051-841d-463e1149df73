package com.light.aiszzy.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.question.entity.dto.QuestionDto;
import com.light.aiszzy.question.entity.bo.QuestionConditionBo;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IQuestionService extends IService<QuestionDto> {

    List<QuestionVo> getQuestionListByCondition(QuestionConditionBo condition);

	AjaxResult addQuestion(QuestionBo questionBo);

	AjaxResult updateQuestion(QuestionBo questionBo);

    QuestionVo getDetail(String questionOid, String practiceBookOid);

	/**
	 * 根据内部关联OID获取最新创建的question的OID
	 * @param insideSourceType 内部来源类型
	 * @param insideLinkOid 内部关联OID
	 * @return question的OID
	 */
	String getLatestQuestionOidByInsideLink(String insideSourceType, String insideLinkOid);

}

