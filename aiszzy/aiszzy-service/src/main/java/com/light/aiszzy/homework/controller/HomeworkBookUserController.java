package com.light.aiszzy.homework.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.aiszzy.homework.api.HomeworkBookUserApi;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserConditionBo;
import com.light.aiszzy.homework.service.IHomeworkBookUserService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 老师使用作业本
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-18 14:20:30
 */
@RestController
@Validated
@Api(value = "", tags = "老师使用作业本接口")
public class HomeworkBookUserController implements HomeworkBookUserApi {

    @Autowired
    private IHomeworkBookUserService homeworkBookUserService;

    public AjaxResult getHomeworkBookUserListByCondition(@RequestBody HomeworkBookUserConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", homeworkBookUserService.getHomeworkBookUserListByCondition(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
            PageInfo<HomeworkBookVo> pageInfo = new PageInfo<>(homeworkBookUserService.getHomeworkBookUserListByCondition(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
        }
    }

    public AjaxResult addHomeworkBookUser(@Validated @RequestBody HomeworkBookUserBo homeworkBookUserBo) {
        homeworkBookUserBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return homeworkBookUserService.addBatchHomeworkBookUser(homeworkBookUserBo);
    }

    public AjaxResult updateHomeworkBookUser(@Validated @RequestBody HomeworkBookUserBo homeworkBookUserBo) {
        return homeworkBookUserService.updateHomeworkBookUser(homeworkBookUserBo);
    }

    public AjaxResult getDetail(@NotNull(message = "请选择数据") String oid) {
        Map<String, Object> map = homeworkBookUserService.getDetail(oid);
        return AjaxResult.success(map);
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
        HomeworkBookUserBo homeworkBookUserBo = new HomeworkBookUserBo();
        homeworkBookUserBo.setOid(oid);
        homeworkBookUserBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkBookUserService.updateHomeworkBookUser(homeworkBookUserBo);
    }

    @Override
    public AjaxResult deleteUse(String homeworkBookOid, String userCode, String orgCode) {
        return homeworkBookUserService.deleteUse(homeworkBookOid,userCode,orgCode);
    }
}
