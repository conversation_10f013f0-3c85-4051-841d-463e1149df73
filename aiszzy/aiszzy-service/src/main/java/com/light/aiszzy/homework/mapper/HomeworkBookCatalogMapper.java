package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogVo;

/**
 * 作业本目录Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkBookCatalogMapper extends BaseMapper<HomeworkBookCatalogDto> {

	List<HomeworkBookCatalogVo> getHomeworkBookCatalogListByCondition(HomeworkBookCatalogConditionBo condition);

}
