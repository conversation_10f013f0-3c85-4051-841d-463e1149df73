package com.light.aiszzy.schoolResourcesQuestion.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.schoolResourcesQuestion.entity.dto.SchoolResourcesQuestionSimilarRelationDto;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationConditionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionSimilarRelationVo;
import com.light.aiszzy.schoolResourcesQuestion.service.ISchoolResourcesQuestionSimilarRelationService;
import com.light.aiszzy.schoolResourcesQuestion.mapper.SchoolResourcesQuestionSimilarRelationMapper;
import com.light.core.entity.AjaxResult;
/**
 * 资源库题目相似题接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class SchoolResourcesQuestionSimilarRelationServiceImpl extends ServiceImpl<SchoolResourcesQuestionSimilarRelationMapper, SchoolResourcesQuestionSimilarRelationDto> implements ISchoolResourcesQuestionSimilarRelationService {

	@Resource
	private SchoolResourcesQuestionSimilarRelationMapper schoolResourcesQuestionSimilarRelationMapper;
	
    @Override
	public List<SchoolResourcesQuestionSimilarRelationVo> getSchoolResourcesQuestionSimilarRelationListByCondition(SchoolResourcesQuestionSimilarRelationConditionBo condition) {
        return schoolResourcesQuestionSimilarRelationMapper.getSchoolResourcesQuestionSimilarRelationListByCondition(condition);
	}

	@Override
	public AjaxResult addSchoolResourcesQuestionSimilarRelation(SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo) {
		SchoolResourcesQuestionSimilarRelationDto schoolResourcesQuestionSimilarRelation = new SchoolResourcesQuestionSimilarRelationDto();
		BeanUtils.copyProperties(schoolResourcesQuestionSimilarRelationBo, schoolResourcesQuestionSimilarRelation);
		schoolResourcesQuestionSimilarRelation.setIsDelete(StatusEnum.NOTDELETE.getCode());
		schoolResourcesQuestionSimilarRelation.setOid(IdUtil.simpleUUID());
		if(save(schoolResourcesQuestionSimilarRelation)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateSchoolResourcesQuestionSimilarRelation(SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo) {
		LambdaQueryWrapper<SchoolResourcesQuestionSimilarRelationDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolResourcesQuestionSimilarRelationDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolResourcesQuestionSimilarRelationDto::getOid, schoolResourcesQuestionSimilarRelationBo.getOid());
		SchoolResourcesQuestionSimilarRelationDto schoolResourcesQuestionSimilarRelation = getOne(lqw);
		Long id = schoolResourcesQuestionSimilarRelation.getId();
		if(schoolResourcesQuestionSimilarRelation == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(schoolResourcesQuestionSimilarRelationBo, schoolResourcesQuestionSimilarRelation);
		schoolResourcesQuestionSimilarRelation.setId(id);
		if(updateById(schoolResourcesQuestionSimilarRelation)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public SchoolResourcesQuestionSimilarRelationVo getDetail(String oid) {
		LambdaQueryWrapper<SchoolResourcesQuestionSimilarRelationDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolResourcesQuestionSimilarRelationDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolResourcesQuestionSimilarRelationDto::getOid, oid);
		SchoolResourcesQuestionSimilarRelationDto schoolResourcesQuestionSimilarRelation = getOne(lqw);
	    SchoolResourcesQuestionSimilarRelationVo schoolResourcesQuestionSimilarRelationVo = new SchoolResourcesQuestionSimilarRelationVo();
		if(schoolResourcesQuestionSimilarRelation != null){
			BeanUtils.copyProperties(schoolResourcesQuestionSimilarRelation, schoolResourcesQuestionSimilarRelationVo);
		}
		return schoolResourcesQuestionSimilarRelationVo;
	}

}