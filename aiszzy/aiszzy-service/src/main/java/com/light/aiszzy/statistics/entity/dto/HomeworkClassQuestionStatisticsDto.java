package com.light.aiszzy.statistics.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 作业班级题目正确率
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_class_question_statistics")
public class HomeworkClassQuestionStatisticsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 学校oid
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 班级id
	 */
	@TableField("class_id")
	private Long classId;

	/**
	 * 作业oid
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 关联question题目oid
	 */
	@TableField("question_oid")
	private String questionOid;

	/**
	 * 题型id
	 */
	@TableField("question_type_id")
	private String questionTypeId;

	/**
	 * 题型名称
	 */
	@TableField("question_type_name")
	private String questionTypeName;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Long subject;

	/**
	 * 知识点,多个逗号分割
	 */
	@TableField("knowledge_points_id")
	private String knowledgePointsId;

	/**
	 * 章节ID
	 */
	@TableField("chapter_id")
	private String chapterId;

	/**
	 * 节ID
	 */
	@TableField("section_id")
	private String sectionId;

	/**
	 * 题目排序
	 */
	@TableField("ques_order_num")
	private Long quesOrderNum;

	/**
	 * 班级正确个数
	 */
	@TableField("right_num")
	private Long rightNum;

	/**
	 * 班级错误个数
	 */
	@TableField("wrong_num")
	private Long wrongNum;

	/**
	 * 班级提交个数
	 */
	@TableField("total_num")
	private Long totalNum;

	/**
	 * 班级作业题目正确率小数*100
	 */
	@TableField("accuracy_rate")
	private Long accuracyRate;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
