package com.light.aiszzy.schoolResourcesQuestion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.schoolResourcesQuestion.entity.dto.SchoolResourcesQuestionSimilarRelationDto;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationConditionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionSimilarRelationVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 资源库题目相似题接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface ISchoolResourcesQuestionSimilarRelationService extends IService<SchoolResourcesQuestionSimilarRelationDto> {

    List<SchoolResourcesQuestionSimilarRelationVo> getSchoolResourcesQuestionSimilarRelationListByCondition(SchoolResourcesQuestionSimilarRelationConditionBo condition);

	AjaxResult addSchoolResourcesQuestionSimilarRelation(SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo);

	AjaxResult updateSchoolResourcesQuestionSimilarRelation(SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo);

    SchoolResourcesQuestionSimilarRelationVo getDetail(String oid);

}

