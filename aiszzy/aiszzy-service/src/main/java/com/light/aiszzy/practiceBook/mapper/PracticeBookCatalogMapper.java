package com.light.aiszzy.practiceBook.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookCatalogDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import org.apache.ibatis.annotations.Param;

/**
 * 教辅目录表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface PracticeBookCatalogMapper extends BaseMapper<PracticeBookCatalogDto> {

	List<PracticeBookCatalogVo> getPracticeBookCatalogListByCondition(PracticeBookCatalogConditionBo condition);

    /**
     * 根据 OID 删除, 并删除所有子集
     *
     * @param oid the  oid
     * @return int
     */
    int deleteByOid(String oid);

    /**
     *  根据父级 OID 获取最大排序数值
     * @param parentOid the parent oid  父级 OID
     * @param practiceBookOid the practice book oid  教辅 OID
     * @return {@link Integer }
     */
    Integer selectMaxOrderNumByParentOid(@Param("parentOid") String parentOid, @Param("practiceBookOid") String practiceBookOid);

    /**
     *  根据目录OID集合查询数据
     * @param catalogList the catalog oid list
     * @return {@link List }<{@link PracticeBookCatalogVo }>
     */
    List<PracticeBookCatalogVo> selectListByOidList(@Param("oidList") List<String> catalogList);

    /**
     *  根据教辅 OID 获取目录数据
     * @param practiceBookOid the practice book oid
     * @return {@link List }<{@link PracticeBookCatalogVo }>
     */
    List<PracticeBookCatalogVo> selectByPracticeBookOid(@Param("practiceBookOid") String practiceBookOid);
}
