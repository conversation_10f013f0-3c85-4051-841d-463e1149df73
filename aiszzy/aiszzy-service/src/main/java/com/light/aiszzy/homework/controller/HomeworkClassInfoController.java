package com.light.aiszzy.homework.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassInfoVo;
import com.light.aiszzy.homework.service.IHomeworkClassInfoService;

import com.light.aiszzy.homework.api.HomeworkClassInfoApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 作业班级信息，包含疑问项汇总
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@RestController
@Validated
@Api(value = "", tags = "作业班级信息，包含疑问项汇总接口")
public class HomeworkClassInfoController implements HomeworkClassInfoApi {

    @Autowired
    private IHomeworkClassInfoService homeworkClassInfoService;

    public AjaxResult<PageInfo<HomeworkClassInfoVo>> getHomeworkClassInfoPageListByCondition(@RequestBody HomeworkClassInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<HomeworkClassInfoVo> pageInfo = new PageInfo<>(homeworkClassInfoService.getHomeworkClassInfoListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkClassInfoVo>> getHomeworkClassInfoListByCondition(@RequestBody HomeworkClassInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkClassInfoService.getHomeworkClassInfoListByCondition(condition));
    }

    public AjaxResult addHomeworkClassInfo(@Validated @RequestBody HomeworkClassInfoBo homeworkClassInfoBo) {
        return homeworkClassInfoService.addHomeworkClassInfo(homeworkClassInfoBo);
    }

    public AjaxResult updateHomeworkClassInfo(@Validated @RequestBody HomeworkClassInfoBo homeworkClassInfoBo) {
        if (null == homeworkClassInfoBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkClassInfoService.updateHomeworkClassInfo(homeworkClassInfoBo);
    }

    public AjaxResult<HomeworkClassInfoVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkClassInfoService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkClassInfoBo homeworkClassInfoBo = new HomeworkClassInfoBo();
            homeworkClassInfoBo.setOid(oid);
            homeworkClassInfoBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkClassInfoService.updateHomeworkClassInfo(homeworkClassInfoBo);
    }
}
