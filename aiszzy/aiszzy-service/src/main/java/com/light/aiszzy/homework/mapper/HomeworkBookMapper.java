package com.light.aiszzy.homework.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkBookDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;

/**
 * 作业本Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface HomeworkBookMapper extends BaseMapper<HomeworkBookDto> {

	List<HomeworkBookVo> getHomeworkBookListByCondition(HomeworkBookConditionBo condition);

	List<HomeworkBookVo> getSchoolNotAddHomeworkBookListByCondition(HomeworkBookConditionBo condition);

	List<Map> getCatalogAndHomeworkForDownload(String bookOid);

}
