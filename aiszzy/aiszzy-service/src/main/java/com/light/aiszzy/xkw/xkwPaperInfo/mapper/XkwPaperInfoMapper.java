package com.light.aiszzy.xkw.xkwPaperInfo.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.dto.XkwPaperInfoDto;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoConditionBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.vo.XkwPaperInfoVo;

/**
 * 题目信息Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
public interface XkwPaperInfoMapper extends BaseMapper<XkwPaperInfoDto> {

	List<XkwPaperInfoVo> getXkwPaperInfoListByCondition(XkwPaperInfoConditionBo condition);

}
