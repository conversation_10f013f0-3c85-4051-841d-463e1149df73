package com.light.aiszzy.xkw.xkwTextbookVersions.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsConditionBo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.dto.XkwTextbookVersionsDto;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo;

import java.util.List;

/**
 * 教材版本Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-21 16:06:17
 */
public interface XkwTextbookVersionsMapper extends BaseMapper<XkwTextbookVersionsDto> {

    List<XkwTextbookVersionsVo> getXkwTextbookVersionsListByCondition(XkwTextbookVersionsConditionBo condition);
}
