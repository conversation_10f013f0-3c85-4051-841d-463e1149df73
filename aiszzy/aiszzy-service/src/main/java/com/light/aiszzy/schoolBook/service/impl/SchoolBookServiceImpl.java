package com.light.aiszzy.schoolBook.service.impl;

import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.light.aiszzy.practiceBook.service.IPracticeBookService;
import com.light.base.category.api.CategoryApi;
import com.light.base.dictionary.api.DictionaryDataApi;
import com.light.base.dictionary.entity.bo.DictionaryDataListConditionBo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.aiszzy.practiceBook.enums.PracticeBookSourceType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;
import java.util.ArrayList;
import java.util.stream.Collectors;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;

import com.light.aiszzy.schoolBook.entity.dto.SchoolBookDto;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookVo;
import com.light.aiszzy.schoolBook.entity.vo.PracticeBookWithSchoolVo;
import com.light.aiszzy.schoolBook.service.ISchoolBookService;
import com.light.aiszzy.schoolBook.mapper.SchoolBookMapper;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordBo;
import com.light.aiszzy.schoolBook.entity.bo.BatchSchoolBookBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolInfoBo;
import com.light.aiszzy.schoolBook.entity.bo.BookInfoBo;
import com.light.aiszzy.schoolBook.service.ISchoolBookRecordService;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookWithSchoolCountVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookExportVo;
import com.light.aiszzy.practiceBook.service.PracticeBookApiService;

/**
 * 教辅或作业本开通记录表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Service
public class SchoolBookServiceImpl extends ServiceImpl<SchoolBookMapper, SchoolBookDto> implements ISchoolBookService {

    @Resource
    private SchoolBookMapper schoolBookMapper;

    @Resource
    private IPracticeBookService practiceBookService;

    @Resource
    private ISchoolBookRecordService schoolBookRecordService;

    @Resource
    private DictionaryDataApi dictionaryDataApi;

    @Override
    public List<SchoolBookVo> getSchoolBookListByCondition(SchoolBookConditionBo condition) {
        return schoolBookMapper.getSchoolBookListByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addSchoolBook(SchoolBookBo schoolBookBo) {
        // 检查学校-教辅关系是否已存在
        LambdaQueryWrapper<SchoolBookDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SchoolBookDto::getOrgCode, schoolBookBo.getOrgCode())
                   .eq(SchoolBookDto::getBookOid, schoolBookBo.getBookOid())
                   .eq(SchoolBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode()).last("limit 1");

        SchoolBookDto existingSchoolBook = getOne(queryWrapper);
        String schoolBookOid;

        if (existingSchoolBook != null) {
            // 学校-教辅关系已存在，更新现有记录
            schoolBookOid = existingSchoolBook.getOid();
            BeanUtils.copyProperties(schoolBookBo, existingSchoolBook);
            // 保持原有的id和oid不变
            existingSchoolBook.setOid(schoolBookOid);
            existingSchoolBook.setIsDelete(StatusEnum.NOTDELETE.getCode());

            if (!updateById(existingSchoolBook)) {
                return AjaxResult.fail("更新失败");
            }
        } else {
            // 学校-教辅关系不存在，新增记录
            SchoolBookDto schoolBook = new SchoolBookDto();
            BeanUtils.copyProperties(schoolBookBo, schoolBook);
            schoolBook.setIsDelete(StatusEnum.NOTDELETE.getCode());
            schoolBookOid = IdUtil.simpleUUID();
            schoolBook.setOid(schoolBookOid);

            if (!save(schoolBook)) {
                return AjaxResult.fail("保存失败");
            }
        }

        // 无论是新增还是更新，都要新增SchoolBookRecord开通记录
        SchoolBookRecordBo schoolBookRecordBo = new SchoolBookRecordBo();
        // 复制SchoolBook的相关字段到SchoolBookRecord
        schoolBookRecordBo.setSchoolBookOid(schoolBookOid); // 关联开通记录oid
        schoolBookRecordBo.setOrgCode(schoolBookBo.getOrgCode());
        schoolBookRecordBo.setOrgName(schoolBookBo.getOrgName());
        schoolBookRecordBo.setOrgAreaName(schoolBookBo.getOrgAreaName());
        schoolBookRecordBo.setOrgAreaCode(schoolBookBo.getOrgAreaCode());
        schoolBookRecordBo.setOrgCityName(schoolBookBo.getOrgCityName());
        schoolBookRecordBo.setOrgCityCode(schoolBookBo.getOrgCityCode());
        schoolBookRecordBo.setOrgProvinceName(schoolBookBo.getOrgProvinceName());
        schoolBookRecordBo.setOrgProvinceCode(schoolBookBo.getOrgProvinceCode());
        schoolBookRecordBo.setBookOid(schoolBookBo.getBookOid());
        schoolBookRecordBo.setStatus(schoolBookBo.getStatus());
        schoolBookRecordBo.setStartDate(schoolBookBo.getStartDate());
        schoolBookRecordBo.setEndDate(schoolBookBo.getEndDate());
        schoolBookRecordBo.setCreateBy(schoolBookBo.getCreateBy());
        schoolBookRecordBo.setUpdateBy(schoolBookBo.getUpdateBy());

        // 保存SchoolBookRecord记录，如果失败会触发事务回滚
        AjaxResult recordResult = schoolBookRecordService.addSchoolBookRecord(schoolBookRecordBo);
        if (!recordResult.isSuccess()) {
            // 抛出异常触发事务回滚
            throw new RuntimeException("SchoolBookRecord保存失败：" + recordResult.getMsg());
        }

        return AjaxResult.success(existingSchoolBook != null ? "更新成功" : "保存成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult batchAddSchoolBook(BatchSchoolBookBo batchSchoolBookBo) {
        List<SchoolInfoBo> schoolInfoList = batchSchoolBookBo.getSchoolInfoList();
        List<BookInfoBo> bookInfoList = batchSchoolBookBo.getBookInfoList();

        // 参数校验
        if (CollectionUtils.isEmpty(schoolInfoList)) {
            return AjaxResult.fail("学校信息列表不能为空");
        }
        if (CollectionUtils.isEmpty(bookInfoList)) {
            return AjaxResult.fail("教辅信息列表不能为空");
        }

        int successCount = 0;
        int totalCount = schoolInfoList.size() * bookInfoList.size();
        List<String> errorMessages = new ArrayList<>();

        try {
            // 批量查询已存在的学校-教辅关系，提升性能
            List<String> orgCodes = schoolInfoList.stream().map(SchoolInfoBo::getOrgCode).collect(Collectors.toList());
            List<String> bookOids = bookInfoList.stream().map(BookInfoBo::getBookOid).collect(Collectors.toList());

            LambdaQueryWrapper<SchoolBookDto> batchQueryWrapper = new LambdaQueryWrapper<>();
            batchQueryWrapper.in(SchoolBookDto::getOrgCode, orgCodes)
                           .in(SchoolBookDto::getBookOid, bookOids)
                           .eq(SchoolBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());

            List<SchoolBookDto> existingSchoolBooks = list(batchQueryWrapper);

            // 构建已存在关系的映射表，key为"orgCode_bookOid"
            Map<String, SchoolBookDto> existingRelationMap = existingSchoolBooks.stream()
                .collect(Collectors.toMap(
                    sb -> sb.getOrgCode() + "_" + sb.getBookOid(),
                    sb -> sb
                ));

            // 为每个学校开通每个教辅
            for (SchoolInfoBo schoolInfo : schoolInfoList) {
                for (BookInfoBo bookInfo : bookInfoList) {
                    try {
                        String relationKey = schoolInfo.getOrgCode() + "_" + bookInfo.getBookOid();
                        SchoolBookDto existingSchoolBook = existingRelationMap.get(relationKey);
                        String schoolBookOid;

                        if (existingSchoolBook != null) {
                            // 学校-教辅关系已存在，更新现有记录
                            schoolBookOid = existingSchoolBook.getOid();

                            // 更新字段
                            existingSchoolBook.setOrgName(schoolInfo.getOrgName());
                            existingSchoolBook.setOrgAreaName(schoolInfo.getOrgAreaName());
                            existingSchoolBook.setOrgAreaCode(schoolInfo.getOrgAreaCode());
                            existingSchoolBook.setOrgCityName(schoolInfo.getOrgCityName());
                            existingSchoolBook.setOrgCityCode(schoolInfo.getOrgCityCode());
                            existingSchoolBook.setOrgProvinceName(schoolInfo.getOrgProvinceName());
                            existingSchoolBook.setOrgProvinceCode(schoolInfo.getOrgProvinceCode());
                            existingSchoolBook.setBookType(bookInfo.getBookType());
                            existingSchoolBook.setStatus(batchSchoolBookBo.getStatus());
                            existingSchoolBook.setStartDate(batchSchoolBookBo.getStartDate());
                            existingSchoolBook.setEndDate(batchSchoolBookBo.getEndDate());
                            existingSchoolBook.setUpdateBy(batchSchoolBookBo.getUpdateBy());

                            if (!updateById(existingSchoolBook)) {
                                errorMessages.add(String.format("学校[%s]开通教辅[%s]更新失败", schoolInfo.getOrgName(), bookInfo.getBookOid()));
                                continue;
                            }
                        } else {
                            // 学校-教辅关系不存在，新增记录
                            SchoolBookDto schoolBook = new SchoolBookDto();
                            schoolBook.setOrgCode(schoolInfo.getOrgCode());
                            schoolBook.setOrgName(schoolInfo.getOrgName());
                            schoolBook.setOrgAreaName(schoolInfo.getOrgAreaName());
                            schoolBook.setOrgAreaCode(schoolInfo.getOrgAreaCode());
                            schoolBook.setOrgCityName(schoolInfo.getOrgCityName());
                            schoolBook.setOrgCityCode(schoolInfo.getOrgCityCode());
                            schoolBook.setOrgProvinceName(schoolInfo.getOrgProvinceName());
                            schoolBook.setOrgProvinceCode(schoolInfo.getOrgProvinceCode());
                            schoolBook.setBookOid(bookInfo.getBookOid());
                            schoolBook.setBookType(bookInfo.getBookType());
                            schoolBook.setStatus(batchSchoolBookBo.getStatus());
                            schoolBook.setStartDate(batchSchoolBookBo.getStartDate());
                            schoolBook.setEndDate(batchSchoolBookBo.getEndDate());
                            schoolBook.setCreateBy(batchSchoolBookBo.getCreateBy());
                            schoolBook.setUpdateBy(batchSchoolBookBo.getUpdateBy());
                            schoolBook.setIsDelete(StatusEnum.NOTDELETE.getCode());
                            schoolBookOid = IdUtil.simpleUUID();
                            schoolBook.setOid(schoolBookOid);

                            if (!save(schoolBook)) {
                                errorMessages.add(String.format("学校[%s]开通教辅[%s]保存失败", schoolInfo.getOrgName(), bookInfo.getBookOid()));
                                continue;
                            }
                        }

                        // 无论是新增还是更新，都要新增SchoolBookRecord开通记录
                        SchoolBookRecordBo schoolBookRecordBo = new SchoolBookRecordBo();
                        schoolBookRecordBo.setSchoolBookOid(schoolBookOid);
                        schoolBookRecordBo.setOrgCode(schoolInfo.getOrgCode());
                        schoolBookRecordBo.setOrgName(schoolInfo.getOrgName());
                        schoolBookRecordBo.setOrgAreaName(schoolInfo.getOrgAreaName());
                        schoolBookRecordBo.setOrgAreaCode(schoolInfo.getOrgAreaCode());
                        schoolBookRecordBo.setOrgCityName(schoolInfo.getOrgCityName());
                        schoolBookRecordBo.setOrgCityCode(schoolInfo.getOrgCityCode());
                        schoolBookRecordBo.setOrgProvinceName(schoolInfo.getOrgProvinceName());
                        schoolBookRecordBo.setOrgProvinceCode(schoolInfo.getOrgProvinceCode());
                        schoolBookRecordBo.setBookOid(bookInfo.getBookOid());
                        schoolBookRecordBo.setStatus(batchSchoolBookBo.getStatus());
                        schoolBookRecordBo.setStartDate(batchSchoolBookBo.getStartDate());
                        schoolBookRecordBo.setEndDate(batchSchoolBookBo.getEndDate());
                        schoolBookRecordBo.setCreateBy(batchSchoolBookBo.getCreateBy());
                        schoolBookRecordBo.setUpdateBy(batchSchoolBookBo.getUpdateBy());

                        AjaxResult recordResult = schoolBookRecordService.addSchoolBookRecord(schoolBookRecordBo);
                        if (!recordResult.isSuccess()) {
                            throw new RuntimeException("SchoolBookRecord保存失败：" + recordResult.getMsg());
                        }

                        successCount++;
                    } catch (Exception e) {
                        errorMessages.add(String.format("学校[%s]开通教辅[%s]异常：%s", schoolInfo.getOrgName(),
                            bookInfo.getBookOid(), e.getMessage()));
                    }
                }
            }

            // 构建返回结果
            if (successCount == totalCount) {
                return AjaxResult.success(String.format("批量开通成功，共开通%d条记录", successCount));
            } else if (successCount > 0) {
                String message = String.format("批量开通部分成功，成功%d条，失败%d条", successCount, totalCount - successCount);
                if (!errorMessages.isEmpty()) {
                    message += "。失败详情：" + String.join("; ", errorMessages);
                }
                return AjaxResult.success(message);
            } else {
                String message = "批量开通全部失败";
                if (!errorMessages.isEmpty()) {
                    message += "。失败详情：" + String.join("; ", errorMessages);
                }
                return AjaxResult.fail(message);
            }
        } catch (Exception e) {
            // 如果发生异常，事务会自动回滚
            throw new RuntimeException("批量开通失败：" + e.getMessage(), e);
        }
    }

    @Override
    public AjaxResult updateSchoolBook(SchoolBookBo schoolBookBo) {
        LambdaQueryWrapper<SchoolBookDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SchoolBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(SchoolBookDto::getOid, schoolBookBo.getOid());
        SchoolBookDto schoolBook = getOne(lqw);
        if(schoolBook == null){
            return AjaxResult.fail("数据不存在");
        }
        Long id = schoolBook.getId();

        BeanUtils.copyProperties(schoolBookBo, schoolBook);
        schoolBook.setId(id);
        if (updateById(schoolBook)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public SchoolBookVo getDetail(String oid) {
        LambdaQueryWrapper<SchoolBookDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SchoolBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(SchoolBookDto::getOid, oid);
        SchoolBookDto schoolBook = getOne(lqw);
        SchoolBookVo schoolBookVo = new SchoolBookVo();
        if (schoolBook != null) {
            BeanUtils.copyProperties(schoolBook, schoolBookVo);
        }
        return schoolBookVo;
    }

    @Override
    public Map<String, Integer> getTotalSchoolCountByPracticeBookOids(List<String> practiceBookOids) {
        if (CollectionUtils.isEmpty(practiceBookOids)) {
            return Maps.newHashMap();
        }

        // 查询数据库获取原始结果
        List<Map<String, Object>> rawResults = schoolBookMapper.getTotalSchoolCountByPracticeBookOids(practiceBookOids);

        // 转换为目标格式：key是教辅OID，value是学校数量
        Map<String, Integer> resultMap = Maps.newHashMap();
        for (Map<String, Object> row : rawResults) {
            String practiceBookOid = (String)row.get("practiceBookOid");
            Integer schoolCount = ((Number)row.get("schoolCount")).intValue();
            resultMap.put(practiceBookOid, schoolCount);
        }

        return resultMap;
    }

    @Override
    public Map<String, Integer> getActiveSchoolCountByPracticeBookOids(List<String> practiceBookOids) {
        if (CollectionUtils.isEmpty(practiceBookOids)) {
            return Maps.newHashMap();
        }

        // 查询数据库获取原始结果
        List<Map<String, Object>> rawResults =
            schoolBookMapper.getActiveSchoolCountByPracticeBookOids(practiceBookOids);

        // 转换为目标格式：key是教辅OID，value是学校数量
        Map<String, Integer> resultMap = Maps.newHashMap();
        for (Map<String, Object> row : rawResults) {
            String practiceBookOid = (String)row.get("practiceBookOid");
            Integer schoolCount = ((Number)row.get("schoolCount")).intValue();
            resultMap.put(practiceBookOid, schoolCount);
        }

        return resultMap;
    }

    @Override
    public AjaxResult<PageInfo<PracticeBookWithSchoolCountVo>>
        getPracticeBookPageListWithSchoolCount(PracticeBookConditionBo condition) {
        // 1. 查询教辅列表-分页
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),
            com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(), condition.getClass()));
        List<PracticeBookVo> list = practiceBookService.getPracticeBookListByCondition(condition);
        PageInfo<PracticeBookVo> practiceBookPageInfo = new PageInfo<>(list);
        List<PracticeBookVo> practiceBookList = practiceBookPageInfo.getList();

        // 2. 提取教辅OID集合
        List<String> practiceBookOids =
            practiceBookList.stream().map(PracticeBookVo::getOid).collect(Collectors.toList());

        // 3. 批量查询学校开通数量和活跃学校数量
        Map<String, Integer> totalSchoolCountMap = getTotalSchoolCountByPracticeBookOids(practiceBookOids);
        Map<String, Integer> activeSchoolCountMap = getActiveSchoolCountByPracticeBookOids(practiceBookOids);

        // 4. 组装结果数据
        List<PracticeBookWithSchoolCountVo> resultList = practiceBookList.stream().map(practiceBook -> {
            PracticeBookWithSchoolCountVo practiceBookWithSchoolCount = new PracticeBookWithSchoolCountVo();
            BeanUtils.copyProperties(practiceBook, practiceBookWithSchoolCount);

            Integer totalCount = totalSchoolCountMap.getOrDefault(practiceBook.getOid(), 0);
            Integer activeCount = activeSchoolCountMap.getOrDefault(practiceBook.getOid(), 0);

            practiceBookWithSchoolCount.setTotalSchoolCount(totalCount);
            practiceBookWithSchoolCount.setActiveSchoolCount(activeCount);

            practiceBookWithSchoolCount
                .setPracticeBookSourceType(PracticeBookSourceType.getByXkwZsId(practiceBook.getXkwZsId()).getCode());
            return practiceBookWithSchoolCount;
        }).collect(Collectors.toList());

        // 5. 构建返回的分页信息
        PageInfo<PracticeBookWithSchoolCountVo> resultPageInfo = new PageInfo<>(resultList);
        resultPageInfo.setTotal(practiceBookPageInfo.getTotal());
        resultPageInfo.setPageNum(practiceBookPageInfo.getPageNum());
        resultPageInfo.setPageSize(practiceBookPageInfo.getPageSize());
        resultPageInfo.setPages(practiceBookPageInfo.getPages());

        return AjaxResult.success(resultPageInfo);
    }

    @Override
    public AjaxResult<List<PracticeBookExportVo>> exportPracticeBookWithSchoolCount(PracticeBookConditionBo condition) {
        // 1. 查询教辅列表-不分页（导出所有数据）
        List<PracticeBookVo> list = practiceBookService.getPracticeBookListByCondition(condition);

        // 2. 提取教辅OID集合
        List<String> practiceBookOids = list.stream().map(PracticeBookVo::getOid).collect(Collectors.toList());

        // 3. 批量查询学校开通数量和活跃学校数量
        Map<String, Integer> totalSchoolCountMap = getTotalSchoolCountByPracticeBookOids(practiceBookOids);
        Map<String, Integer> activeSchoolCountMap = getActiveSchoolCountByPracticeBookOids(practiceBookOids);

        // 4. 查询字典数据
        Map<String, String> publisherMap = getDictionaryMap("publisher");
        Map<String, String> subjectMap = getDictionaryMap("subject");
        Map<String, String> gradeMap = getDictionaryMap("book_grade");

        // 5. 组装导出数据
        List<PracticeBookExportVo> exportList = list.stream().map(practiceBook -> {
            PracticeBookWithSchoolCountVo practiceBookWithSchoolCount = new PracticeBookWithSchoolCountVo();
            BeanUtils.copyProperties(practiceBook, practiceBookWithSchoolCount);

            Integer totalCount = totalSchoolCountMap.getOrDefault(practiceBook.getOid(), 0);
            Integer activeCount = activeSchoolCountMap.getOrDefault(practiceBook.getOid(), 0);

            practiceBookWithSchoolCount.setTotalSchoolCount(totalCount);
            practiceBookWithSchoolCount.setActiveSchoolCount(activeCount);

            // 转换为导出对象
            PracticeBookExportVo exportVo =
                PracticeBookExportVo.fromPracticeBookWithSchoolCountVo(practiceBookWithSchoolCount);

            // 处理字典字段
            if (practiceBook.getPublisher() != null) {
                exportVo
                    .setPublisher(publisherMap.getOrDefault(practiceBook.getPublisher(), practiceBook.getPublisher()));
            }
            if (practiceBook.getSubject() != null) {
                exportVo.setSubject(subjectMap.getOrDefault(practiceBook.getSubject().toString(),
                    practiceBook.getSubject().toString()));
            }
            if (practiceBook.getGrade() != null) {
                exportVo.setGrade(
                    gradeMap.getOrDefault(practiceBook.getGrade().toString(), practiceBook.getGrade().toString()));
            }

            // 处理来源字段
            PracticeBookSourceType sourceType = PracticeBookSourceType.getByXkwZsId(practiceBook.getXkwZsId());
            exportVo.setSourceTypeName(sourceType.getName());

            return exportVo;
        }).collect(Collectors.toList());

        return AjaxResult.success(exportList);
    }

    /**
     * 获取字典数据Map
     */
    private Map<String, String> getDictionaryMap(String dictType) {
        Map<String, String> resultMap = new HashMap<>();
        try {
            DictionaryDataListConditionBo condition = new DictionaryDataListConditionBo();
            condition.setDictType(dictType);
            AjaxResult<List<DictionaryDataVo>> result = dictionaryDataApi.getAvailableList(condition);
            if (result.isSuccess() && result.getData() != null) {
                for (DictionaryDataVo dictData : result.getData()) {
                    resultMap.put(dictData.getDictValue(), dictData.getDictLabel());
                }
            }
        } catch (Exception e) {
            // 字典查询失败时记录日志，但不影响导出功能
            System.err.println("查询字典数据失败，dictType: " + dictType + ", error: " + e.getMessage());
        }
        return resultMap;
    }

    @Override
    public AjaxResult<PageInfo<PracticeBookWithSchoolVo>>
        getPracticeBookWithSchoolPageListByCondition(SchoolBookConditionBo condition) {
        // 设置默认删除状态
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());

        // 分页查询教辅与学校关联信息
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),
            com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(), condition.getClass()));

        List<PracticeBookWithSchoolVo> list = schoolBookMapper.getPracticeBookWithSchoolListByCondition(condition);
        
        // 查询字典数据
        Map<String, String> publisherMap = getDictionaryMap("publisher");
        Map<String, String> gradeMap = getDictionaryMap("book_grade");
        Map<String, String> subjectMap = getDictionaryMap("subject");
        Map<String, String> categoryMap = getDictionaryMap("book_type");

        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(practiceBook -> {
                practiceBook.setPracticeBookSourceType(
                    PracticeBookSourceType.getByXkwZsId(practiceBook.getXkwZsId()).getCode());
                
                // 封装字典数据返回
                // 处理出版社
                if (practiceBook.getPublisher() != null) {
                    practiceBook.setPublisherName(publisherMap.getOrDefault(practiceBook.getPublisher(), 
                        practiceBook.getPublisher()));
                }
                
                // 处理年级
                if (practiceBook.getGrade() != null) {
                    practiceBook.setGradeName(gradeMap.getOrDefault(practiceBook.getGrade().toString(), 
                        practiceBook.getGrade().toString()));
                }
                
                // 处理学科
                if (practiceBook.getSubject() != null) {
                    practiceBook.setSubjectName(subjectMap.getOrDefault(practiceBook.getSubject().toString(), 
                        practiceBook.getSubject().toString()));
                }
                
                // 处理分类
                if (practiceBook.getCategory() != null) {
                    practiceBook.setCategoryName(categoryMap.getOrDefault(practiceBook.getCategory(), 
                        practiceBook.getCategory()));
                }
            });
        }

        PageInfo<PracticeBookWithSchoolVo> pageInfo = new PageInfo<>(list);

        // 这里如果添加了学校开通数量和活跃数量，则可以完全替代上面的getPracticeBookPageListWithSchoolCount接口

        // 封装分页接口对象返回
        pageInfo.setTotal(pageInfo.getTotal());
        pageInfo.setPageNum(pageInfo.getPageNum());
        pageInfo.setPageSize(pageInfo.getPageSize());
        pageInfo.setPages(pageInfo.getPages());
        return AjaxResult.success(pageInfo);
    }

}