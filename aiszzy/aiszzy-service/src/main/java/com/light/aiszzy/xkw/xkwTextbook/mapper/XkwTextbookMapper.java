package com.light.aiszzy.xkw.xkwTextbook.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookConditionBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.dto.XkwTextbookDto;
import com.light.aiszzy.xkw.xkwTextbook.entity.vo.XkwTextbookVo;

/**
 * 教材Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
public interface XkwTextbookMapper extends BaseMapper<XkwTextbookDto> {

	List<XkwTextbookVo> getXkwTextbookListByCondition(XkwTextbookConditionBo condition);

}
