package com.light.aiszzy.statistics.service.impl;

import cn.hutool.core.util.IdUtil;
import com.light.aiszzy.statistics.entity.dto.HomeworkClassQuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;
import com.light.aiszzy.statistics.mapper.HomeworkClassQuestionStatisticsMapper;
import com.light.aiszzy.statistics.service.IHomeworkClassQuestionStatisticsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.core.entity.AjaxResult;
/**
 * 作业班级题目正确率接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@Service
public class HomeworkClassQuestionStatisticsServiceImpl extends ServiceImpl<HomeworkClassQuestionStatisticsMapper, HomeworkClassQuestionStatisticsDto> implements IHomeworkClassQuestionStatisticsService {

	@Resource
	private HomeworkClassQuestionStatisticsMapper homeworkClassQuestionStatisticsMapper;
	
    @Override
	public List<HomeworkClassQuestionStatisticsVo> getHomeworkClassQuestionStatisticsListByCondition(HomeworkClassQuestionStatisticsConditionBo condition) {
        return homeworkClassQuestionStatisticsMapper.getHomeworkClassQuestionStatisticsListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkClassQuestionStatistics(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo) {
		HomeworkClassQuestionStatisticsDto homeworkClassQuestionStatistics = new HomeworkClassQuestionStatisticsDto();
		BeanUtils.copyProperties(homeworkClassQuestionStatisticsBo, homeworkClassQuestionStatistics);
		homeworkClassQuestionStatistics.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkClassQuestionStatistics.setOid(IdUtil.simpleUUID());
		if(save(homeworkClassQuestionStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkClassQuestionStatistics(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo) {
		LambdaQueryWrapper<HomeworkClassQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassQuestionStatisticsDto::getOid, homeworkClassQuestionStatisticsBo.getOid());
		HomeworkClassQuestionStatisticsDto homeworkClassQuestionStatistics = getOne(lqw);
		Long id = homeworkClassQuestionStatistics.getId();
		if(homeworkClassQuestionStatistics == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkClassQuestionStatisticsBo, homeworkClassQuestionStatistics);
		homeworkClassQuestionStatistics.setId(id);
		if(updateById(homeworkClassQuestionStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkClassQuestionStatisticsVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkClassQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassQuestionStatisticsDto::getOid, oid);
		HomeworkClassQuestionStatisticsDto homeworkClassQuestionStatistics = getOne(lqw);
	    HomeworkClassQuestionStatisticsVo homeworkClassQuestionStatisticsVo = new HomeworkClassQuestionStatisticsVo();
		if(homeworkClassQuestionStatistics != null){
			BeanUtils.copyProperties(homeworkClassQuestionStatistics, homeworkClassQuestionStatisticsVo);
		}
		return homeworkClassQuestionStatisticsVo;
	}

}