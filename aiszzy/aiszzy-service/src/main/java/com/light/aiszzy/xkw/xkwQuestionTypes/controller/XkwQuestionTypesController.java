package com.light.aiszzy.xkw.xkwQuestionTypes.controller;

import com.light.aiszzy.xkw.xkwQuestionTypes.api.XkwQuestionTypesApi;
import com.light.aiszzy.xkw.xkwQuestionTypes.entity.vo.XkwQuestionTypesVo;
import com.light.aiszzy.xkw.xkwQuestionTypes.service.IXkwQuestionTypesService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 试题类型
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@RestController
@Validated
@Api(value = "", tags = "试题类型接口")
public class XkwQuestionTypesController implements XkwQuestionTypesApi {

    @Autowired
    private IXkwQuestionTypesService xkwQuestionTypesService;

    @Override
    public AjaxResult<List<XkwQuestionTypesVo>> getByGradeAndSubject(@RequestParam("grade") Integer grade, @RequestParam("subject")  String subject) {
        List<XkwQuestionTypesVo> list = this.xkwQuestionTypesService.getByGradeAndSubject(grade, subject);
        return AjaxResult.success(list);
    }

    @Override
    public AjaxResult<List<XkwQuestionTypesVo>> getByStageAndSubject(@RequestParam("stage") Integer stage, @RequestParam("subject") String subject) {
        List<XkwQuestionTypesVo> list = this.xkwQuestionTypesService.getByStageAndSubject(stage, subject);
        return AjaxResult.success(list);
    }
}
