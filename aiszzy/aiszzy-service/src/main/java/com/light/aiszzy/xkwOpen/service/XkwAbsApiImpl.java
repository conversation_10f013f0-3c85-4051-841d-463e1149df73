package com.light.aiszzy.xkwOpen.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.google.gson.Gson;
import com.light.aiszzy.apiRequestLog.entity.dto.ApiRequestLogDto;
import com.light.beans.SimilarRecommendBo;
import com.light.beans.SimilarRecommendVo;
import com.light.utils.ApiRequestLogUtil;
import com.xkw.xop.client.XopHttpClient;
import kong.unirest.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;

@Slf4j
public abstract class XkwAbsApiImpl implements XkwApi {

    @Value("${xkw.access.key.id:}")
    private String xkwAppId;
    @Value("${xkw.access.key.secret:}")
    private String xkwSecret;

    /**
     * 创建XOP HTTP客户端 使用懒加载模式，提高性能
     */
    protected final Supplier<XopHttpClient> clientSupplier = () -> new XopHttpClient.Builder().appId(xkwAppId)
            .secret(xkwSecret).timeout(10).maxConnectionPerRoute(10).build();



    /**
     * 发送GET请求
     */
    protected  <T> Optional<T> doGet(String url, Function<String, T>  respFunc, Map<String, Object> queryParams) {
        return executeRequest(url,"get", queryParams, respFunc, client -> client.get(url, queryParams));
    }

    /**
     * 发送POST请求
     */
    protected <T> Optional<T> doPost(String url, Function<String, T>  respFunc, Map<String, Object> params) {
        return executeRequest(url,"post",params, respFunc, client -> client.post(url, null, params));
    }


    /**
     * 通用HTTP请求处理器 使用函数式接口提高代码复用性
     */
    private <T> Optional<T> executeRequest(String url,String method,Map<String, Object> param, Function<String, T>  respFunc,
                                                    Function<XopHttpClient, HttpResponse<String>> requestExecutor) {
        String body = null;
        try {
            HttpResponse<String> response = requestExecutor.apply(clientSupplier.get());
            body = response.getBody();
            return response.getStatus() == 200 ? Optional.ofNullable(respFunc.apply(body))
                    : Optional.empty();
        } catch (Exception e) {
            log.error("XKW API: {} request failed", url, e);
            throw new RuntimeException("XKW API: " + url + " request failed: " + e.getMessage());
        }finally {
            ApiRequestLogDto apiRequestLogDto = new ApiRequestLogDto();
            apiRequestLogDto.setMethod(method);
            apiRequestLogDto.setUrl(url);
            apiRequestLogDto.setParams(JSONUtil.toJsonStr(param));
            apiRequestLogDto.setResponse(body);
            ApiRequestLogUtil.publishMessage(JSONUtil.toJsonStr(apiRequestLogDto));
        }
    }


    /**
     * 对象转Map的通用方法 使用Optional优化空值处理
     */
    protected Map<String, Object> convertToMap(Object requestBody) {
        return Optional.ofNullable(requestBody).map(body -> {
            Gson gson = new Gson();
            return gson.<Map<String, Object>>fromJson(gson.toJson(body),
                    new TypeToken<Map<String, Object>>() {}.getType());
        }).orElse(null);
    }

}
