package com.light.aiszzy.schoolResourcesQuestion.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationConditionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionSimilarRelationVo;
import com.light.aiszzy.schoolResourcesQuestion.service.ISchoolResourcesQuestionSimilarRelationService;

import com.light.aiszzy.schoolResourcesQuestion.api.SchoolResourcesQuestionSimilarRelationApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 资源库题目相似题
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "资源库题目相似题接口")
public class SchoolResourcesQuestionSimilarRelationController implements SchoolResourcesQuestionSimilarRelationApi {

    @Autowired
    private ISchoolResourcesQuestionSimilarRelationService schoolResourcesQuestionSimilarRelationService;

    public AjaxResult<PageInfo<SchoolResourcesQuestionSimilarRelationVo>> getSchoolResourcesQuestionSimilarRelationPageListByCondition(@RequestBody SchoolResourcesQuestionSimilarRelationConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<SchoolResourcesQuestionSimilarRelationVo> pageInfo = new PageInfo<>(schoolResourcesQuestionSimilarRelationService.getSchoolResourcesQuestionSimilarRelationListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<SchoolResourcesQuestionSimilarRelationVo>> getSchoolResourcesQuestionSimilarRelationListByCondition(@RequestBody SchoolResourcesQuestionSimilarRelationConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(schoolResourcesQuestionSimilarRelationService.getSchoolResourcesQuestionSimilarRelationListByCondition(condition));
    }

    public AjaxResult addSchoolResourcesQuestionSimilarRelation(@Validated @RequestBody SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo) {
        return schoolResourcesQuestionSimilarRelationService.addSchoolResourcesQuestionSimilarRelation(schoolResourcesQuestionSimilarRelationBo);
    }

    public AjaxResult updateSchoolResourcesQuestionSimilarRelation(@Validated @RequestBody SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo) {
        if (null == schoolResourcesQuestionSimilarRelationBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return schoolResourcesQuestionSimilarRelationService.updateSchoolResourcesQuestionSimilarRelation(schoolResourcesQuestionSimilarRelationBo);
    }

    public AjaxResult<SchoolResourcesQuestionSimilarRelationVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(schoolResourcesQuestionSimilarRelationService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            SchoolResourcesQuestionSimilarRelationBo schoolResourcesQuestionSimilarRelationBo = new SchoolResourcesQuestionSimilarRelationBo();
            schoolResourcesQuestionSimilarRelationBo.setOid(oid);
            schoolResourcesQuestionSimilarRelationBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return schoolResourcesQuestionSimilarRelationService.updateSchoolResourcesQuestionSimilarRelation(schoolResourcesQuestionSimilarRelationBo);
    }
}
