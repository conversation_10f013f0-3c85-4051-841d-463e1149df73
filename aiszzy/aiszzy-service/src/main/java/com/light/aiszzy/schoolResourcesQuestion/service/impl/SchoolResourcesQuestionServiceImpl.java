package com.light.aiszzy.schoolResourcesQuestion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionConditionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.dto.SchoolResourcesQuestionDto;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo;
import com.light.aiszzy.schoolResourcesQuestion.mapper.SchoolResourcesQuestionMapper;
import com.light.aiszzy.schoolResourcesQuestion.service.ISchoolResourcesQuestionService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 资源库题目表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class SchoolResourcesQuestionServiceImpl extends ServiceImpl<SchoolResourcesQuestionMapper, SchoolResourcesQuestionDto> implements ISchoolResourcesQuestionService {

	@Resource
	private SchoolResourcesQuestionMapper schoolResourcesQuestionMapper;
	
    @Override
	public List<SchoolResourcesQuestionVo> getSchoolResourcesQuestionListByCondition(SchoolResourcesQuestionConditionBo condition) {
        return schoolResourcesQuestionMapper.getSchoolResourcesQuestionListByCondition(condition);
	}

	@Override
	public AjaxResult addSchoolResourcesQuestion(SchoolResourcesQuestionBo schoolResourcesQuestionBo) {
		SchoolResourcesQuestionDto schoolResourcesQuestion = new SchoolResourcesQuestionDto();
		BeanUtils.copyProperties(schoolResourcesQuestionBo, schoolResourcesQuestion);
		schoolResourcesQuestion.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(StrUtil.isEmpty(schoolResourcesQuestionBo.getOid())) {
			schoolResourcesQuestion.setOid(IdUtil.simpleUUID());
		}
		if(save(schoolResourcesQuestion)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateSchoolResourcesQuestion(SchoolResourcesQuestionBo schoolResourcesQuestionBo) {
		LambdaQueryWrapper<SchoolResourcesQuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolResourcesQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolResourcesQuestionDto::getOid, schoolResourcesQuestionBo.getOid());
		SchoolResourcesQuestionDto schoolResourcesQuestion = getOne(lqw);
		if(schoolResourcesQuestion == null){
			return AjaxResult.fail("数据不存在");
		}
		Long id = schoolResourcesQuestion.getId();
		BeanUtils.copyProperties(schoolResourcesQuestionBo, schoolResourcesQuestion);
		schoolResourcesQuestion.setId(id);
		if(updateById(schoolResourcesQuestion)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public SchoolResourcesQuestionVo getDetail(String oid) {
		LambdaQueryWrapper<SchoolResourcesQuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolResourcesQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolResourcesQuestionDto::getOid, oid);
		SchoolResourcesQuestionDto schoolResourcesQuestion = getOne(lqw);
		if(schoolResourcesQuestion != null){
			return BeanUtil.toBean(schoolResourcesQuestion, SchoolResourcesQuestionVo.class);
		}
		return null;
	}

}