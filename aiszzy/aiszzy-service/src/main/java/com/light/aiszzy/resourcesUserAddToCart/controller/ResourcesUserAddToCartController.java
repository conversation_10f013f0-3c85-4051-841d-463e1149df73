package com.light.aiszzy.resourcesUserAddToCart.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartConditionBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.vo.ResourcesUserAddToCartVo;
import com.light.aiszzy.resourcesUserAddToCart.service.IResourcesUserAddToCartService;

import com.light.aiszzy.resourcesUserAddToCart.api.ResourcesUserAddToCartApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)接口")
public class ResourcesUserAddToCartController implements ResourcesUserAddToCartApi {

    @Autowired
    private IResourcesUserAddToCartService resourcesUserAddToCartService;

    public AjaxResult<PageInfo<ResourcesUserAddToCartVo>> getResourcesUserAddToCartPageListByCondition(@RequestBody ResourcesUserAddToCartConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(), condition.getClass()));
        PageInfo<ResourcesUserAddToCartVo> pageInfo = new PageInfo<>(resourcesUserAddToCartService.getResourcesUserAddToCartListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<ResourcesUserAddToCartVo>> getResourcesUserAddToCartListByCondition(@RequestBody ResourcesUserAddToCartConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(resourcesUserAddToCartService.getResourcesUserAddToCartListByCondition(condition));
    }

    public AjaxResult addResourcesUserAddToCart(@Validated @RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
        return resourcesUserAddToCartService.addResourcesUserAddToCart(resourcesUserAddToCartBo);
    }

    public AjaxResult updateResourcesUserAddToCart(@Validated @RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
        if (null == resourcesUserAddToCartBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return resourcesUserAddToCartService.updateResourcesUserAddToCart(resourcesUserAddToCartBo);
    }

    public AjaxResult<List<ResourcesUserAddToCartVo>> getDetail(String userOid, String orgCode, Integer subject) {
        return AjaxResult.success(resourcesUserAddToCartService.getDetail(userOid, orgCode, subject));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
        ResourcesUserAddToCartBo resourcesUserAddToCartBo = new ResourcesUserAddToCartBo();
        resourcesUserAddToCartBo.setOid(oid);
        resourcesUserAddToCartBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return resourcesUserAddToCartService.updateResourcesUserAddToCart(resourcesUserAddToCartBo);
    }

    @Override
    public AjaxResult deleteQuestion(ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
        return resourcesUserAddToCartService.deleteQuestion(resourcesUserAddToCartBo);

    }

}
