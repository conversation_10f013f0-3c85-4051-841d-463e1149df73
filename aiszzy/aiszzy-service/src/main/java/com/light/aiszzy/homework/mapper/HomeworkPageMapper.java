package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkPageDto;
import com.light.aiszzy.homework.entity.bo.HomeworkPageConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkPageVo;

/**
 * 作业每页题目坐标信息Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
public interface HomeworkPageMapper extends BaseMapper<HomeworkPageDto> {

	List<HomeworkPageVo> getHomeworkPageListByCondition(HomeworkPageConditionBo condition);

}
