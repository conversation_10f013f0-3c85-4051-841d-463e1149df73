package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkClassCommentQuestionDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassCommentQuestionVo;
import com.light.aiszzy.homework.service.IHomeworkClassCommentQuestionService;
import com.light.aiszzy.homework.mapper.HomeworkClassCommentQuestionMapper;
import com.light.core.entity.AjaxResult;
/**
 * 班级作业讲评题目接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@Service
public class HomeworkClassCommentQuestionServiceImpl extends ServiceImpl<HomeworkClassCommentQuestionMapper, HomeworkClassCommentQuestionDto> implements IHomeworkClassCommentQuestionService {

	@Resource
	private HomeworkClassCommentQuestionMapper homeworkClassCommentQuestionMapper;
	
    @Override
	public List<HomeworkClassCommentQuestionVo> getHomeworkClassCommentQuestionListByCondition(HomeworkClassCommentQuestionConditionBo condition) {
        return homeworkClassCommentQuestionMapper.getHomeworkClassCommentQuestionListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkClassCommentQuestion(HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo) {
		HomeworkClassCommentQuestionDto homeworkClassCommentQuestion = new HomeworkClassCommentQuestionDto();
		BeanUtils.copyProperties(homeworkClassCommentQuestionBo, homeworkClassCommentQuestion);
		homeworkClassCommentQuestion.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkClassCommentQuestion.setOid(IdUtil.simpleUUID());
		if(save(homeworkClassCommentQuestion)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkClassCommentQuestion(HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo) {
		LambdaQueryWrapper<HomeworkClassCommentQuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassCommentQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassCommentQuestionDto::getOid, homeworkClassCommentQuestionBo.getOid());
		HomeworkClassCommentQuestionDto homeworkClassCommentQuestion = getOne(lqw);
		Long id = homeworkClassCommentQuestion.getId();
		if(homeworkClassCommentQuestion == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkClassCommentQuestionBo, homeworkClassCommentQuestion);
		homeworkClassCommentQuestion.setId(id);
		if(updateById(homeworkClassCommentQuestion)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkClassCommentQuestionVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkClassCommentQuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassCommentQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassCommentQuestionDto::getOid, oid);
		HomeworkClassCommentQuestionDto homeworkClassCommentQuestion = getOne(lqw);
	    HomeworkClassCommentQuestionVo homeworkClassCommentQuestionVo = new HomeworkClassCommentQuestionVo();
		if(homeworkClassCommentQuestion != null){
			BeanUtils.copyProperties(homeworkClassCommentQuestion, homeworkClassCommentQuestionVo);
		}
		return homeworkClassCommentQuestionVo;
	}

}