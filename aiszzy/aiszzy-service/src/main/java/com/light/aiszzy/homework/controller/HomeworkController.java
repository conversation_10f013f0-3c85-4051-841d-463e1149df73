package com.light.aiszzy.homework.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.aiszzy.homework.service.IHomeworkService;

import com.light.aiszzy.homework.api.HomeworkApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 作业表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "作业表接口")
public class HomeworkController implements HomeworkApi {

    @Autowired
    private IHomeworkService homeworkService;

    public AjaxResult<PageInfo<HomeworkVo>> getHomeworkPageListByCondition(@RequestBody HomeworkConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkVo> pageInfo = new PageInfo<>(homeworkService.getHomeworkListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkVo>> getHomeworkListByCondition(@RequestBody HomeworkConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkService.getHomeworkListByCondition(condition));
    }

    @Override
    public AjaxResult listForTeacher(HomeworkConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", homeworkService.listForTeacher(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<HomeworkVo> pageInfo = new PageInfo<>(homeworkService.listForTeacher(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
        }
    }

    @Override
    public AjaxResult<PageInfo<HomeworkVo>> getHomeworkResultPageListByCondition(HomeworkConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkVo> pageInfo = new PageInfo<>(homeworkService.getHomeworkResultPageListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    @Override
    public AjaxResult listForBind(HomeworkConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", homeworkService.listForBind(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<HomeworkVo> pageInfo = new PageInfo<>(homeworkService.listForBind(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
        }
    }


    @Override
    public AjaxResult listBindAndNoBind(HomeworkConditionBo condition) {
        return homeworkService.listBindAndNoBind(condition);
    }

    @Override
    public String generateCode() {
        return homeworkService.generateCode();
    }

    public AjaxResult addHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        return homeworkService.addHomework(homeworkBo);
    }

    @Override
    public AjaxResult batchUpdateStatus(HomeworkBo homeworkBo) {
        return homeworkService.batchUpdateStatus(homeworkBo);
    }

    public AjaxResult updateHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        if (null == homeworkBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkService.updateHomework(homeworkBo);
    }

    public AjaxResult<HomeworkVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkBo homeworkBo = new HomeworkBo();
            homeworkBo.setOid(oid);
            homeworkBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkService.updateHomework(homeworkBo);
    }

    @Override
    public AjaxResult<HomeworkVo> cartHomework(ResourcesUserAddToCartBo bo) {
        return homeworkService.cartHomework(bo);
    }

    @Override
    public AjaxResult<HomeworkVo> copyHomework(String oid) {
        return homeworkService.copyHomework(oid);
    }

    @Override
    public AjaxResult originalHomework(HomeworkBo homeworkBo) {
        return homeworkService.originalHomework(homeworkBo);
    }

    @Override
    public AjaxResult layeredHomework(HomeworkBo homeworkBo) {
        return homeworkService.layeredHomework(homeworkBo);
    }

    @Override
    public AjaxResult schoolHomework(HomeworkBo homeworkBo) {
        return homeworkService.schoolHomework(homeworkBo);
    }

}
