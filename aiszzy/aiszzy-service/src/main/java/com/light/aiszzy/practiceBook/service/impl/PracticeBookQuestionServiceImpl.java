package com.light.aiszzy.practiceBook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.light.aiszzy.practiceBook.entity.bean.PracticeBookPositionQuestion;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookService;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionVo;
import com.light.aiszzy.xkw.xkwCourses.entity.dto.XkwCoursesDto;
import com.light.aiszzy.xkw.xkwCourses.entity.vo.XkwCoursesVo;
import com.light.aiszzy.xkw.xkwCourses.mapper.XkwCoursesMapper;
import com.light.aiszzy.xkw.xkwCourses.service.IXkwCoursesService;
import com.light.beans.XkwQuestionVo;
import com.light.contants.AISzzyConstants;
import com.light.core.exception.WarningException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.practiceBook.entity.dto.PracticeBookQuestionDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionChangeBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookQuestionService;
import com.light.aiszzy.practiceBook.service.IPracticeBookPageService;
import com.light.aiszzy.practiceBook.mapper.PracticeBookQuestionMapper;
import com.light.aiszzy.question.service.IQuestionService;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.enums.MarkStatusEnum;
import com.light.enums.RelationTypeStatusEnum;
import com.light.enums.InsideSourceTypeEnum;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.xkwOpen.service.XkwService;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionService;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionBo;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionSimilarRelationService;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationBo;
import com.light.enums.QuestionModifyStatusEnum;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 教辅题目表，用于存储题目信息接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Slf4j
@Service
public class PracticeBookQuestionServiceImpl extends ServiceImpl<PracticeBookQuestionMapper, PracticeBookQuestionDto>
    implements IPracticeBookQuestionService {

    @Resource
    private PracticeBookQuestionMapper practiceBookQuestionMapper;

    @Resource
    private IPracticeBookPageService practiceBookPageService;

    @Resource
    private IPracticeBookService practiceBookService;

    @Resource
    private IQuestionService questionService;

    @Resource
    private XkwService xkwService;

    @Resource
    private IResourcesQuestionService resourcesQuestionService;

    @Resource
    private IResourcesQuestionSimilarRelationService resourcesQuestionSimilarRelationService;

    @Resource
    private XkwCoursesMapper xkwCoursesMapper;

    @Resource
    private IXkwCoursesService xkwCoursesService;

    @Resource
    private IPracticeBookQuestionService practiceBookQuestionService;

    @Override
    public List<PracticeBookQuestionVo>
        getPracticeBookQuestionListByCondition(PracticeBookQuestionConditionBo condition) {
        return practiceBookQuestionMapper.getPracticeBookQuestionListByCondition(condition);
    }

    @Override
    public List<PracticeBookQuestionVo> getPracticeBookQuestionWithBodyListByCondition(PracticeBookQuestionConditionBo condition) {
        return practiceBookQuestionMapper.getPracticeBookQuestionWithBodyListByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<PracticeBookQuestionVo> addPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo) {
        String practiceBookPageOid = practiceBookQuestionBo.getPracticeBookPageOid();
        PracticeBookPageVo detail = this.practiceBookPageService.getDetail(practiceBookPageOid);
        if(detail == null){
            throw new WarningException("页码数据不存在");
        }

        List<PracticeBookPositionQuestion> positionQuestionList = detail.getPositionQuestionList();

        long orderNum = 1;
        // 如果下一个题目 OID 为空 将插入到末尾
        String nextPracticeBookQuestionOid = practiceBookQuestionBo.getNextPracticeBookQuestionOid();
        if(StrUtil.isEmpty(nextPracticeBookQuestionOid)) {
            // 获取最大序号值
            Integer maxOrderNum = this.baseMapper.selectMaxOrderByPracticeBookPageOid(practiceBookPageOid);
            orderNum = maxOrderNum + 1;
        }else {
            PracticeBookQuestionVo practiceBookQuestionVo = Optional.ofNullable(this.queryByOid(nextPracticeBookQuestionOid)).orElseThrow(()-> new WarningException("题目信息存在"));
            orderNum = practiceBookQuestionVo.getPageNoOrderNum();
            // 重置排序
            positionQuestionList.stream().filter(x-> x.getOrderNum() >= practiceBookQuestionVo.getPageNoOrderNum() ).forEach(x-> x.setOrderNum(x.getOrderNum() + 1));
        }

        practiceBookQuestionBo.setPracticeBookOid(detail.getPracticeBookOid());
        practiceBookQuestionBo.setPageNo(detail.getPageNo().intValue());


        PracticeBookQuestionDto practiceBookQuestion = new PracticeBookQuestionDto();
        BeanUtils.copyProperties(practiceBookQuestionBo, practiceBookQuestion);
        practiceBookQuestion.setIsDelete(StatusEnum.NOTDELETE.getCode());
        practiceBookQuestion.setOid(IdUtil.simpleUUID());
        practiceBookQuestion.setPageNoOrderNum(orderNum);

        this.save(practiceBookQuestion);


        // 更新页码对应坐标数据

        String position = practiceBookQuestionBo.getPosition();
        String oid = practiceBookQuestion.getOid();
        positionQuestionList.add(new PracticeBookPositionQuestion(position, oid, orderNum));
        this.practiceBookPageService.updateQuestionJsonAndAddNum(practiceBookPageOid, JSON.toJSONString(positionQuestionList));

        return AjaxResult.success(BeanUtil.toBean(practiceBookQuestion, PracticeBookQuestionVo.class));
    }

    @Override
    public AjaxResult updatePracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo) {
        LambdaQueryWrapper<PracticeBookQuestionDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PracticeBookQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(PracticeBookQuestionDto::getOid, practiceBookQuestionBo.getOid());
        PracticeBookQuestionDto practiceBookQuestion = getOne(lqw);
        Long id = practiceBookQuestion.getId();
        if (practiceBookQuestion == null) {
            return AjaxResult.fail("保存失败");
        }
        BeanUtils.copyProperties(practiceBookQuestionBo, practiceBookQuestion);
        practiceBookQuestion.setId(id);
        if (updateById(practiceBookQuestion)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public PracticeBookQuestionVo getDetail(String oid) {
        LambdaQueryWrapper<PracticeBookQuestionDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PracticeBookQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(PracticeBookQuestionDto::getOid, oid);
        PracticeBookQuestionDto practiceBookQuestion = getOne(lqw);
        PracticeBookQuestionVo practiceBookQuestionVo = new PracticeBookQuestionVo();
        if (practiceBookQuestion != null) {
            BeanUtils.copyProperties(practiceBookQuestion, practiceBookQuestionVo);
        }
        return practiceBookQuestionVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult markPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo) {
        try {
            // 1. 查询当前题目信息 - 复用getDetail方法
            PracticeBookQuestionVo practiceBookQuestionVo = getDetail(practiceBookQuestionBo.getOid());
            if (practiceBookQuestionVo == null || practiceBookQuestionVo.getOid() == null) {
                return AjaxResult.fail("题目不存在");
            }

            // 检查是否已经完成标注
            if (practiceBookQuestionVo.getMarkStatus() != null
                && practiceBookQuestionVo.getMarkStatus().equals(MarkStatusEnum.COMPLETED.getCode())) {
                return AjaxResult.fail("该题目已完成标注");
            }

            // 2. 修改practice_book_question表：mark_status=1, out_question_relation_type_status=1
            // 复用updatePracticeBookQuestion方法
            PracticeBookQuestionBo updateBo = new PracticeBookQuestionBo();
            updateBo.setOid(practiceBookQuestionBo.getOid());
            updateBo.setMarkStatus(MarkStatusEnum.COMPLETED.getCode());
            updateBo.setOutQuestionRelationTypeStatus(RelationTypeStatusEnum.CONFIRMED.getCode());

            AjaxResult updateResult = practiceBookQuestionService.updatePracticeBookQuestion(updateBo);
            if (!updateResult.isSuccess()) {
                return AjaxResult.fail("更新题目状态失败");
            }

            // 2.1. 修改practice_book表：finish_question_num+1 - 复用practiceBookService.incrementFinishQuestionNum方法
            if (practiceBookQuestionVo.getPracticeBookOid() != null) {
                AjaxResult practiceBookUpdateResult =
                    practiceBookService.incrementFinishQuestionNum(practiceBookQuestionVo.getPracticeBookOid());
                if (!practiceBookUpdateResult.isSuccess()) {
                    return AjaxResult.fail("更新教辅完成题目数量失败");
                }
            }

            // 3. 修改practice_book_page表：finish_question_num+1 - 复用practiceBookPageService.incrementFinishQuestionNum方法
            if (practiceBookQuestionVo.getPracticeBookPageOid() != null) {
                AjaxResult pageUpdateResult =
                    practiceBookPageService.incrementFinishQuestionNum(practiceBookQuestionVo.getPracticeBookPageOid());
                if (!pageUpdateResult.isSuccess()) {
                    return AjaxResult.fail("更新页面完成题目数量失败");
                }
            }

            // 4. 复制教辅题目信息到question表 - 复用questionService.addQuestion方法
            String questionContentJson = practiceBookQuestionVo.getQuestionContentJson();
            if (StringUtils.isBlank(questionContentJson)) {
                return AjaxResult.fail("questionContentJson为空");
            }

            // 反序列化questionContentJson
            QuestionVo mainQuestionVo;
            try {
                mainQuestionVo = JSON.parseObject(questionContentJson, QuestionVo.class);
            } catch (Exception e) {
                return AjaxResult.fail("反序列化questionContentJson失败：" + e.getMessage());
            }

            if (mainQuestionVo == null) {
                return AjaxResult.fail("questionContentJson解析为空");
            }

            // 创建主题目
            QuestionBo questionBo = new QuestionBo();
            BeanUtils.copyProperties(mainQuestionVo, questionBo);
            questionBo.setThirdSourceType(practiceBookQuestionVo.getThirdSourceType());
            questionBo.setThirdOutId(practiceBookQuestionVo.getThirdOutId());
            questionBo.setInsideSourceType(InsideSourceTypeEnum.PRACTICE_BOOK_QUESTION.getCode());
            questionBo.setInsideLinkOid(practiceBookQuestionVo.getOid());

            AjaxResult addQuestionResult = questionService.addQuestion(questionBo);
            if (!addQuestionResult.isSuccess()) {
                return AjaxResult.fail("保存题目到question表失败");
            }

            // 获取新创建的question的oid
            String questionOid = questionService.getLatestQuestionOidByInsideLink(
                InsideSourceTypeEnum.PRACTICE_BOOK_QUESTION.getCode(), practiceBookQuestionVo.getOid());
            if (questionOid == null) {
                return AjaxResult.fail("获取新创建的question_oid失败");
            }

            // 处理相似题
            List<QuestionVo> similarQuestions = mainQuestionVo.fetSimilarRecommendResultList();
            if (CollectionUtils.isNotEmpty(similarQuestions)) {
                try {
                    List<String> similarQuestionOids = new ArrayList<>();

                    // 遍历相似题，插入到question表
                    for (QuestionVo similarQuestion : similarQuestions) {
                        QuestionBo similarQuestionBo = new QuestionBo();
                        BeanUtils.copyProperties(similarQuestion, similarQuestionBo);
                        similarQuestionBo.setOid(null); // 清空oid，让系统自动生成

                        AjaxResult addSimilarQuestionResult = questionService.addQuestion(similarQuestionBo);
                        if (addSimilarQuestionResult.isSuccess()) {
                            // 获取新插入的相似题oid
                            String similarQuestionOid = (String)addSimilarQuestionResult.getData();
                            if (StringUtils.isNotBlank(similarQuestionOid)) {
                                similarQuestionOids.add(similarQuestionOid);
                                // 更新原相似题对象的oid
                                similarQuestion.setOid(similarQuestionOid);
                            }
                        }
                    }

                    // 重新序列化更新后的相似题jsonArray
                    String updatedSimilarRecommendResult = JSON.toJSONString(similarQuestions);

                    // 更新主题目的similar_recommend_result字段
                    QuestionBo updateMainQuestionBo = new QuestionBo();
                    updateMainQuestionBo.setOid(questionOid);
                    updateMainQuestionBo.setSimilarRecommendResult(updatedSimilarRecommendResult);
                    questionService.updateQuestion(updateMainQuestionBo);
                } catch (Exception e) {
                    return AjaxResult.fail("处理相似题失败：" + e.getMessage());
                }
            }

            // 5. 回写question_oid到practice_book_question表 - 复用updatePracticeBookQuestion方法
            PracticeBookQuestionBo questionOidUpdateBo = new PracticeBookQuestionBo();
            questionOidUpdateBo.setOid(practiceBookQuestionBo.getOid());
            questionOidUpdateBo.setQuestionOid(questionOid);

            AjaxResult updateQuestionOidResult = practiceBookQuestionService.updatePracticeBookQuestion(questionOidUpdateBo);
            if (!updateQuestionOidResult.isSuccess()) {
                return AjaxResult.fail("回写question_oid失败");
            }

            // 6. 通过thirdOutId查询resourceQuestion并更新questionId字段
            String thirdOutId = practiceBookQuestionVo.getThirdOutId();
            if (StringUtils.isNotBlank(thirdOutId)) {
                try {
                    AjaxResult updateResourceQuestionResult = resourcesQuestionService
                        .updateQuestionIdByThirdOutId(practiceBookQuestionVo.getThirdSourceType(), thirdOutId, questionOid);
                    if (!updateResourceQuestionResult.isSuccess()) {
                        log.warn("更新resourceQuestion表questionId字段失败，可能对应的资源题目记录不存在，thirdSourceType: {}, thirdOutId: {}, questionOid: {}, practiceBookQuestionOid: {}, 错误信息: {}",
                            practiceBookQuestionVo.getThirdSourceType(), thirdOutId, questionOid, practiceBookQuestionVo.getOid(), updateResourceQuestionResult.getMsg());
                        // 记录日志但不返回错误，继续执行后续逻辑
                    } else {
                        log.info("成功更新resourceQuestion表questionId字段，thirdSourceType: {}, thirdOutId: {}, questionOid: {}, practiceBookQuestionOid: {}",
                            practiceBookQuestionVo.getThirdSourceType(), thirdOutId, questionOid, practiceBookQuestionVo.getOid());
                    }
                } catch (Exception e) {
                    log.error("更新resourceQuestion表questionId字段时发生异常，thirdSourceType: {}, thirdOutId: {}, questionOid: {}, practiceBookQuestionOid: {}, 异常信息: {}",
                        practiceBookQuestionVo.getThirdSourceType(), thirdOutId, questionOid, practiceBookQuestionVo.getOid(), e.getMessage(), e);
                    // 记录异常日志但不返回错误，继续执行后续逻辑
                }
            }

            return AjaxResult.success("标注完成");

        } catch (Exception e) {
            throw new RuntimeException("完成标注失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cancelPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo) {
        try {
            // 1. 查询当前题目信息 - 复用getDetail方法
            PracticeBookQuestionVo practiceBookQuestionVo = getDetail(practiceBookQuestionBo.getOid());
            if (practiceBookQuestionVo == null || practiceBookQuestionVo.getOid() == null) {
                return AjaxResult.fail("题目不存在");
            }

            // 检查题目是否已被删除
            if (practiceBookQuestionVo.getIsDelete() != null
                && practiceBookQuestionVo.getIsDelete().equals(StatusEnum.ISDELETE.getCode())) {
                return AjaxResult.fail("该题目已被删除");
            }

            // 2. 软删除practice_book_question - 设置is_delete=1
            LambdaUpdateWrapper<PracticeBookQuestionDto> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PracticeBookQuestionDto::getOid, practiceBookQuestionBo.getOid())
                .eq(PracticeBookQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .set(PracticeBookQuestionDto::getIsDelete, StatusEnum.ISDELETE.getCode());

            boolean deleteResult = this.update(updateWrapper);
            if (!deleteResult) {
                return AjaxResult.fail("软删除题目失败");
            }

            // 3. 同时更新practice_book_page的questionJson字段和减少question_num
            if (practiceBookQuestionVo.getPracticeBookPageOid() != null) {
                try {
                    // 获取页面信息
                    PracticeBookPageVo pageVo = practiceBookPageService.queryByOid(practiceBookQuestionVo.getPracticeBookPageOid());
                    if (pageVo != null) {
                        // 获取现有的题目结构列表
                        List<PracticeBookPositionQuestion> positionQuestionList = pageVo.getPositionQuestionList();

                        // 从列表中移除对应的题目
                        positionQuestionList.removeIf(question ->
                            question.getPracticeBookQuestionOid().equals(practiceBookQuestionVo.getOid()));

                        // 同时更新questionJson字段和减少question_num
                        boolean updateResult = practiceBookPageService.updateQuestionJsonAndDecrementNum(
                            practiceBookQuestionVo.getPracticeBookPageOid(),
                            JSON.toJSONString(positionQuestionList));

                        if (!updateResult) {
                            return AjaxResult.fail("更新页面题目信息失败");
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException("取消划题失败-1：" + e.getMessage(), e);
                }
            }

            // 4. 修改practice_book表：total_question_num-1 - 复用practiceBookService.decrementTotalQuestionNum方法
            if (practiceBookQuestionVo.getPracticeBookOid() != null) {
                AjaxResult practiceBookUpdateResult =
                    practiceBookService.decrementTotalQuestionNum(practiceBookQuestionVo.getPracticeBookOid());
                if (!practiceBookUpdateResult.isSuccess()) {
                    return AjaxResult.fail("更新教辅总题目数量失败");
                }
            }

            return AjaxResult.success("取消划题成功");

        } catch (Exception e) {
            throw new RuntimeException("取消划题失败-2：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult delAndSaveBatchByPracticeBookPageOid(String practiceBookPageOid,
        List<PracticeBookQuestionBo> list) {

        // 根据页码表 OID 删除原有数据
        this.deleteByPracticeBookPageOid(practiceBookPageOid);

        List<PracticeBookQuestionDto> questionBoList = list.stream().map(x -> {
            if(StrUtil.isEmpty(x.getOid())) {
                x.setOid(IdUtil.fastSimpleUUID());
            }
            x.setPracticeBookPageOid(practiceBookPageOid);
            return BeanUtil.toBean(x, PracticeBookQuestionDto.class);
        }).collect(Collectors.toList());

        this.saveBatch(questionBoList);

        return AjaxResult.success();
    }

    @Override
    public boolean deleteByPracticeBookPageOid(String practiceBookPageOid) {
        UpdateWrapper<PracticeBookQuestionDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(PracticeBookQuestionDto::getPracticeBookPageOid, practiceBookPageOid);
        updateWrapper.lambda().set(PracticeBookQuestionDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        return this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPracticeBookOid(String practiceBookOid) {

        UpdateWrapper<PracticeBookQuestionDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(PracticeBookQuestionDto::getPracticeBookOid, practiceBookOid);
        updateWrapper.lambda().set(PracticeBookQuestionDto::getIsDelete, StatusEnum.ISDELETE.getCode());

        return this.update(updateWrapper);
    }

    public Integer queryCountByPracticeBookPageOid(String practiceBookPageOid) {
        QueryWrapper<PracticeBookQuestionDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PracticeBookQuestionDto::getPracticeBookPageOid, practiceBookPageOid);
        queryWrapper.lambda().eq(PracticeBookQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        return this.count(queryWrapper);
    }

    /**
     * 根据教辅 OID 查询数量
     *
     * @param practiceBookOid the practice book oid 教辅 OID
     * @return {@link Integer }
     */
    public Integer queryCountByPracticeBookOid(String practiceBookOid) {
        QueryWrapper<PracticeBookQuestionDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PracticeBookQuestionDto::getPracticeBookOid, practiceBookOid);
        queryWrapper.lambda().eq(PracticeBookQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        return this.count(queryWrapper);
    }

    @Override
    public AjaxResult querySimilarQuestion(PracticeBookQuestionChangeBo practiceBookQuestionChangeBo) {
        try {
            // 1. 获取原题目信息
            PracticeBookQuestionVo originalQuestion = getDetail(practiceBookQuestionChangeBo.getOid());
            if (originalQuestion == null) {
                return AjaxResult.fail("题目不存在");
            }

            // 2. 准备查询相似题的参数
            String questionText = practiceBookQuestionChangeBo.getQuestionText();
            if (StringUtils.isBlank(questionText)) {
                // 如果没有提供题干文本，尝试从题目内容中提取
                questionText = extractTextFromQuestionContent(originalQuestion.getQuestionContentJson());
            }

            if (StringUtils.isBlank(questionText)) {
                return AjaxResult.fail("无法获取题目文本内容");
            }

            //
            PracticeBookVo practiceBookVo = this.practiceBookService.queryByOid(originalQuestion.getPracticeBookOid());
            XkwCoursesVo xkwCoursesVo = this.xkwCoursesService.queryByGradeAndSubject(practiceBookVo.getGrade(), practiceBookVo.getSubject().toString());

            // 3. 查询并保存学科网题目到resource_question表
            List<ResourcesQuestionBo> savedQuestions = resourcesQuestionService.queryAndSaveXkwSimilarQuestions(
                questionText, xkwCoursesVo.getId().intValue(),
                practiceBookQuestionChangeBo.getDifficultyLevels(), practiceBookQuestionChangeBo.getQuestionCount());

            if (CollectionUtils.isEmpty(savedQuestions)) {
                return AjaxResult.success(Collections.emptyList());
            }

            // 4. 将学科网返回的对象转换成系统对象并返回
            List<QuestionVo> systemQuestions = convertSavedQuestionsToSystemQuestions(savedQuestions);

            return AjaxResult.success(systemQuestions);

        } catch (Exception e) {
            return AjaxResult.fail("换题失败：" + e.getMessage());
        }
    }

    /**
     * 将保存的ResourcesQuestionBo转换为系统QuestionVo对象 符合DDD设计原则的转换方法
     */
    private List<QuestionVo> convertSavedQuestionsToSystemQuestions(List<ResourcesQuestionBo> savedQuestions) {
        if (CollectionUtils.isEmpty(savedQuestions)) {
            return new ArrayList<>();
        }

        return savedQuestions.stream().map(this::convertResourcesQuestionToQuestionVo).collect(Collectors.toList());
    }

    /**
     * 单个ResourcesQuestionBo转换为QuestionVo
     */
    private QuestionVo convertResourcesQuestionToQuestionVo(ResourcesQuestionBo resourcesQuestion) {
        QuestionVo questionVo = new QuestionVo();

        // 基础字段映射
        questionVo.setOid(resourcesQuestion.getOid());
        questionVo.setQuestionTypeId(resourcesQuestion.getQuestionTypeId());
        questionVo.setQuestionTypeName(resourcesQuestion.getQuestionTypeName());
        questionVo.setSubject(resourcesQuestion.getSubject());
        questionVo.setYear(resourcesQuestion.getYear());
        questionVo.setGrade(resourcesQuestion.getGrade());
        questionVo.setDifficultId(resourcesQuestion.getDifficultId());

        // 题目内容字段
        questionVo.setQuesBody(resourcesQuestion.getQuesBody());
        questionVo.setPublicQues(resourcesQuestion.getPublicQues());
        questionVo.setQuesAnswer(resourcesQuestion.getQuesAnswer());
        questionVo.setAnalysisAnswer(resourcesQuestion.getAnalysisAnswer());

        // 展示类型字段
        questionVo.setQuesBodyType(resourcesQuestion.getQuesBodyType());
        questionVo.setPublicQuesType(resourcesQuestion.getPublicQuesType());
        questionVo.setQuesAnswerType(resourcesQuestion.getQuesAnswerType());
        questionVo.setAnalysisAnswerType(resourcesQuestion.getAnalysisAnswerType());

        // 来源信息
        questionVo.setThirdSourceType(resourcesQuestion.getThirdSourceType());
        questionVo.setThirdOutId(resourcesQuestion.getThirdOutId());

        // 知识点和章节信息
        questionVo.setKnowledgePointsId(resourcesQuestion.getKnowledgePointsId());
        questionVo.setChapterId(resourcesQuestion.getChapterId());
        questionVo.setSectionId(resourcesQuestion.getSectionId());

        // 设置内部来源为资源题目
        questionVo.setInsideSourceType(InsideSourceTypeEnum.RESOURCE_QUESTION.getCode());
        questionVo.setInsideLinkOid(resourcesQuestion.getOid());

        // 时间字段
        questionVo.setCreateTime(resourcesQuestion.getCreateTime());
        questionVo.setUpdateTime(resourcesQuestion.getUpdateTime());
        questionVo.setCreateBy(resourcesQuestion.getCreateBy());
        questionVo.setUpdateBy(resourcesQuestion.getUpdateBy());
        questionVo.setIsDelete(resourcesQuestion.getIsDelete());

        return questionVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult setSimilarQuestion(PracticeBookQuestionChangeBo practiceBookQuestionChangeBo) {
        try {
            // 1. 参数校验
            if (StringUtils.isBlank(practiceBookQuestionChangeBo.getOid())) {
                return AjaxResult.fail("题目OID不能为空");
            }
            if (StringUtils.isBlank(practiceBookQuestionChangeBo.getSimilarQuestionJson())) {
                return AjaxResult.fail("相似题目JSON数据不能为空");
            }

            // 2. 查询当前题目信息
            PracticeBookQuestionVo originalQuestion = getDetail(practiceBookQuestionChangeBo.getOid());
            if (originalQuestion == null) {
                return AjaxResult.fail("题目不存在");
            }

            if (StringUtils.isBlank(originalQuestion.getQuestionContentJson())) {
                return AjaxResult.fail("题目内容为空");
            }

            // 3. 反序列化相似题JSON数据为QuestionVo对象
            QuestionVo newSimilarQuestion;
            // 要替换的题目的第三方题目oid
            String similarThirdOutId = practiceBookQuestionChangeBo.getSimilarThirdOutId();
            // 要替换的题目的难度
            Integer similarDifficultyLevel = practiceBookQuestionChangeBo.getSimilarDifficultyLevel();
            try {
                newSimilarQuestion =
                    JSON.parseObject(practiceBookQuestionChangeBo.getSimilarQuestionJson(), QuestionVo.class);
            } catch (Exception e) {
                return AjaxResult.fail("相似题目JSON格式错误：" + e.getMessage());
            }

            if (newSimilarQuestion == null || StringUtils.isBlank(newSimilarQuestion.getThirdOutId())) {
                return AjaxResult.fail("相似题目数据无效，缺少third_out_id");
            }

            // 4. 反序列化question_content_json为QuestionVo对象
            QuestionVo mainQuestion;
            try {
                mainQuestion = JSON.parseObject(originalQuestion.getQuestionContentJson(), QuestionVo.class);
            } catch (Exception e) {
                return AjaxResult.fail("题目内容JSON格式错误：" + e.getMessage());
            }

            if (mainQuestion == null) {
                return AjaxResult.fail("题目内容解析失败");
            }

            // 5. 获取相似题列表，匹配并更新
            String similarRecommendResultStr = mainQuestion.getSimilarRecommendResult();
            List<QuestionVo> similarQuestions = new ArrayList<>();
            boolean found = false;

            // 如果存在相似题列表，先解析
            if (StringUtils.isNotBlank(similarRecommendResultStr)) {
                try {
                    similarQuestions = JSON.parseArray(similarRecommendResultStr, QuestionVo.class);
                } catch (Exception e) {
                    // 解析失败，使用空列表
                    similarQuestions = new ArrayList<>();
                }
            }

            if (CollectionUtils.isNotEmpty(similarQuestions)) {
                // 第一轮：遍历相似题列表，通过thirdOutId精确匹配
                for (int i = 0; i < similarQuestions.size(); i++) {
                    QuestionVo existingSimilar = similarQuestions.get(i);
                    if (existingSimilar != null
                        && StringUtils.equals(existingSimilar.getThirdOutId(), similarThirdOutId)) {
                        // 找到匹配的相似题，进行更新
                        similarQuestions.set(i, newSimilarQuestion);
                        found = true;
                        break;
                    }
                }

                // 第二轮：如果thirdOutId匹配失败，尝试通过难度进行容错匹配
                if (!found && similarDifficultyLevel != null) {
                    for (int i = 0; i < similarQuestions.size(); i++) {
                        QuestionVo existingSimilar = similarQuestions.get(i);
                        if (existingSimilar != null
                            && StringUtils.isBlank(existingSimilar.getThirdOutId()) // 已存在相似题的thirdOutId为空
                            && existingSimilar.getDifficultId() != null
                            && existingSimilar.getDifficultId().equals(similarDifficultyLevel.longValue())) {
                            // 通过难度匹配到相似题，进行更新
                            similarQuestions.set(i, newSimilarQuestion);
                            found = true;
                            break;
                        }
                    }
                }
            }

            // 如果没有找到匹配的相似题，添加到列表中
            if (!found) {
                similarQuestions.add(newSimilarQuestion);
            }

            // 6. 重新序列化相似题列表到similarRecommendResult字段
            String updatedSimilarRecommendResult = JSON.toJSONString(similarQuestions);
            mainQuestion.setSimilarRecommendResult(updatedSimilarRecommendResult);

            // 7. 序列化整个mainQuestion对象为JSON
            String updatedQuestionContentJson = JSON.toJSONString(mainQuestion);

            // 8. 更新practice_book_question的question_content_json
            PracticeBookQuestionBo updateBo = new PracticeBookQuestionBo();
            updateBo.setOid(practiceBookQuestionChangeBo.getOid());
            updateBo.setQuestionContentJson(updatedQuestionContentJson);

            AjaxResult updateResult = updatePracticeBookQuestion(updateBo);
            if (!updateResult.isSuccess()) {
                return AjaxResult.fail("更新题目内容失败");
            }

            // 9. 处理resources_question_similar_relation关系表
            // 判断is_question_modify是否为true，true则不新增，否则需要新增
            if (QuestionModifyStatusEnum.isNotModified(originalQuestion.getIsQuestionModify())) {
                try {
                    // 查询original_resources_question_oid：通过practice_book_question的third_out_id查询resource_question
                    String originalResourcesQuestionOid = resourcesQuestionService
                        .getOidByThirdOutId(com.light.enums.ThirdSourceTypeEnum.XKW, originalQuestion.getThirdOutId());

                    if (StringUtils.isBlank(originalResourcesQuestionOid)) {
                        log.warn("未找到原题目对应的资源题目记录，原题thirdOutId: {}, practiceBookQuestionOid: {}",
                            originalQuestion.getThirdOutId(), originalQuestion.getOid());
                        // 记录日志但不返回错误，继续执行后续逻辑
                    } else {
                        // 查询similar_resources_question_oid：通过相似题的third_out_id查询resource_question
                        String similarResourcesQuestionOid = resourcesQuestionService
                            .getOidByThirdOutId(com.light.enums.ThirdSourceTypeEnum.XKW, newSimilarQuestion.getThirdOutId());

                        if (StringUtils.isBlank(similarResourcesQuestionOid)) {
                            log.warn("未找到相似题目对应的资源题目记录，相似题thirdOutId: {}, practiceBookQuestionOid: {}",
                                newSimilarQuestion.getThirdOutId(), originalQuestion.getOid());
                            // 记录日志但不返回错误，继续执行后续逻辑
                        } else {
                            // 两个记录都存在时才新增相似题关系记录
                            ResourcesQuestionSimilarRelationBo relationBo = new ResourcesQuestionSimilarRelationBo();
                            relationBo.setOriginalResourcesQuestionOid(originalResourcesQuestionOid);
                            relationBo.setSimilarResourcesQuestionOid(similarResourcesQuestionOid);

                            AjaxResult addRelationResult =
                                resourcesQuestionSimilarRelationService.addResourcesQuestionSimilarRelation(relationBo);
                            if (!addRelationResult.isSuccess()) {
                                log.warn("新增相似题关系失败，originalResourcesQuestionOid: {}, similarResourcesQuestionOid: {}, practiceBookQuestionOid: {}, 错误信息: {}",
                                    originalResourcesQuestionOid, similarResourcesQuestionOid, originalQuestion.getOid(), addRelationResult.getMsg());
                                // 记录日志但不返回错误，继续执行后续逻辑
                            } else {
                                log.info("成功新增相似题关系记录，originalResourcesQuestionOid: {}, similarResourcesQuestionOid: {}, practiceBookQuestionOid: {}",
                                    originalResourcesQuestionOid, similarResourcesQuestionOid, originalQuestion.getOid());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("处理resources_question_similar_relation关系表时发生异常，practiceBookQuestionOid: {}, 异常信息: {}",
                        originalQuestion.getOid(), e.getMessage(), e);
                    // 记录异常日志但不返回错误，继续执行后续逻辑
                }
            }

            return AjaxResult.success("设为相似题成功");

        } catch (Exception e) {
            throw new RuntimeException("设为相似题失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从题目内容JSON中提取文本
     */
    private String extractTextFromQuestionContent(String questionContentJson) {
        if (StringUtils.isBlank(questionContentJson)) {
            return null;
        }
        try {
            // 这里可以根据实际的JSON结构来提取文本内容
            // 暂时返回原始JSON，实际使用时需要根据具体的JSON结构来解析
            return questionContentJson;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public PracticeBookQuestionVo queryByOid(String oid) {
        QueryWrapper<PracticeBookQuestionDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PracticeBookQuestionDto::getOid, oid);
        queryWrapper.lambda().eq(PracticeBookQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        PracticeBookQuestionDto practiceBookQuestionDto = this.baseMapper.selectOne(queryWrapper);
        if(practiceBookQuestionDto==null){
            return null;
        }
        return BeanUtil.toBean(practiceBookQuestionDto, PracticeBookQuestionVo.class);
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public PracticeBookQuestionVo processAndUpdateQuestionByPositionImg(String oid,String practiceBookPageOid, String position) {
        // 获取题目信息
        PracticeBookQuestionVo vo = Optional.ofNullable(this.queryByOid(oid))
                .orElseThrow(() -> new WarningException("题目信息不存在"));
        return this.processAndUpdateQuestionByPositionImg(vo,practiceBookPageOid, position);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PracticeBookQuestionVo processAndUpdateQuestionByPositionImg(PracticeBookQuestionVo vo,String practiceBookPageOid, String position) {
        String practiceBookOid = vo.getPracticeBookOid();
        PracticeBookVo practiceBookVo = Optional.ofNullable(this.practiceBookService.queryByOid(practiceBookOid))
                .orElseThrow(() -> new WarningException("所属教辅信息不存在"));

        PracticeBookPageVo pageVo = this.practiceBookPageService.queryByOid(practiceBookPageOid);
        List<PracticeBookPositionQuestion> positionQuestionList = pageVo.getPositionQuestionList();
         positionQuestionList.stream().filter(x -> x.getPracticeBookQuestionOid().equalsIgnoreCase(vo.getOid()))
                 .forEach(x-> x.setPosition(position));

         AtomicLong i = new AtomicLong(1);
         // 重置顺排序，根据 y 轴大小进行排序
        positionQuestionList = positionQuestionList.stream()
                .sorted(Comparator.comparing(x-> Integer.parseInt(x.getPosition().split("_")[1])))
                .peek(x-> x.setOrderNum(i.getAndIncrement())).collect(Collectors.toList());
        this.practiceBookPageService.updateQuestionJson(practiceBookPageOid, JSON.toJSONString(positionQuestionList));


        String base64Img = this.cutQuestionPageImg2Base64(pageVo.getImageUrl(), position);

        // 获取课程
        Integer stage = AISzzyConstants.getXkwStageByGrade(practiceBookVo.getGrade());
        String subject = AISzzyConstants.getXkwSubject(practiceBookVo.getSubject().toString());
        Integer xkwCourseId = Optional.ofNullable(xkwCoursesMapper.selectOne(new LambdaUpdateWrapper<XkwCoursesDto>()
                        .eq(XkwCoursesDto::getSubjectId, subject)
                        .eq(XkwCoursesDto::getStageId, stage)))
                .map(XkwCoursesDto::getId)
                .map(Long::intValue)
                .orElse(null);


        String similarity = "0";
        String questionContentJson = "{}";
        // 获取题目数据
        ResourcesQuestionVo resourcesQuestionVo = this.resourcesQuestionService.queryAndSaveQuestionWithSimilarRelationsByImage(xkwCourseId, base64Img);
        if(resourcesQuestionVo != null) {
            // 获取相似度
            String questionJson = resourcesQuestionVo.getQuestionJson();
            if (StrUtil.isNotEmpty(questionJson)) {
                XkwQuestionVo xkwQuestionVo = JSON.parseObject(questionJson, XkwQuestionVo.class);
                similarity = xkwQuestionVo.getSimilarity() + "";
            }
            // 题目转换为 question list
            QuestionVo questionVo = BeanUtil.toBean(resourcesQuestionVo, QuestionVo.class);
            questionVo.setOid(null);
            questionVo.setInsideSourceType(InsideSourceTypeEnum.RESOURCE_QUESTION.getCode());
            questionVo.setInsideLinkOid(resourcesQuestionVo.getOid());

            // 设置相似题
            if(CollUtil.isNotEmpty(resourcesQuestionVo.getSimilarQuestionList())) {
                List<QuestionVo> similarQuestionVoList = resourcesQuestionVo.getSimilarQuestionList().stream()
                        .map(x -> BeanUtil.toBean(x, QuestionVo.class))
                        .collect(Collectors.toList());

                questionVo.setSimilarRecommendResult(JSON.toJSONString(similarQuestionVoList));
            }

            questionContentJson = JSON.toJSONString(questionVo);
            vo.setQuestionContentJson(questionContentJson);
        }

        // 更新题目题目信息
        UpdateWrapper<PracticeBookQuestionDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(PracticeBookQuestionDto::getId, vo.getId());
        updateWrapper.lambda().set(PracticeBookQuestionDto::getQuestionContentJson, questionContentJson);
        updateWrapper.lambda().set(PracticeBookQuestionDto::getSimilarity, similarity);
        if(resourcesQuestionVo != null) {
            updateWrapper.lambda().set(StrUtil.isNotEmpty(resourcesQuestionVo.getThirdOutId()) ,PracticeBookQuestionDto::getThirdOutId, resourcesQuestionVo.getThirdOutId());
            updateWrapper.lambda().set(StrUtil.isNotEmpty(resourcesQuestionVo.getThirdSourceType()) ,PracticeBookQuestionDto::getThirdSourceType, resourcesQuestionVo.getThirdSourceType());
            updateWrapper.lambda().set(StrUtil.isNotEmpty(resourcesQuestionVo.getImageOcrText()) ,PracticeBookQuestionDto::getOcrContent, resourcesQuestionVo.getImageOcrText());
            vo.setSimilarity(similarity);
            vo.setOcrContent(resourcesQuestionVo.getImageOcrText());
        }
        this.update(updateWrapper);

        // 更新序号
        List<PracticeBookQuestionDto> questionDtoList = positionQuestionList.stream().map(x -> {
            PracticeBookQuestionDto dto = new PracticeBookQuestionDto();
            dto.setPageNoOrderNum(x.getOrderNum());
            dto.setOid(x.getPracticeBookQuestionOid());
            return dto;
        }).collect(Collectors.toList());

        this.baseMapper.batchUpdateByOid(questionDtoList);

//        vo.setPosition(position);
        return vo;
    }


    public static void main(String[] args) {
        List<PracticeBookPositionQuestion> questions = Lists.newArrayList();
        questions.add(new PracticeBookPositionQuestion("1_3", "123123", 1L));
        questions.add(new PracticeBookPositionQuestion("1_2", "123123", 2L));
        AtomicLong i = new AtomicLong(1);
        questions = questions.stream()
                .sorted(Comparator.comparing(x-> x.getPosition().split("_")[1]))
                .peek(x-> x.setOrderNum(i.getAndIncrement())).collect(Collectors.toList());
        System.out.println(questions);
    }

    /**
     *  截取图片获取
     * @param imgUrl the img url
     * @param position the position x_y_h_w
     * @return {@link String }
     */
    private String cutQuestionPageImg2Base64(String imgUrl, String position) {
        if(StrUtil.isEmpty(imgUrl)) {
            throw new WarningException("图片地址不能为空");
        }

        String[] positionArray = position.split("_");
        // position[2] 长， position[3] 宽，position[0] x  , position[1] y
        String cutImg = imgUrl.concat("?imageMogr2/cut/" + positionArray[2] + "x" + positionArray[3] + "x" + positionArray[0] + "x" + positionArray[1]);
        try(InputStream inputStream = new URL(cutImg).openStream()) {
            return Base64.getEncoder().encodeToString(IoUtil.readBytes(inputStream));
        } catch (IOException e) {
            log.error("【截取图片失败】 cutImg地址：{},  原图片地址：{},  position 地址：{}", cutImg, imgUrl, position);
            throw new WarningException("图片截取失败");
        }
    }
}