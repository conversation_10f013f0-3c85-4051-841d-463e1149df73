package com.light.aiszzy.schoolResourcesQuestion.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 资源库题目相似题
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("school_resources_question_similar_relation")
public class SchoolResourcesQuestionSimilarRelationDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 学校CODE
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * school_resource_question的oid
	 */
	@TableField("school_resources_question_oid")
	private String schoolResourcesQuestionOid;

	/**
	 * 外部题目id
	 */
	@TableField("third_out_id")
	private String thirdOutId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
