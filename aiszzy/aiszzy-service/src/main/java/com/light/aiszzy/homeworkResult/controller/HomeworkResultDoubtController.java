package com.light.aiszzy.homeworkResult.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultDoubtVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultDoubtService;

import com.light.aiszzy.homeworkResult.api.HomeworkResultDoubtApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 扫描结构每页处理结果
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "扫描结构每页处理结果")
public class HomeworkResultDoubtController implements HomeworkResultDoubtApi {

    @Autowired
    private IHomeworkResultDoubtService homeworkResultDoubtService;

    public AjaxResult<PageInfo<HomeworkResultDoubtVo>> getHomeworkResultDoubtPageListByCondition(@RequestBody HomeworkResultDoubtConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkResultDoubtVo> pageInfo = new PageInfo<>(homeworkResultDoubtService.getHomeworkResultDoubtListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkResultDoubtVo>> getHomeworkResultDoubtListByCondition(@RequestBody HomeworkResultDoubtConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkResultDoubtService.getHomeworkResultDoubtListByCondition(condition));
    }

    public AjaxResult addHomeworkResultDoubt(@Validated @RequestBody HomeworkResultDoubtBo homeworkResultDoubtBo) {
        return homeworkResultDoubtService.addHomeworkResultDoubt(homeworkResultDoubtBo);
    }

    public AjaxResult updateHomeworkResultDoubt(@Validated @RequestBody HomeworkResultDoubtBo homeworkResultDoubtBo) {
        if (null == homeworkResultDoubtBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkResultDoubtService.updateHomeworkResultDoubt(homeworkResultDoubtBo);
    }

    public AjaxResult<HomeworkResultDoubtVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkResultDoubtService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkResultDoubtBo homeworkResultDoubtBo = new HomeworkResultDoubtBo();
            homeworkResultDoubtBo.setOid(oid);
            homeworkResultDoubtBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkResultDoubtService.updateHomeworkResultDoubt(homeworkResultDoubtBo);
    }
}
