package com.light.aiszzy.resourcesQuestion.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.resourcesQuestion.entity.dto.ResourcesQuestionSimilarRelationDto;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationConditionBo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionSimilarRelationVo;

/**
 * 资源库题目相似题Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface ResourcesQuestionSimilarRelationMapper extends BaseMapper<ResourcesQuestionSimilarRelationDto> {

	List<ResourcesQuestionSimilarRelationVo> getResourcesQuestionSimilarRelationListByCondition(ResourcesQuestionSimilarRelationConditionBo condition);

}
