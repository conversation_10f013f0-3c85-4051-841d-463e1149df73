package com.light.aiszzy.homeworkResult.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 扫描结构每页处理结果
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_page_result")
public class HomeworkPageResultDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 学校id
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 班级id
	 */
	@TableField("class_id")
	private Long classId;

	/**
	 * 学生oid
	 */
	@TableField("stu_oid")
	private String stuOid;

	/**
	 * 学生名字
	 */
	@TableField("ocr_stu_name")
	private String ocrStuName;

	/**
	 * 学生学号
	 */
	@TableField("ocr_stu_no")
	private String ocrStuNo;

	/**
	 * 页码,正面二维码的页码
	 */
	@TableField("page_no")
	private Integer pageNo;

	/**
	 * 正面图片地址
	 */
	@TableField("stu_answer_url_one")
	private String stuAnswerUrlOne;

	/**
	 * 反面图片地址
	 */
	@TableField("stu_answer_url_two")
	private String stuAnswerUrlTwo;

	/**
	 * 作业答案表oid，错题时使用
	 */
	@TableField("homework_result_oid")
	private String homeworkResultOid;

	/**
	 * 1学号没有，2重复学号，3作业页数不全，4题目疑问
	 */
	@TableField("doubt_type")
	private Integer doubtType;

	/**
	 * 是否有疑问 0：无，1：存在 2已经处理
	 */
	@TableField("is_doubt")
	private Integer isDoubt;

	/**
	 * 智批结果打印合并学生作答和智批结果的地址
	 */
	@TableField("stu_answer_correct_result")
	private String stuAnswerCorrectResult;

	/**
	 * 智批结果（白底红色对错图片地址）用于打印
	 */
	@TableField("stu_answer_correct_result_print")
	private String stuAnswerCorrectResultPrint;

	/**
	 * 学生作答图片信息json存储
	 */
	@TableField("stu_answer_page_info_json")
	private String stuAnswerPageInfoJson;

	/**
	 * 本页（含正反页）答对题目个数
	 */
	@TableField("right_num")
	private Integer rightNum;
	/**
	 * 本页（含正反页）答错题目个数
	 */
	@TableField("wrong_num")
	private Integer wrongNum;
	/**
	 * 本页（含正反页）未知是否正确题目个数
	 */
	@TableField("unknown_num")
	private Integer unknownNum;
	/**
	 * 本页（含正反页）题目总个数
	 */
	@TableField("total_num")
	private Integer totalNum;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
