package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkClassInfoDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassInfoVo;
import com.light.aiszzy.homework.service.IHomeworkClassInfoService;
import com.light.aiszzy.homework.mapper.HomeworkClassInfoMapper;
import com.light.core.entity.AjaxResult;
/**
 * 作业班级信息，包含疑问项汇总接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
@Service
public class HomeworkClassInfoServiceImpl extends ServiceImpl<HomeworkClassInfoMapper, HomeworkClassInfoDto> implements IHomeworkClassInfoService {

	@Resource
	private HomeworkClassInfoMapper homeworkClassInfoMapper;
	
    @Override
	public List<HomeworkClassInfoVo> getHomeworkClassInfoListByCondition(HomeworkClassInfoConditionBo condition) {
        return homeworkClassInfoMapper.getHomeworkClassInfoListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkClassInfo(HomeworkClassInfoBo homeworkClassInfoBo) {
		HomeworkClassInfoDto homeworkClassInfo = new HomeworkClassInfoDto();
		BeanUtils.copyProperties(homeworkClassInfoBo, homeworkClassInfo);
		homeworkClassInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkClassInfo.setOid(IdUtil.simpleUUID());
		if(save(homeworkClassInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkClassInfo(HomeworkClassInfoBo homeworkClassInfoBo) {
		LambdaQueryWrapper<HomeworkClassInfoDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassInfoDto::getOid, homeworkClassInfoBo.getOid());
		HomeworkClassInfoDto homeworkClassInfo = getOne(lqw);
		Long id = homeworkClassInfo.getId();
		if(homeworkClassInfo == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkClassInfoBo, homeworkClassInfo);
		homeworkClassInfo.setId(id);
		if(updateById(homeworkClassInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkClassInfoVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkClassInfoDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassInfoDto::getOid, oid);
		HomeworkClassInfoDto homeworkClassInfo = getOne(lqw);
	    HomeworkClassInfoVo homeworkClassInfoVo = new HomeworkClassInfoVo();
		if(homeworkClassInfo != null){
			BeanUtils.copyProperties(homeworkClassInfo, homeworkClassInfoVo);
		}
		return homeworkClassInfoVo;
	}

}