package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogInfoDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 作业本目录关联作业接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IHomeworkBookCatalogInfoService extends IService<HomeworkBookCatalogInfoDto> {

    List<HomeworkBookCatalogInfoVo> getHomeworkBookCatalogInfoListByCondition(HomeworkBookCatalogInfoConditionBo condition);

    AjaxResult addHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);

    AjaxResult updateHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);

    AjaxResult bind(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);

    AjaxResult unbind(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);

    AjaxResult sortHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo);

    HomeworkBookCatalogInfoVo getDetail(String oid);

}

