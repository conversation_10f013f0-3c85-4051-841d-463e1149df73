package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 作业本目录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IHomeworkBookCatalogService extends IService<HomeworkBookCatalogDto> {

    List<HomeworkBookCatalogVo> getHomeworkBookCatalogListByCondition(HomeworkBookCatalogConditionBo condition);

    AjaxResult listAllChild(HomeworkBookCatalogConditionBo homeworkBookCatalogBo);

    AjaxResult listAllChildByBook(HomeworkBookCatalogConditionBo homeworkBookCatalogBo);

    AjaxResult sortHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo);

    AjaxResult addHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo);

	AjaxResult updateHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo);

    AjaxResult deleteHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo);

    AjaxResult deleteByBookOid(String oid);

    HomeworkBookCatalogVo getDetail(String oid);

}

