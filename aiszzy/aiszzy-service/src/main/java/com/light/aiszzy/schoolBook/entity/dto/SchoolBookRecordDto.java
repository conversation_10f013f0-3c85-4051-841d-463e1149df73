package com.light.aiszzy.schoolBook.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 教辅或作业本开通记录详情表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("school_book_record")
public class SchoolBookRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条开通记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 关联开通记录oid
	 */
	@TableField("school_book_oid")
	private String schoolBookOid;

	/**
	 * 学校CODE
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 学校名称
	 */
	@TableField("org_name")
	private String orgName;

	/**
	 * 学校区域
	 */
	@TableField("org_area_name")
	private String orgAreaName;

	/**
	 * 学校区域CODE
	 */
	@TableField("org_area_code")
	private String orgAreaCode;

	/**
	 * 学校市区
	 */
	@TableField("org_city_name")
	private String orgCityName;

	/**
	 * 学校市区CODE
	 */
	@TableField("org_city_code")
	private String orgCityCode;

	/**
	 * 学校省
	 */
	@TableField("org_province_name")
	private String orgProvinceName;

	/**
	 * 学校省CODE
	 */
	@TableField("org_province_code")
	private String orgProvinceCode;

	/**
	 * 教辅OID或作业本OID
	 */
	@TableField("book_oid")
	private String bookOid;

	/**
	 * 启用 1启用 2停用
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 开通开始日期
	 */
	@TableField("start_date")
	private Date startDate;

	/**
	 * 开通结束日期
	 */
	@TableField("end_date")
	private Date endDate;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
