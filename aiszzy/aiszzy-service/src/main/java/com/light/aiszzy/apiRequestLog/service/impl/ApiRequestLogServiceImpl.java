package com.light.aiszzy.apiRequestLog.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.apiRequestLog.entity.dto.ApiRequestLogDto;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogConditionBo;
import com.light.aiszzy.apiRequestLog.entity.bo.ApiRequestLogBo;
import com.light.aiszzy.apiRequestLog.entity.vo.ApiRequestLogVo;
import com.light.aiszzy.apiRequestLog.service.IApiRequestLogService;
import com.light.aiszzy.apiRequestLog.mapper.ApiRequestLogMapper;
import com.light.core.entity.AjaxResult;
/**
 * 第三方请求日志表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class ApiRequestLogServiceImpl extends ServiceImpl<ApiRequestLogMapper, ApiRequestLogDto> implements IApiRequestLogService {

	@Resource
	private ApiRequestLogMapper apiRequestLogMapper;
	
    @Override
	public List<ApiRequestLogVo> getApiRequestLogListByCondition(ApiRequestLogConditionBo condition) {
        return apiRequestLogMapper.getApiRequestLogListByCondition(condition);
	}

	@Override
	public AjaxResult addApiRequestLog(ApiRequestLogBo apiRequestLogBo) {
		ApiRequestLogDto apiRequestLog = new ApiRequestLogDto();
		BeanUtils.copyProperties(apiRequestLogBo, apiRequestLog);
		apiRequestLog.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(apiRequestLog)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateApiRequestLog(ApiRequestLogBo apiRequestLogBo) {
		LambdaQueryWrapper<ApiRequestLogDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ApiRequestLogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		ApiRequestLogDto apiRequestLog = getOne(lqw);
		Long id = apiRequestLog.getId();
		if(apiRequestLog == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(apiRequestLogBo, apiRequestLog);
		apiRequestLog.setId(id);
		if(updateById(apiRequestLog)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ApiRequestLogVo getDetail(String oid) {
		LambdaQueryWrapper<ApiRequestLogDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ApiRequestLogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		ApiRequestLogDto apiRequestLog = getOne(lqw);
	    ApiRequestLogVo apiRequestLogVo = new ApiRequestLogVo();
		if(apiRequestLog != null){
			BeanUtils.copyProperties(apiRequestLog, apiRequestLogVo);
		}
		return apiRequestLogVo;
	}

}