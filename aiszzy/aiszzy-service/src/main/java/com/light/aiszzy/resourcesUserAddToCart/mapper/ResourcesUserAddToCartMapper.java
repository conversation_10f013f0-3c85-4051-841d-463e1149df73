package com.light.aiszzy.resourcesUserAddToCart.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.resourcesUserAddToCart.entity.dto.ResourcesUserAddToCartDto;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartConditionBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.vo.ResourcesUserAddToCartVo;

/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface ResourcesUserAddToCartMapper extends BaseMapper<ResourcesUserAddToCartDto> {

	List<ResourcesUserAddToCartVo> getResourcesUserAddToCartListByCondition(ResourcesUserAddToCartConditionBo condition);

}
