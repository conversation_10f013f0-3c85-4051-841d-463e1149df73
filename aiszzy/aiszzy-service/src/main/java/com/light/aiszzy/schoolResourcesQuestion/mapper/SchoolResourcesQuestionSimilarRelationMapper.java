package com.light.aiszzy.schoolResourcesQuestion.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.schoolResourcesQuestion.entity.dto.SchoolResourcesQuestionSimilarRelationDto;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionSimilarRelationConditionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionSimilarRelationVo;

/**
 * 资源库题目相似题Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface SchoolResourcesQuestionSimilarRelationMapper extends BaseMapper<SchoolResourcesQuestionSimilarRelationDto> {

	List<SchoolResourcesQuestionSimilarRelationVo> getSchoolResourcesQuestionSimilarRelationListByCondition(SchoolResourcesQuestionSimilarRelationConditionBo condition);

}
