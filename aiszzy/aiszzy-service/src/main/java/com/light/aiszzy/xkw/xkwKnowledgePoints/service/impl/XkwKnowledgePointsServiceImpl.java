package com.light.aiszzy.xkw.xkwKnowledgePoints.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsConditionBo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.dto.XkwKnowledgePointsDto;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.mapper.XkwKnowledgePointsMapper;
import com.light.aiszzy.xkw.xkwKnowledgePoints.service.IXkwKnowledgePointsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 知识树接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Service
public class XkwKnowledgePointsServiceImpl extends ServiceImpl<XkwKnowledgePointsMapper, XkwKnowledgePointsDto> implements IXkwKnowledgePointsService {

	@Resource
	private XkwKnowledgePointsMapper xkwKnowledgePointsMapper;
	
    @Override
	public List<XkwKnowledgePointsVo> getXkwKnowledgePointsListByCondition(XkwKnowledgePointsConditionBo condition) {
        return xkwKnowledgePointsMapper.getXkwKnowledgePointsListByCondition(condition);
	}

	@Override
	public List<XkwKnowledgePointsVo> getKnowledgeByIds(List<String> ids) {
		return xkwKnowledgePointsMapper.getKnowledgeByIds(ids);
	}

	@Override
	public Map<Long, XkwKnowledgePointsVo> queryMapByIdList(List<Long> knowledgePointIdList) {
		List<XkwKnowledgePointsVo> xkwKnowledgePointsVos = this.queryByIdList(knowledgePointIdList);


		return xkwKnowledgePointsVos.stream()
				.collect(Collectors.toMap(XkwKnowledgePointsVo::getId, xkwKnowledgePointsVo -> xkwKnowledgePointsVo));
	}

	public List<XkwKnowledgePointsVo> queryByIdList(List<Long> knowledgePointIdList) {
		if(CollectionUtil.isEmpty(knowledgePointIdList)) {
			return Collections.emptyList();
		}
		QueryWrapper<XkwKnowledgePointsDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(XkwKnowledgePointsDto::getId, knowledgePointIdList);
		List<XkwKnowledgePointsDto> xkwKnowledgePointsDtos = this.baseMapper.selectList(queryWrapper);
		if(CollectionUtil.isEmpty(xkwKnowledgePointsDtos)){
			return Collections.emptyList();
		}
		return xkwKnowledgePointsDtos.stream()
				.map(x-> BeanUtil.toBean(x, XkwKnowledgePointsVo.class))
				.collect(Collectors.toList());
	}
}