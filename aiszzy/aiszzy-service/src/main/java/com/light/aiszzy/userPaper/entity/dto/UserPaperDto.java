package com.light.aiszzy.userPaper.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户上传试卷
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_paper")
public class UserPaperDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 试卷名称
	 */
	@TableField("paper_name")
	private String paperName;

	/**
	 * 学校oid
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 年份
	 */
	@TableField("year")
	private String year;

	/**
	 * 学期  1:上学期  2：下学期
	 */
	@TableField("term")
	private Integer term;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	@TableField("category")
	private String category;

	/**
	 * 版本，存储教辅的版本信息
	 */
	@TableField("text_book_version_id")
	private Long textBookVersionId;

	/**
	 * 教材 ID
	 */
	@TableField("text_book_id")
	private Long textBookId;

	/**
	 * 用户id（创建者）
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 试卷 img 版 url (会有多个)
	 */
	@TableField("paper_img_urls")
	private String paperImgUrls;

	/**
	 * 题目数量
	 */
	@TableField("question_num")
	private Long questionNum;

	/**
	 * 已完成框题个数
	 */
	@TableField("finish_question_num")
	private Long finishQuestionNum;

	/**
	 * 上传文件地址只是pdf
	 */
	@TableField("file_url")
	private String fileUrl;

	/**
	 * 状态，1上传后等待划题 2划题中，3划题结束 4已经生成作业
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 是否发布 0 否 1 是
	 */
	@TableField("is_publish")
	private Integer isPublish;

	/**
	 * 发布时间
	 */
	@TableField("publish_time")
	private Date publishTime;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
