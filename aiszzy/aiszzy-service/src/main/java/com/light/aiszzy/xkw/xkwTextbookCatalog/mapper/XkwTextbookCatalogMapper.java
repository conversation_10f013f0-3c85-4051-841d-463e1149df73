package com.light.aiszzy.xkw.xkwTextbookCatalog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogConditionBo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.dto.XkwTextbookCatalogDto;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.vo.XkwTextbookCatalogVo;

import java.util.List;

/**
 * 教材目录Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-21 16:06:17
 */
public interface XkwTextbookCatalogMapper extends BaseMapper<XkwTextbookCatalogDto> {

    List<XkwTextbookCatalogVo> getXkwTextbookCatalogListByCondition(XkwTextbookCatalogConditionBo condition);
}
