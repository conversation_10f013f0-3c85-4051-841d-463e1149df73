package com.light.aiszzy.device.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.device.entity.bo.DeviceBindRecordConditionBo;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordBo;
import com.light.aiszzy.device.entity.vo.DeviceBindRecordVo;
import com.light.aiszzy.device.service.IDeviceBindRecordService;

import com.light.aiszzy.device.api.DeviceBindRecordApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 设备表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@RestController
@Validated
@Api(value = "", tags = "设备表接口")
public class DeviceBindRecordController implements DeviceBindRecordApi {

    @Autowired
    private IDeviceBindRecordService deviceBindRecordService;

    public AjaxResult<PageInfo<DeviceBindRecordVo>> getDeviceBindRecordPageListByCondition(@RequestBody DeviceBindRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<DeviceBindRecordVo> pageInfo = new PageInfo<>(deviceBindRecordService.getDeviceBindRecordListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<DeviceBindRecordVo>> getDeviceBindRecordListByCondition(@RequestBody DeviceBindRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(deviceBindRecordService.getDeviceBindRecordListByCondition(condition));
    }

    public AjaxResult addDeviceBindRecord(@Validated @RequestBody DeviceBindRecordBo deviceBindRecordBo) {
        return deviceBindRecordService.addDeviceBindRecord(deviceBindRecordBo);
    }

    public AjaxResult updateDeviceBindRecord(@Validated @RequestBody DeviceBindRecordBo deviceBindRecordBo) {
        if (null == deviceBindRecordBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return deviceBindRecordService.updateDeviceBindRecord(deviceBindRecordBo);
    }

    public AjaxResult<DeviceBindRecordVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(deviceBindRecordService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            DeviceBindRecordBo deviceBindRecordBo = new DeviceBindRecordBo();
            deviceBindRecordBo.setOid(oid);
            deviceBindRecordBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return deviceBindRecordService.updateDeviceBindRecord(deviceBindRecordBo);
    }
}
