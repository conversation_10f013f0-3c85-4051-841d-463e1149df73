package com.light.aiszzy.homework.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookUserConditionBo;
import com.light.aiszzy.homework.entity.dto.HomeworkBookUserDto;

import java.util.List;

/**
 * 老师使用作业本Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-18 14:20:30
 */
public interface HomeworkBookUserMapper extends BaseMapper<HomeworkBookUserDto> {

	List<HomeworkBookVo> getHomeworkBookUserListByCondition(HomeworkBookUserConditionBo condition);

}
