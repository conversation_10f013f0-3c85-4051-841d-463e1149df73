package com.light.aiszzy.homework.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogInfoVo;
import com.light.aiszzy.homework.service.IHomeworkBookCatalogInfoService;

import com.light.aiszzy.homework.api.HomeworkBookCatalogInfoApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 作业本目录关联作业
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "作业本目录关联作业接口")
public class HomeworkBookCatalogInfoController implements HomeworkBookCatalogInfoApi {

    @Autowired
    private IHomeworkBookCatalogInfoService homeworkBookCatalogInfoService;

    public AjaxResult<PageInfo<HomeworkBookCatalogInfoVo>> getHomeworkBookCatalogInfoPageListByCondition(@RequestBody HomeworkBookCatalogInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkBookCatalogInfoVo> pageInfo = new PageInfo<>(homeworkBookCatalogInfoService.getHomeworkBookCatalogInfoListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkBookCatalogInfoVo>> getHomeworkBookCatalogInfoListByCondition(@RequestBody HomeworkBookCatalogInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkBookCatalogInfoService.getHomeworkBookCatalogInfoListByCondition(condition));
    }

    @Override
    public AjaxResult bind(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
        return homeworkBookCatalogInfoService.bind(homeworkBookCatalogInfoBo);
    }

    @Override
    public AjaxResult unbind(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
        return homeworkBookCatalogInfoService.unbind(homeworkBookCatalogInfoBo);
    }

    public AjaxResult sortHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
        return homeworkBookCatalogInfoService.sortHomeworkBookCatalogInfo(homeworkBookCatalogInfoBo);
    }

    public AjaxResult addHomeworkBookCatalogInfo(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
        return homeworkBookCatalogInfoService.addHomeworkBookCatalogInfo(homeworkBookCatalogInfoBo);
    }

    public AjaxResult updateHomeworkBookCatalogInfo(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
        if (null == homeworkBookCatalogInfoBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkBookCatalogInfoService.updateHomeworkBookCatalogInfo(homeworkBookCatalogInfoBo);
    }

    public AjaxResult<HomeworkBookCatalogInfoVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkBookCatalogInfoService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo = new HomeworkBookCatalogInfoBo();
            homeworkBookCatalogInfoBo.setOid(oid);
            homeworkBookCatalogInfoBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkBookCatalogInfoService.updateHomeworkBookCatalogInfo(homeworkBookCatalogInfoBo);
    }
}
