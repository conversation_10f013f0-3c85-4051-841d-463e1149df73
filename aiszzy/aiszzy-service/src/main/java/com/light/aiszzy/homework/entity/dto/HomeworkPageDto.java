package com.light.aiszzy.homework.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 作业每页题目坐标信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_page")
public class HomeworkPageDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 题目id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 二维码使用，8位，字母加数字，尝试10次，重复返回报错
	 */
	@TableField("homework_code")
	private String homeworkCode;
	/**
	 * 作业id
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 页码
	 */
	@TableField("page_no")
	private Integer pageNo;

	/**
	 * 题目信息，题号，坐标等
	 */
	@TableField("question_json")
	private String questionJson;

	/**
	 * 单页图片地址
	 */
	@TableField("page_url")
	private String pageUrl;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
