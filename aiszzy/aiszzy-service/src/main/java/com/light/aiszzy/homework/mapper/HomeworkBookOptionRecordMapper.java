package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkBookOptionRecordDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookOptionRecordVo;

/**
 * 作业本映射印送记录Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface HomeworkBookOptionRecordMapper extends BaseMapper<HomeworkBookOptionRecordDto> {

	List<HomeworkBookOptionRecordVo> getHomeworkBookOptionRecordListByCondition(HomeworkBookOptionRecordConditionBo condition);

	Integer getHomeworkBookOptionRecordCountByCondition(HomeworkBookOptionRecordConditionBo condition);

}
