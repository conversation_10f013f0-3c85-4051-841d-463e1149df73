package com.light.aiszzy.device.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.device.entity.dto.DeviceDto;
import com.light.aiszzy.device.entity.bo.DeviceConditionBo;
import com.light.aiszzy.device.entity.vo.DeviceVo;

/**
 * 设备表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface DeviceMapper extends BaseMapper<DeviceDto> {

	List<DeviceVo> getDeviceListByCondition(DeviceConditionBo condition);

}
