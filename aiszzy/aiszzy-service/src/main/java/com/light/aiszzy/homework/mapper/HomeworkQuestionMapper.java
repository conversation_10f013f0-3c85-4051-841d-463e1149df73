package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkQuestionDto;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;

/**
 * 校本作业题目信息Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkQuestionMapper extends BaseMapper<HomeworkQuestionDto> {

	List<HomeworkQuestionVo> getHomeworkQuestionListByCondition(HomeworkQuestionConditionBo condition);

}
