package com.light.aiszzy.statistics.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.statistics.entity.dto.SchoolGradeQuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.SchoolGradeQuestionStatisticsVo;

/**
 * 作业年级题目正确率Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface SchoolGradeQuestionStatisticsMapper extends BaseMapper<SchoolGradeQuestionStatisticsDto> {

	List<SchoolGradeQuestionStatisticsVo> getSchoolGradeQuestionStatisticsListByCondition(SchoolGradeQuestionStatisticsConditionBo condition);

}
