package com.light.aiszzy.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.device.entity.dto.DeviceDto;
import com.light.aiszzy.device.entity.bo.DeviceConditionBo;
import com.light.aiszzy.device.entity.bo.DeviceBo;

import com.light.aiszzy.device.entity.vo.DeviceVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 设备表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
public interface IDeviceService extends IService<DeviceDto> {

    List<DeviceVo> getDeviceListByCondition(DeviceConditionBo condition);

    AjaxResult addDevice(DeviceBo deviceBo);

    AjaxResult updateDevice(DeviceBo deviceBo);

    DeviceVo getDetail(String oid);

    DeviceVo getDetailByHardwareCode(String oid);

    /**
     * 根据软件激活码查询设备详情
     * @param activationCode 软件激活码
     * @return 设备详情
     */
    DeviceVo getDetailByActivationCode(String activationCode);

    /**
     * 根据软件激活码查询设备详情（两个参数不可都为空）
     *
     * @param activationCode 软件激活码
     * @param hardwareCode 设备序列号
     * @return 设备详情
     */
    DeviceVo getDetailByActivationCodeAndHardwareCode(String activationCode, String hardwareCode);

    /**
     * 根据软件激活码查询已激活设备详情（两个参数不可都为空）
     *
     * @param activationCode 软件激活码
     * @param hardwareCode 设备序列号
     * @return 已激活设备详情
     */
    DeviceVo getDetailByActivationCodeAndHardwareCodeForActivated(String activationCode, String hardwareCode);

    /**
     * 设备激活功能：安卓端通过设备软件序列号激活设备，如果已激活则更新激活信息
     * 
     * @param deviceBo 激活参数
     * @return 激活结果
     */
    AjaxResult activateDevice(DeviceBo deviceBo);

}
