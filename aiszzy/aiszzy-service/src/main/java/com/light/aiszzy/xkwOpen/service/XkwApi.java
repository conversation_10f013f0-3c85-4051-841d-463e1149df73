package com.light.aiszzy.xkwOpen.service;

import com.light.beans.*;

import java.util.List;

public interface XkwApi {

    /**
     *  题库搜索
     * @param tikuBo 题库搜索参数
     * @return {@link XkwApiResponseVo }<{@link List }<{@link XkwQuestionVo }>>
     */
    XkwApiResponseVo<List<XkwQuestionVo>> questionSearch(TikuBo tikuBo);


    /**
     * 精品搜索
     * @param tikuBo 搜索参数
     * @return {@link XkwApiResponseVo }<{@link List }<{@link XkwQuestionVo }>>
     */
    XkwApiResponseVo<List<XkwQuestionVo>> questionTextSearch(TikuBo tikuBo);


    /**
     *  题目聚合搜索
     *  1. 学科网精品搜索
     *  2. 精品未搜索到数据 ，使用海量搜索，海量搜索 未搜索到 ，使用相似题搜索
     * @param tikuBo 题库搜索参数
     * @return {@link XkwApiResponseVo }<{@link List }<{@link XkwQuestionVo }>>
     */
    XkwApiResponseVo<List<XkwQuestionVo>> questionBffTextSearch(TikuBo tikuBo);

    /**
     *  ocr 识别
     * @param base64Img 图片 base64数据
     * @return {@link Object }
     */
    String ocr(String base64Img);


    /**
     * 查询相似题
     *
     * @param similarBo              相似题参数
     * @param customDifficultyLevels 查询难易度
     * @return {@link List }<{@link XkwQuestionVo }>
     */
    List<SimilarRecommendVo> querySimilarQuestion(SimilarBo similarBo, List<Integer> customDifficultyLevels);


    List<SimilarRecommendVo> querySimilarQuestions(String questionText, Integer xkwCourseId,
                                                          List<Integer> difficultyLevels, Integer count);


    /**
     *  查询相似题
     * @param similarBo the similar bo
     * @return {@link List }<{@link SimilarRecommendVo }>
     */
    XkwApiResponseVo<List<SimilarRecommendVo>> querySimilarRecommend(SimilarRecommendBo similarBo);





}
