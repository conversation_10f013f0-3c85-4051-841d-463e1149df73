package com.light.aiszzy.statistics.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.statistics.entity.dto.HomeworkClassQuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;

/**
 * 作业班级题目正确率Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface HomeworkClassQuestionStatisticsMapper extends BaseMapper<HomeworkClassQuestionStatisticsDto> {

	List<HomeworkClassQuestionStatisticsVo> getHomeworkClassQuestionStatisticsListByCondition(HomeworkClassQuestionStatisticsConditionBo condition);

}
