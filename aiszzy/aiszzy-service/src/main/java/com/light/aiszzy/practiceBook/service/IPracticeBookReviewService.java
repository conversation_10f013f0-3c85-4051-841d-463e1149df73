package com.light.aiszzy.practiceBook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookReviewDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookReviewVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 教辅信息审核接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
public interface IPracticeBookReviewService extends IService<PracticeBookReviewDto> {

    List<PracticeBookReviewVo> getPracticeBookReviewListByCondition(PracticeBookReviewConditionBo condition);

	AjaxResult addPracticeBookReview(PracticeBookReviewBo practiceBookReviewBo);

	AjaxResult updatePracticeBookReview(PracticeBookReviewBo practiceBookReviewBo);

    PracticeBookReviewVo getDetail(String oid);

}

