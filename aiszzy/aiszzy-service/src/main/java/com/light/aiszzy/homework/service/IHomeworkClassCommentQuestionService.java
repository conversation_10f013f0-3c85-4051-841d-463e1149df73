package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkClassCommentQuestionDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassCommentQuestionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 班级作业讲评题目接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface IHomeworkClassCommentQuestionService extends IService<HomeworkClassCommentQuestionDto> {

    List<HomeworkClassCommentQuestionVo> getHomeworkClassCommentQuestionListByCondition(HomeworkClassCommentQuestionConditionBo condition);

	AjaxResult addHomeworkClassCommentQuestion(HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo);

	AjaxResult updateHomeworkClassCommentQuestion(HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo);

    HomeworkClassCommentQuestionVo getDetail(String oid);

}

