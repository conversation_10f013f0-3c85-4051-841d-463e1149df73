package com.light.aiszzy.resultUploadFile.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.resultUploadFile.entity.dto.ResultUploadFileDto;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileConditionBo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileBo;
import com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo;
import com.light.aiszzy.resultUploadFile.service.IResultUploadFileService;
import com.light.aiszzy.resultUploadFile.mapper.ResultUploadFileMapper;
import com.light.core.entity.AjaxResult;
/**
 * 扫描上传图片接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
@Service
public class ResultUploadFileServiceImpl extends ServiceImpl<ResultUploadFileMapper, ResultUploadFileDto> implements IResultUploadFileService {

	@Resource
	private ResultUploadFileMapper resultUploadFileMapper;
	
    @Override
	public List<ResultUploadFileVo> getResultUploadFileListByCondition(ResultUploadFileConditionBo condition) {
        return resultUploadFileMapper.getResultUploadFileListByCondition(condition);
	}

	@Override
	public AjaxResult addResultUploadFile(ResultUploadFileBo resultUploadFileBo) {
		ResultUploadFileDto resultUploadFile = new ResultUploadFileDto();
		BeanUtils.copyProperties(resultUploadFileBo, resultUploadFile);
		resultUploadFile.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(resultUploadFile)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResultUploadFile(ResultUploadFileBo resultUploadFileBo) {
		LambdaQueryWrapper<ResultUploadFileDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ResultUploadFileDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		ResultUploadFileDto resultUploadFile = getOne(lqw);
		Long id = resultUploadFile.getId();
		if(resultUploadFile == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(resultUploadFileBo, resultUploadFile);
		resultUploadFile.setId(id);
		if(updateById(resultUploadFile)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResultUploadFileVo getDetail(String oid) {
		LambdaQueryWrapper<ResultUploadFileDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ResultUploadFileDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		ResultUploadFileDto resultUploadFile = getOne(lqw);
	    ResultUploadFileVo resultUploadFileVo = new ResultUploadFileVo();
		if(resultUploadFile != null){
			BeanUtils.copyProperties(resultUploadFile, resultUploadFileVo);
		}
		return resultUploadFileVo;
	}

}