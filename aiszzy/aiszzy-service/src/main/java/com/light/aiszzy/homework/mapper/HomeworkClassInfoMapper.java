package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkClassInfoDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassInfoVo;

/**
 * 作业班级信息，包含疑问项汇总Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
public interface HomeworkClassInfoMapper extends BaseMapper<HomeworkClassInfoDto> {

	List<HomeworkClassInfoVo> getHomeworkClassInfoListByCondition(HomeworkClassInfoConditionBo condition);

}
