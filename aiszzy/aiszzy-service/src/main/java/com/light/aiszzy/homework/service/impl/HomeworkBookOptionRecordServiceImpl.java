package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkBookOptionRecordDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookOptionRecordVo;
import com.light.aiszzy.homework.service.IHomeworkBookOptionRecordService;
import com.light.aiszzy.homework.mapper.HomeworkBookOptionRecordMapper;
import com.light.core.entity.AjaxResult;
/**
 * 作业本映射印送记录接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Service
public class HomeworkBookOptionRecordServiceImpl extends ServiceImpl<HomeworkBookOptionRecordMapper, HomeworkBookOptionRecordDto> implements IHomeworkBookOptionRecordService {

	@Resource
	private HomeworkBookOptionRecordMapper homeworkBookOptionRecordMapper;
	
    @Override
	public List<HomeworkBookOptionRecordVo> getHomeworkBookOptionRecordListByCondition(HomeworkBookOptionRecordConditionBo condition) {
        return homeworkBookOptionRecordMapper.getHomeworkBookOptionRecordListByCondition(condition);
	}

	@Override
	public AjaxResult getHomeworkBookOptionRecordCountByCondition(HomeworkBookOptionRecordConditionBo condition) {
		return AjaxResult.success(homeworkBookOptionRecordMapper.getHomeworkBookOptionRecordCountByCondition(condition));
	}

	@Override
	public AjaxResult addHomeworkBookOptionRecord(HomeworkBookOptionRecordBo homeworkBookOptionRecordBo) {
		HomeworkBookOptionRecordDto homeworkBookOptionRecord = new HomeworkBookOptionRecordDto();
		BeanUtils.copyProperties(homeworkBookOptionRecordBo, homeworkBookOptionRecord);
		homeworkBookOptionRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkBookOptionRecord.setOid(IdUtil.simpleUUID());
		if(save(homeworkBookOptionRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkBookOptionRecord(HomeworkBookOptionRecordBo homeworkBookOptionRecordBo) {
		LambdaQueryWrapper<HomeworkBookOptionRecordDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkBookOptionRecordDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkBookOptionRecordDto::getOid, homeworkBookOptionRecordBo.getOid());
		HomeworkBookOptionRecordDto homeworkBookOptionRecord = getOne(lqw);
		Long id = homeworkBookOptionRecord.getId();
		if(homeworkBookOptionRecord == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkBookOptionRecordBo, homeworkBookOptionRecord);
		homeworkBookOptionRecord.setId(id);
		if(updateById(homeworkBookOptionRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkBookOptionRecordVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkBookOptionRecordDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkBookOptionRecordDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkBookOptionRecordDto::getOid, oid);
		HomeworkBookOptionRecordDto homeworkBookOptionRecord = getOne(lqw);
	    HomeworkBookOptionRecordVo homeworkBookOptionRecordVo = new HomeworkBookOptionRecordVo();
		if(homeworkBookOptionRecord != null){
			BeanUtils.copyProperties(homeworkBookOptionRecord, homeworkBookOptionRecordVo);
		}
		return homeworkBookOptionRecordVo;
	}

}