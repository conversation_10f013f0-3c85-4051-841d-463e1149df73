package com.light.aiszzy.question.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.question.entity.bo.QuestionConditionBo;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import com.light.aiszzy.question.service.IQuestionService;

import com.light.aiszzy.question.api.QuestionApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 题目表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "题目表接口")
public class QuestionController implements QuestionApi {

    @Autowired
    private IQuestionService questionService;

    public AjaxResult<PageInfo<QuestionVo>> getQuestionPageListByCondition(@RequestBody QuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<QuestionVo> pageInfo = new PageInfo<>(questionService.getQuestionListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<QuestionVo>> getQuestionListByCondition(@RequestBody QuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(questionService.getQuestionListByCondition(condition));
    }

    public AjaxResult addQuestion(@Validated @RequestBody QuestionBo questionBo) {
        return questionService.addQuestion(questionBo);
    }

    public AjaxResult updateQuestion(@Validated @RequestBody QuestionBo questionBo) {
        if (null == questionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return questionService.updateQuestion(questionBo);
    }

    @Override
    public AjaxResult<QuestionVo> getDetail(String questionOid, String practiceBookOid) {
        return AjaxResult.success(questionService.getDetail(questionOid,practiceBookOid));
    }


    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            QuestionBo questionBo = new QuestionBo();
            questionBo.setOid(oid);
            questionBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return questionService.updateQuestion(questionBo);
    }
}
