package com.light.aiszzy.practiceBook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.core.entity.AjaxResult;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 教辅信息表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IPracticeBookService extends IService<PracticeBookDto> {

    List<PracticeBookVo> getPracticeBookListByCondition(PracticeBookConditionBo condition);

	AjaxResult addPracticeBook(PracticeBookBo practiceBookBo);

	AjaxResult updatePracticeBook(PracticeBookBo practiceBookBo);

    PracticeBookVo getDetail(String oid);

    PracticeBookVo queryByOid(String oid);

    /**
     * 根据 OID 更新目录状态
     * @param oid the practice book oid
     * @param catalogStatus the catalog status 目录状态
     * @return boolean
     */
    boolean updateCatalogStatusByOid(String oid, int catalogStatus);

    /**
     *  根据 OID 更新目录地址
     * @param oid the oid
     * @param catalogFilePath the catalog file path 目录文件地址
     * @return {@link AjaxResult }
     */
    AjaxResult updateCatalogPathByOid(String oid, String catalogFilePath);

    /**
     * 增加教辅完成题目数量
     * @param practiceBookOid 教辅OID
     * @return
     */
    AjaxResult incrementFinishQuestionNum(String practiceBookOid);

    /**
     * 减少教辅总题目数量
     * @param practiceBookOid 教辅OID
     * @return
     */
    AjaxResult decrementTotalQuestionNum(String practiceBookOid);

    /**
     * 根据 oid 更新教辅文件信息
     *
     * @param oid the oid
     * @param fileType the file type 文件类型 zip pdf
     * @param fileUrl the file url 文件地址
     * @return {@link AjaxResult }
     */
    boolean updateFileInfoByOid(String oid, Integer fileType, String fileUrl);

    /**
     *  根据 OID 更新教辅 题目数量信息
     * @param practiceBookOid the practice book oid
     * @param totalQuestionNum the total quesiton num 总题目数量
     * @param finishQuestionNum the finish question num 完成框体数量
     */
    boolean updateQuestionNumInfoByOid(String practiceBookOid, long totalQuestionNum, long finishQuestionNum);

    /**
     *  根据教辅 OID 更新题目数量
     * @param practiceBookOid the pratice book oid 教辅 OID
     * @param addQuestionNum the add question num 需要增加的题目数量
     * @return boolean
     */
    boolean addTotalQuestionNumByOid(String practiceBookOid, long addQuestionNum);

    /**
     * 更改是否支持高排
     * @param oid the OID 教辅 OID
     * @param isHighShots
     * @return boolean
     */
    boolean  updateIsHighShotsByOid(String oid, Integer isHighShots);

    /**
     *  根据 OID 删除数据
     * @param oid the practice book oid 教辅 OId
     * @return {@link AjaxResult }
     */
    AjaxResult deleteByOid( String oid);

    /**
     *  提交教辅
     * @param oid the oid 教辅 OID
     * @return {@link AjaxResult }
     */
    AjaxResult commit(String oid);

    /**
     *  审核教辅
     * @param oid the oid 教辅 OID
     * @param reviewStatus the review status 状态
     * @param reviewStatus the review comment 审核备注
     * @return {@link AjaxResult }
     */
    AjaxResult review(String oid, Integer reviewStatus, String reviewComment);

    /**
     * 更改状态
     * @param oid the oid 教辅 OID
     * @param status the status 状态
     * @return {@link AjaxResult }
     */
    AjaxResult updateStatusByOid(String oid, Integer status);
}

