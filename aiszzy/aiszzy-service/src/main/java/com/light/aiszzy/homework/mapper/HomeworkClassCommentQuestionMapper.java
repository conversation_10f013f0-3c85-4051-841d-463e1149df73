package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkClassCommentQuestionDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassCommentQuestionVo;

/**
 * 班级作业讲评题目Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface HomeworkClassCommentQuestionMapper extends BaseMapper<HomeworkClassCommentQuestionDto> {

	List<HomeworkClassCommentQuestionVo> getHomeworkClassCommentQuestionListByCondition(HomeworkClassCommentQuestionConditionBo condition);

}
