package com.light.aiszzy.xkw.xkwCourses.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 课程
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-03 10:43:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xkw_courses")
public class XkwCoursesDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 课程名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 学科ID
	 */
	@TableField("subject_id")
	private Long subjectId;

	/**
	 * 学段ID
	 */
	@TableField("stage_id")
	private Long stageId;

	/**
	 * 排序值
	 */
	@TableField("ordinal")
	private Long ordinal;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除（1：正常 2：删除）
	 */
	@TableField("is_delete")
	private Long isDelete;

}
