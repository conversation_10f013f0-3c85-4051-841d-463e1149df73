package com.light.aiszzy.hwlOpen.tal.ailab.util;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;

public class DateUtil {

    public static final String DATE_FORMAT_YYYY = "yyyy";

    /**
     * 获取当前时间（东8区）
     * @return
     */
    public static Date getCurrentDate(){
        LocalDateTime localDateTime = LocalDateTime.now(ZoneOffset.of("+8"));
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    /**
     * 获取当前时间（东8区）
     * @return
     */
    public static String getDateStr(String dateFormat){
        Date date = getCurrentDate();

        return new SimpleDateFormat(dateFormat).format(date);
    }

}
