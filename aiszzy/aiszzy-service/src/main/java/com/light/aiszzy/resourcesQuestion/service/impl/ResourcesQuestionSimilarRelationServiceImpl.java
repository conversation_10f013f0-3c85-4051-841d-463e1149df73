package com.light.aiszzy.resourcesQuestion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.resourcesQuestion.entity.dto.ResourcesQuestionSimilarRelationDto;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationConditionBo;
import com.light.aiszzy.resourcesQuestion.entity.bo.ResourcesQuestionSimilarRelationBo;
import com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionSimilarRelationVo;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionSimilarRelationService;
import com.light.aiszzy.resourcesQuestion.mapper.ResourcesQuestionSimilarRelationMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资源库题目相似题接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class ResourcesQuestionSimilarRelationServiceImpl extends ServiceImpl<ResourcesQuestionSimilarRelationMapper, ResourcesQuestionSimilarRelationDto> implements IResourcesQuestionSimilarRelationService {

	@Resource
	private ResourcesQuestionSimilarRelationMapper resourcesQuestionSimilarRelationMapper;
	
    @Override
	public List<ResourcesQuestionSimilarRelationVo> getResourcesQuestionSimilarRelationListByCondition(ResourcesQuestionSimilarRelationConditionBo condition) {
        return resourcesQuestionSimilarRelationMapper.getResourcesQuestionSimilarRelationListByCondition(condition);
	}

	@Override
	public AjaxResult addResourcesQuestionSimilarRelation(ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo) {
		ResourcesQuestionSimilarRelationDto resourcesQuestionSimilarRelation = new ResourcesQuestionSimilarRelationDto();
		BeanUtils.copyProperties(resourcesQuestionSimilarRelationBo, resourcesQuestionSimilarRelation);
		resourcesQuestionSimilarRelation.setIsDelete(StatusEnum.NOTDELETE.getCode());
		resourcesQuestionSimilarRelation.setOid(IdUtil.simpleUUID());
		if(save(resourcesQuestionSimilarRelation)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateResourcesQuestionSimilarRelation(ResourcesQuestionSimilarRelationBo resourcesQuestionSimilarRelationBo) {
		LambdaQueryWrapper<ResourcesQuestionSimilarRelationDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ResourcesQuestionSimilarRelationDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(ResourcesQuestionSimilarRelationDto::getOid, resourcesQuestionSimilarRelationBo.getOid());
		ResourcesQuestionSimilarRelationDto resourcesQuestionSimilarRelation = getOne(lqw);
		Long id = resourcesQuestionSimilarRelation.getId();
		if(resourcesQuestionSimilarRelation == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(resourcesQuestionSimilarRelationBo, resourcesQuestionSimilarRelation);
		resourcesQuestionSimilarRelation.setId(id);
		if(updateById(resourcesQuestionSimilarRelation)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ResourcesQuestionSimilarRelationVo getDetail(String oid) {
		LambdaQueryWrapper<ResourcesQuestionSimilarRelationDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ResourcesQuestionSimilarRelationDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(ResourcesQuestionSimilarRelationDto::getOid, oid);
		ResourcesQuestionSimilarRelationDto resourcesQuestionSimilarRelation = getOne(lqw);
	    ResourcesQuestionSimilarRelationVo resourcesQuestionSimilarRelationVo = new ResourcesQuestionSimilarRelationVo();
		if(resourcesQuestionSimilarRelation != null){
			BeanUtils.copyProperties(resourcesQuestionSimilarRelation, resourcesQuestionSimilarRelationVo);
		}
		return resourcesQuestionSimilarRelationVo;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveResourcesQuestionSimilarRef(String oid, List<String> similarQuestionsOidList) {
		if (CollUtil.isEmpty(similarQuestionsOidList)) {
			return false;
		}
		// 查询原关联数据，如已有进行排除
		Map<String, ResourcesQuestionSimilarRelationDto> similarQuestionMap = this.queryMapBySimilarQuestionsOidList(oid, similarQuestionsOidList);

		// 排除已存在 并转换为 DTO
		List<ResourcesQuestionSimilarRelationDto> resourceQuestionSimilarRefDTOList = similarQuestionsOidList.stream().filter(x -> !similarQuestionMap.containsKey(x))
				.map(x -> {
					ResourcesQuestionSimilarRelationDto dto = new ResourcesQuestionSimilarRelationDto();
					dto.setOid(IdUtil.simpleUUID());
					dto.setOriginalResourcesQuestionOid(oid);
					dto.setSimilarResourcesQuestionOid(x);
					return dto;
				}).collect(Collectors.toList());

		if(CollUtil.isNotEmpty(resourceQuestionSimilarRefDTOList)) {
			return this.saveBatch(resourceQuestionSimilarRefDTOList);
		}

		return false;
	}


	/**
	 *  根据原题 OID 、相似题 OID 集合查询数据
	 * @param originalResourcesQuestionOid 原题 OID
	 * @param similarQuestionsOidList 相似题 OID 集合
	 * @return {@link List }<{@link ResourcesQuestionSimilarRelationDto }>
	 */
	public List<ResourcesQuestionSimilarRelationDto> queryBySimilarQuestionsOidList(String originalResourcesQuestionOid, List<String> similarQuestionsOidList) {
		QueryWrapper<ResourcesQuestionSimilarRelationDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(ResourcesQuestionSimilarRelationDto::getOid, originalResourcesQuestionOid);
		queryWrapper.lambda().in(ResourcesQuestionSimilarRelationDto::getSimilarResourcesQuestionOid, similarQuestionsOidList);
		queryWrapper.lambda().in(ResourcesQuestionSimilarRelationDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		return this.list(queryWrapper);
	}


	/**
	 *  根据原题 OID 、相似题 OID 集合查询map数据
	 * @param originalResourcesQuestionOid 原题 OID
	 * @param similarQuestionsOidList 相似题 OID 集合
	 * @return {@link Map }<{@link String }, {@link ResourcesQuestionSimilarRelationDto }>
	 */
	public Map<String, ResourcesQuestionSimilarRelationDto> queryMapBySimilarQuestionsOidList(String originalResourcesQuestionOid, List<String> similarQuestionsOidList) {
		List<ResourcesQuestionSimilarRelationDto> resourcesQuestionSimilarRelationDtos = queryBySimilarQuestionsOidList(originalResourcesQuestionOid, similarQuestionsOidList);
		if(CollUtil.isEmpty(resourcesQuestionSimilarRelationDtos)){
			return  Maps.newHashMap();
		}
		return resourcesQuestionSimilarRelationDtos.stream().collect(Collectors.toMap(ResourcesQuestionSimilarRelationDto::getSimilarResourcesQuestionOid, x-> x));
	}
}