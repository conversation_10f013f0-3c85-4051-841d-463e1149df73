package com.light.aiszzy.schoolBook.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.schoolBook.entity.dto.SchoolBookRecordDto;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookRecordVo;
import com.light.aiszzy.schoolBook.service.ISchoolBookRecordService;
import com.light.aiszzy.schoolBook.mapper.SchoolBookRecordMapper;
import com.light.core.entity.AjaxResult;
/**
 * 教辅或作业本开通记录详情表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@Service
public class SchoolBookRecordServiceImpl extends ServiceImpl<SchoolBookRecordMapper, SchoolBookRecordDto> implements ISchoolBookRecordService {

	@Resource
	private SchoolBookRecordMapper schoolBookRecordMapper;
	
    @Override
	public List<SchoolBookRecordVo> getSchoolBookRecordListByCondition(SchoolBookRecordConditionBo condition) {
        return schoolBookRecordMapper.getSchoolBookRecordListByCondition(condition);
	}

	@Override
	public AjaxResult addSchoolBookRecord(SchoolBookRecordBo schoolBookRecordBo) {
		SchoolBookRecordDto schoolBookRecord = new SchoolBookRecordDto();
		BeanUtils.copyProperties(schoolBookRecordBo, schoolBookRecord);
		schoolBookRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
		schoolBookRecord.setOid(IdUtil.simpleUUID());
		if(save(schoolBookRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateSchoolBookRecord(SchoolBookRecordBo schoolBookRecordBo) {
		LambdaQueryWrapper<SchoolBookRecordDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolBookRecordDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolBookRecordDto::getOid, schoolBookRecordBo.getOid());
		SchoolBookRecordDto schoolBookRecord = getOne(lqw);
		Long id = schoolBookRecord.getId();
		if(schoolBookRecord == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(schoolBookRecordBo, schoolBookRecord);
		schoolBookRecord.setId(id);
		if(updateById(schoolBookRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public SchoolBookRecordVo getDetail(String oid) {
		LambdaQueryWrapper<SchoolBookRecordDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolBookRecordDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolBookRecordDto::getOid, oid);
		SchoolBookRecordDto schoolBookRecord = getOne(lqw);
	    SchoolBookRecordVo schoolBookRecordVo = new SchoolBookRecordVo();
		if(schoolBookRecord != null){
			BeanUtils.copyProperties(schoolBookRecord, schoolBookRecordVo);
		}
		return schoolBookRecordVo;
	}

}