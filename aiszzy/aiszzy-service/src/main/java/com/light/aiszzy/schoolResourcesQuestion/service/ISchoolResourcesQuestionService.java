package com.light.aiszzy.schoolResourcesQuestion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.schoolResourcesQuestion.entity.dto.SchoolResourcesQuestionDto;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionConditionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 资源库题目表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface ISchoolResourcesQuestionService extends IService<SchoolResourcesQuestionDto> {

    List<SchoolResourcesQuestionVo> getSchoolResourcesQuestionListByCondition(SchoolResourcesQuestionConditionBo condition);

	AjaxResult addSchoolResourcesQuestion(SchoolResourcesQuestionBo schoolResourcesQuestionBo);

	AjaxResult updateSchoolResourcesQuestion(SchoolResourcesQuestionBo schoolResourcesQuestionBo);

    SchoolResourcesQuestionVo getDetail(String oid);
}

