package com.light.aiszzy.xkw.xkwTextbook.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.light.aiszzy.xkw.xkwTextbook.api.XkwTextbookApi;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookConditionBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.vo.XkwTextbookVo;
import com.light.aiszzy.xkw.xkwTextbook.service.IXkwTextbookService;
import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;



import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 教材
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@RestController
@Validated
@Api(value = "", tags = "教材接口")
public class XkwTextbookController implements XkwTextbookApi {

    @Autowired
    private IXkwTextbookService xkwTextbookService;

    public AjaxResult<PageInfo<XkwTextbookVo>> getXkwTextbookPageListByCondition(@RequestBody XkwTextbookConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<XkwTextbookVo> pageInfo = new PageInfo<>(xkwTextbookService.getXkwTextbookListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<XkwTextbookVo>> getXkwTextbookListByCondition(@RequestBody XkwTextbookConditionBo condition) {
        return AjaxResult.success(xkwTextbookService.getXkwTextbookListByCondition(condition));
    }


}
