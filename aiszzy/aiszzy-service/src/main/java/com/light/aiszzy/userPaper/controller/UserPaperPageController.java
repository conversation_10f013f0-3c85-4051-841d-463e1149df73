package com.light.aiszzy.userPaper.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.userPaper.entity.bo.UserPaperPageConditionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo;
import com.light.aiszzy.userPaper.service.IUserPaperPageService;

import com.light.aiszzy.userPaper.api.UserPaperPageApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 用户上传每页图片表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "用户上传每页图片表接口")
public class UserPaperPageController implements UserPaperPageApi {

    @Autowired
    private IUserPaperPageService userPaperPageService;

    public AjaxResult<PageInfo<UserPaperPageVo>> getUserPaperPagePageListByCondition(@RequestBody UserPaperPageConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<UserPaperPageVo> pageInfo = new PageInfo<>(userPaperPageService.getUserPaperPageListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<UserPaperPageVo>> getUserPaperPageListByCondition(@RequestBody UserPaperPageConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(userPaperPageService.getUserPaperPageListByCondition(condition));
    }

    public AjaxResult addUserPaperPage(@Validated @RequestBody UserPaperPageBo userPaperPageBo) {
        return userPaperPageService.addUserPaperPage(userPaperPageBo);
    }

    public AjaxResult updateUserPaperPage(@Validated @RequestBody UserPaperPageBo userPaperPageBo) {
        if (null == userPaperPageBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return userPaperPageService.updateUserPaperPage(userPaperPageBo);
    }

    public AjaxResult<UserPaperPageVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(userPaperPageService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            UserPaperPageBo userPaperPageBo = new UserPaperPageBo();
            userPaperPageBo.setOid(oid);
            userPaperPageBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return userPaperPageService.updateUserPaperPage(userPaperPageBo);
    }

    @Override
    public AjaxResult deleteByUserPaperOid(@PathVariable("userPaperOid") String userPaperOid) {
        return AjaxResult.success( this.userPaperPageService.deleteByUserPaperOid(userPaperOid));
    }

    @Override
    public AjaxResult saveBatchByUserPaperOid(@PathVariable("userPaperOid") String userPaperOid, @RequestBody List<UserPaperPageBo> list) {
        return this.userPaperPageService.saveBatchByUserPaperOid(userPaperOid, list);
    }

    @Override
    public AjaxResult<List<UserPaperPageVo>> queryByUserPaperOid(@PathVariable("userPaperOid") String userPaperOid) {
        List<UserPaperPageVo> userPaperPageVos = this.userPaperPageService.queryByUserPaperOid(userPaperOid);
        return AjaxResult.success(userPaperPageVos) ;
    }
}
