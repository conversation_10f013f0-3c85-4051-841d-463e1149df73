package com.light.aiszzy.practiceBook.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 教辅信息审核
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("practice_book_review")
public class PracticeBookReviewDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 关联的教辅OID
	 */
	@TableField("practice_book_oid")
	private String practiceBookOid;

	/**
	 * 审核意见
	 */
	@TableField("comment")
	private String comment;

	/**
	 * 审核状态，1未提交，2审核中 3审核成功 4打回
	 */
	@TableField("status")
	private Long status;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
