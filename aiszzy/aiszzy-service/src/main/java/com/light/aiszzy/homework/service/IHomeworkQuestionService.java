package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkQuestionDto;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本作业题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IHomeworkQuestionService extends IService<HomeworkQuestionDto> {

    List<HomeworkQuestionVo> getHomeworkQuestionListByCondition(HomeworkQuestionConditionBo condition);

    AjaxResult addHomeworkQuestion(HomeworkQuestionBo homeworkQuestionBo);

    AjaxResult updateHomeworkQuestion(HomeworkQuestionBo homeworkQuestionBo);

    HomeworkQuestionVo getDetail(String oid);

    AjaxResult changeQuestion(String oid, String oldQuestionOid, String newQuestionOid);

    AjaxResult deleteQuestion(String oid,String questionOid);

}

