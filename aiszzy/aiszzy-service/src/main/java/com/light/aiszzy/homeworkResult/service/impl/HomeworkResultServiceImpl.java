package com.light.aiszzy.homeworkResult.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultService;
import com.light.aiszzy.homeworkResult.mapper.HomeworkResultMapper;
import com.light.core.entity.AjaxResult;
/**
 * 校本作业结果表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class HomeworkResultServiceImpl extends ServiceImpl<HomeworkResultMapper, HomeworkResultDto> implements IHomeworkResultService {

	@Resource
	private HomeworkResultMapper homeworkResultMapper;
	
    @Override
	public List<HomeworkResultVo> getHomeworkResultListByCondition(HomeworkResultConditionBo condition) {
        return homeworkResultMapper.getHomeworkResultListByCondition(condition);
	}

	@Override
	public List<HomeworkResultVo> getDuplicateHomeworkResultListByCondition(HomeworkResultConditionBo condition) {

		return homeworkResultMapper.getDuplicateHomeworkResultListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkResult(HomeworkResultBo homeworkResultBo) {
		HomeworkResultDto homeworkResult = new HomeworkResultDto();
		BeanUtils.copyProperties(homeworkResultBo, homeworkResult);
		homeworkResult.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkResult.setOid(IdUtil.simpleUUID());
		if(save(homeworkResult)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkResult(HomeworkResultBo homeworkResultBo) {
		LambdaQueryWrapper<HomeworkResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultDto::getOid, homeworkResultBo.getOid());
		HomeworkResultDto homeworkResult = getOne(lqw);
		Long id = homeworkResult.getId();
		if(homeworkResult == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkResultBo, homeworkResult);
		homeworkResult.setId(id);
		if(updateById(homeworkResult)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkResultVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultDto::getOid, oid);
		HomeworkResultDto homeworkResult = getOne(lqw);
	    HomeworkResultVo homeworkResultVo = new HomeworkResultVo();
		if(homeworkResult != null){
			BeanUtils.copyProperties(homeworkResult, homeworkResultVo);
		}
		return homeworkResultVo;
	}

}