package com.light.aiszzy.xkw.xkwTextbookCatalog.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 教材目录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-21 16:06:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xkw_textbook_catalog")
public class XkwTextbookCatalogDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 教材ID
	 */
	@TableField("textbook_id")
	private Long textbookId;

	/**
	 * 排序值
	 */
	@TableField("ordinal")
	private Long ordinal;

	/**
	 * 父节点ID
	 */
	@TableField("parent_id")
	private Long parentId;

	/**
	 * 节点类型，分为实节点和虚节点（真实教材目录不存在的节点，例如：单元综合与测试）,可用值:VIRTUAL,REA
	 */
	@TableField("type")
	private String type;

	/**
	 * 节点名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

}
