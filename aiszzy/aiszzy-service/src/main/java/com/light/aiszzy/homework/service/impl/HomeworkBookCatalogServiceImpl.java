package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.aiszzy.homework.entity.bo.HomeworkConditionBo;
import com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogInfoDto;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.aiszzy.homework.mapper.HomeworkBookCatalogInfoMapper;
import com.light.aiszzy.homework.mapper.HomeworkMapper;
import com.light.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogVo;
import com.light.aiszzy.homework.service.IHomeworkBookCatalogService;
import com.light.aiszzy.homework.mapper.HomeworkBookCatalogMapper;
import com.light.core.entity.AjaxResult;
/**
 * 作业本目录接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class HomeworkBookCatalogServiceImpl extends ServiceImpl<HomeworkBookCatalogMapper, HomeworkBookCatalogDto> implements IHomeworkBookCatalogService {

	@Resource
	private HomeworkBookCatalogMapper homeworkBookCatalogMapper;

	@Resource
	private HomeworkMapper homeworkMapper;

	@Resource
	private HomeworkBookCatalogInfoMapper homeworkBookCatalogInfoMapper;

	@Override
	public List<HomeworkBookCatalogVo> getHomeworkBookCatalogListByCondition(HomeworkBookCatalogConditionBo condition) {
        return homeworkBookCatalogMapper.getHomeworkBookCatalogListByCondition(condition);
	}

	public AjaxResult listAllChild(HomeworkBookCatalogConditionBo condition) {
		Map<String, Object> result = new HashMap<String, Object>(4);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<HomeworkBookCatalogVo> homeworkBookCatalogListByCondition = homeworkBookCatalogMapper.getHomeworkBookCatalogListByCondition(condition);
		result.put("homeworkBookCatalogVo", homeworkBookCatalogListByCondition);

		for (HomeworkBookCatalogVo vo : homeworkBookCatalogListByCondition) {
			HomeworkBookCatalogConditionBo childBo = new HomeworkBookCatalogConditionBo();
			childBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
			childBo.setParentOid(vo.getOid());
			childBo.setHomeworkBookOid(condition.getHomeworkBookOid());
			List<HomeworkBookCatalogVo> child = homeworkBookCatalogMapper.getHomeworkBookCatalogListByCondition(childBo);
			HomeworkConditionBo childHomeworkbo = new HomeworkConditionBo();
			childHomeworkbo.setHasBind("yes");
			childHomeworkbo.setHomeworkBookOid(childBo.getHomeworkBookOid());
			childHomeworkbo.setHomeworkBookCatalogOid(childBo.getParentOid());
			childHomeworkbo.setIsDelete(StatusEnum.NOTDELETE.getCode());
			List<HomeworkVo> homeworkChildListByCondition = homeworkMapper.getHomeworkListByCondition(childHomeworkbo);
			vo.setHasChildCount(child.size() + homeworkChildListByCondition.size());
		}

		HomeworkConditionBo bo = new HomeworkConditionBo();
		bo.setHasBind("yes");
		bo.setHomeworkBookOid(condition.getHomeworkBookOid());
		bo.setHomeworkBookCatalogOid(condition.getParentOid());

		if (StringUtils.isEmpty(bo.getHomeworkBookCatalogOid())) {
			result.put("homeworkVo", new ArrayList());
			return AjaxResult.success(result);
		}
		bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		bo.setOrderBy("order_num asc");
		List<HomeworkVo> homeworkListByCondition = homeworkMapper.getHomeworkListByCondition(bo);
		result.put("homeworkVo", homeworkListByCondition);
		return AjaxResult.success(result);
	}

	@Override
	public AjaxResult listAllChildByBook(HomeworkBookCatalogConditionBo homeworkBookCatalogBo) {
		homeworkBookCatalogBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<HomeworkBookCatalogVo> homeworkBookCatalogListByCondition = homeworkBookCatalogMapper.getHomeworkBookCatalogListByCondition(homeworkBookCatalogBo);

		for (HomeworkBookCatalogVo vo : homeworkBookCatalogListByCondition) {
			HomeworkConditionBo bo = new HomeworkConditionBo();
			bo.setHasBind("yes");
			bo.setHomeworkBookOid(homeworkBookCatalogBo.getHomeworkBookOid());
			bo.setHomeworkBookCatalogOid(vo.getOid());

			bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
			bo.setOrderBy("order_num asc");
			List<HomeworkVo> homeworkListByCondition = homeworkMapper.getHomeworkListByCondition(bo);
			vo.setHomeworkList(homeworkListByCondition);
		}
		return AjaxResult.success(homeworkBookCatalogListByCondition);

	}

	@Override
	public AjaxResult sortHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo) {
		LambdaQueryWrapper<HomeworkBookCatalogDto> draglqw = new LambdaQueryWrapper<>();
		draglqw.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		draglqw.eq(HomeworkBookCatalogDto::getOid, homeworkBookCatalogBo.getDragOid());
		HomeworkBookCatalogDto drag = getOne(draglqw);
		if (StringUtils.isEmpty(homeworkBookCatalogBo.getDragPreOid())) {

			if (drag != null && !drag.getParentOid().equals(homeworkBookCatalogBo.getParentOid())) {
				drag.setParentOid(homeworkBookCatalogBo.getParentOid());
			}
			drag.setOrderNum(0L);
			updateById(drag);
			LambdaQueryWrapper<HomeworkBookCatalogDto> changelqw = new LambdaQueryWrapper<>();
			changelqw.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
			changelqw.eq(HomeworkBookCatalogDto::getHomeworkBookOid, homeworkBookCatalogBo.getHomeworkBookOid());
			changelqw.eq(HomeworkBookCatalogDto::getParentOid, homeworkBookCatalogBo.getParentOid());
			changelqw.orderByAsc(HomeworkBookCatalogDto::getOrderNum);
			List<HomeworkBookCatalogDto> list = list(changelqw);
			Long dragPreNum = 1L;
			List<HomeworkBookCatalogDto> arr = new ArrayList<>();
			if (CollectionUtil.isNotEmpty(list)) {
				for (HomeworkBookCatalogDto homeworkBookCatalogDto : list) {
					if (!homeworkBookCatalogDto.getOid().equals(homeworkBookCatalogBo.getDragOid())) {
						homeworkBookCatalogDto.setOrderNum(dragPreNum + 1);
						arr.add(homeworkBookCatalogDto);
						dragPreNum++;
					}
				}
			}
			if (CollectionUtil.isNotEmpty(arr)) {
				updateBatchById(arr);
			}
			return AjaxResult.success("保存成功");
		}
		LambdaQueryWrapper<HomeworkBookCatalogDto> dragPrelqw = new LambdaQueryWrapper<>();
		dragPrelqw.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		dragPrelqw.eq(HomeworkBookCatalogDto::getOid, homeworkBookCatalogBo.getDragPreOid());
		HomeworkBookCatalogDto dragPre = getOne(dragPrelqw);
		Long dragPreNum = dragPre.getOrderNum() + 1;
		LambdaQueryWrapper<HomeworkBookCatalogDto> changelqw = new LambdaQueryWrapper<>();
		changelqw.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		changelqw.eq(HomeworkBookCatalogDto::getHomeworkBookOid, dragPre.getHomeworkBookOid());
		changelqw.eq(HomeworkBookCatalogDto::getParentOid, dragPre.getParentOid());
		changelqw.ge(HomeworkBookCatalogDto::getOrderNum, dragPreNum);
		changelqw.orderByAsc(HomeworkBookCatalogDto::getOrderNum);
		List<HomeworkBookCatalogDto> list = list(changelqw);
		List<HomeworkBookCatalogDto> arr = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(list)) {
			for (HomeworkBookCatalogDto homeworkBookCatalogDto : list) {
				if (!homeworkBookCatalogDto.getOid().equals(homeworkBookCatalogBo.getDragOid())) {
					homeworkBookCatalogDto.setOrderNum(dragPreNum + 1);
					arr.add(homeworkBookCatalogDto);
					dragPreNum++;
				}
			}
		}
		if (drag != null && !drag.getParentOid().equals(homeworkBookCatalogBo.getParentOid())) {
			drag.setParentOid(homeworkBookCatalogBo.getParentOid());
		}
		drag.setOrderNum(dragPre.getOrderNum() + 1);
		arr.add(drag);
		updateBatchById(arr);
		return AjaxResult.success("保存成功");
	}

	@Override
	public AjaxResult addHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo) {
		HomeworkBookCatalogDto homeworkBookCatalog = new HomeworkBookCatalogDto();
		BeanUtils.copyProperties(homeworkBookCatalogBo, homeworkBookCatalog);
		homeworkBookCatalog.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkBookCatalog.setOid(IdUtil.simpleUUID());
		if(!homeworkBookCatalogBo.getParentOid().equals("0")){
			int count = count(new LambdaUpdateWrapper<HomeworkBookCatalogDto>()
					.eq(HomeworkBookCatalogDto::getParentOid, homeworkBookCatalogBo.getParentOid())
					.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

			Long orderNum = 1L;
			homeworkBookCatalog.setOrderNum(orderNum + count);
		}

		if (save(homeworkBookCatalog)) {
			if(homeworkBookCatalogBo.getParentOid().equals("0")){
				homeworkBookCatalog.setOrderNum(homeworkBookCatalog.getId());
				updateById(homeworkBookCatalog);
			}
			return AjaxResult.success(homeworkBookCatalog);
		} else {
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo) {
		LambdaQueryWrapper<HomeworkBookCatalogDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkBookCatalogDto::getOid, homeworkBookCatalogBo.getOid());
		HomeworkBookCatalogDto homeworkBookCatalog = getOne(lqw);
		Long id = homeworkBookCatalog.getId();
		if(homeworkBookCatalog == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkBookCatalogBo, homeworkBookCatalog);
		homeworkBookCatalog.setId(id);
		if(updateById(homeworkBookCatalog)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult deleteHomeworkBookCatalog(HomeworkBookCatalogBo homeworkBookCatalogBo) {
		List<HomeworkBookCatalogInfoDto> infoList = new ArrayList<>();
		infoList(homeworkBookCatalogBo.getOid(), infoList);
		if (CollectionUtil.isNotEmpty(infoList)) {
			return AjaxResult.fail("已经绑定作业不能删除目录");
		}

		deleteByParent(homeworkBookCatalogBo.getOid());

		return AjaxResult.success("删除成功");
	}

	public void deleteByParent(String oid) {
		List<HomeworkBookCatalogDto> list = list(new LambdaQueryWrapper<HomeworkBookCatalogDto>()
				.eq(HomeworkBookCatalogDto::getParentOid, oid)
				.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
		if (CollectionUtil.isNotEmpty(list)) {
			for (HomeworkBookCatalogDto homeworkBookCatalogDto : list) {
				deleteByParent(homeworkBookCatalogDto.getOid());
			}
		}
		update(null,new LambdaUpdateWrapper<HomeworkBookCatalogDto>()
				.set(HomeworkBookCatalogDto::getIsDelete, StatusEnum.ISDELETE.getCode())
				.eq(HomeworkBookCatalogDto::getOid,oid));
	}

	public void infoList(String oid,List<HomeworkBookCatalogInfoDto> infoList) {
		List<HomeworkBookCatalogInfoDto> homeworkDtos = homeworkBookCatalogInfoMapper.selectList(new LambdaQueryWrapper<HomeworkBookCatalogInfoDto>()
				.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.eq(HomeworkBookCatalogInfoDto::getHomeworkBookCatalogOid, oid));
		if (CollectionUtil.isNotEmpty(homeworkDtos)) {
			infoList.addAll(homeworkDtos);
			return;
		}
		List<HomeworkBookCatalogDto> list = list(new LambdaQueryWrapper<HomeworkBookCatalogDto>()
				.eq(HomeworkBookCatalogDto::getParentOid, oid)
				.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
		if (CollectionUtil.isNotEmpty(list)) {
			for (HomeworkBookCatalogDto homeworkBookCatalogDto : list) {
				infoList(homeworkBookCatalogDto.getOid(), infoList);
			}
		}
	}

	@Override
	public AjaxResult deleteByBookOid(String oid) {
		HomeworkConditionBo childHomeworkbo = new HomeworkConditionBo();
		childHomeworkbo.setHasBind("yes");
		childHomeworkbo.setHomeworkBookOid(oid);
		childHomeworkbo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<HomeworkVo> homeworkChildListByCondition = homeworkMapper.getHomeworkListByCondition(childHomeworkbo);

		if (CollectionUtil.isNotEmpty(homeworkChildListByCondition)) {
			return AjaxResult.fail("存在作业已经绑定");
		}

		LambdaUpdateWrapper<HomeworkBookCatalogDto> udp = new LambdaUpdateWrapper<>();
		udp.eq(HomeworkBookCatalogDto::getOid, oid);
		udp.set(HomeworkBookCatalogDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		update(null,udp);
		return AjaxResult.fail("保存失败");
	}

	@Override
	public HomeworkBookCatalogVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkBookCatalogDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkBookCatalogDto::getOid, oid);
		HomeworkBookCatalogDto homeworkBookCatalog = getOne(lqw);
	    HomeworkBookCatalogVo homeworkBookCatalogVo = new HomeworkBookCatalogVo();
		if(homeworkBookCatalog != null){
			BeanUtils.copyProperties(homeworkBookCatalog, homeworkBookCatalogVo);
		}
		return homeworkBookCatalogVo;
	}

}