package com.light.aiszzy.xkw.xkwPaperInfo.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.light.aiszzy.question.entity.bo.QuestionBo;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoConditionBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.vo.XkwPaperInfoVo;
import com.light.aiszzy.xkw.xkwPaperInfo.service.IXkwPaperInfoService;

import com.light.aiszzy.xkw.xkwPaperInfo.api.XkwPaperInfoApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 题目信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
@RestController
@Validated
@Api(value = "", tags = "题目信息接口")
public class XkwPaperInfoController implements XkwPaperInfoApi {

    @Autowired
    private IXkwPaperInfoService xkwPaperInfoService;

    public AjaxResult<PageInfo<XkwPaperInfoVo>> getXkwPaperInfoPageListByCondition(@RequestBody XkwPaperInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<XkwPaperInfoVo> pageInfo = new PageInfo<>(xkwPaperInfoService.getXkwPaperInfoListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<XkwPaperInfoVo>> getXkwPaperInfoListByCondition(@RequestBody XkwPaperInfoConditionBo condition) {
        return AjaxResult.success(xkwPaperInfoService.getXkwPaperInfoListByCondition(condition));
    }

    public AjaxResult addXkwPaperInfo(QuestionBo bo) {
        return xkwPaperInfoService.addXkwPaperInfo(bo);
    }

    @Override
    public AjaxResult editXkwPaperInfo(QuestionBo bo) {
        return xkwPaperInfoService.editXkwPaperInfo(bo);
    }

    public AjaxResult updateXkwPaperInfo(@Validated @RequestBody XkwPaperInfoBo xkwPaperInfoBo) {
        if (null == xkwPaperInfoBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return xkwPaperInfoService.updateXkwPaperInfo(xkwPaperInfoBo);
    }

    public AjaxResult<XkwPaperInfoVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(xkwPaperInfoService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            XkwPaperInfoBo xkwPaperInfoBo = new XkwPaperInfoBo();
            xkwPaperInfoBo.setOid(oid);
            xkwPaperInfoBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return xkwPaperInfoService.updateXkwPaperInfo(xkwPaperInfoBo);
    }
}
