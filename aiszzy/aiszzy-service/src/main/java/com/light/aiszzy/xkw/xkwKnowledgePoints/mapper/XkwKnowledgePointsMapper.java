package com.light.aiszzy.xkw.xkwKnowledgePoints.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsConditionBo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.dto.XkwKnowledgePointsDto;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo;
import org.apache.ibatis.annotations.Param;


/**
 * 知识树Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
public interface XkwKnowledgePointsMapper extends BaseMapper<XkwKnowledgePointsDto> {

	List<XkwKnowledgePointsVo> getXkwKnowledgePointsListByCondition(XkwKnowledgePointsConditionBo condition);

	List<XkwKnowledgePointsVo> getKnowledgeByIds(@Param("ids") List<String> ids);

}
