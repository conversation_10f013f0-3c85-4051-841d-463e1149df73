package com.light.aiszzy.device.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordConditionBo;
import com.light.aiszzy.device.entity.vo.DeviceBindRecordVo;
import com.light.contants.AISzzyConstants;
import com.light.core.exception.WarningException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import com.light.aiszzy.device.entity.dto.DeviceDto;
import com.light.aiszzy.device.entity.dto.DeviceBindRecordDto;
import com.light.aiszzy.device.entity.bo.DeviceConditionBo;
import com.light.aiszzy.device.entity.bo.DeviceBo;

import com.light.aiszzy.device.entity.bo.DeviceBindRecordBo;
import com.light.aiszzy.device.entity.vo.DeviceVo;
import com.light.aiszzy.device.service.IDeviceService;
import com.light.aiszzy.device.service.IDeviceBindRecordService;
import com.light.aiszzy.device.mapper.DeviceMapper;
import com.light.core.entity.AjaxResult;
import com.light.enums.DeviceActivationStatusEnum;
import com.light.enums.DeviceStatusEnum;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设备表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@Slf4j
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, DeviceDto> implements IDeviceService {

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private IDeviceBindRecordService deviceBindRecordService;
    @Resource
    private IDeviceService deviceService;

    @Override
    public List<DeviceVo> getDeviceListByCondition(DeviceConditionBo condition) {
        return deviceMapper.getDeviceListByCondition(condition);
    }

    @Override
    public AjaxResult addDevice(DeviceBo deviceBo) {
        DeviceDto device = new DeviceDto();
        BeanUtils.copyProperties(deviceBo, device);
        device.setIsDelete(StatusEnum.NOTDELETE.getCode());
        device.setOid(IdUtil.simpleUUID());
        // 自动生成软件激活码（6位：数字+大写字母）
        device.setActivationCode(generateUniqueActivationCode());

        if (save(device)) {
            // 同步创建设备绑定记录
            if (StringUtils.isNotBlank(deviceBo.getOrgCode())) {
                DeviceBindRecordBo bindRecordBo = new DeviceBindRecordBo();
                bindRecordBo.setDeviceOid(device.getOid());
                bindRecordBo.setOrgCode(deviceBo.getOrgCode());
                bindRecordBo.setOrgName(deviceBo.getOrgName());
                bindRecordBo.setOrgAreaName(deviceBo.getOrgAreaName());
                bindRecordBo.setOrgAreaCode(deviceBo.getOrgAreaCode());
                bindRecordBo.setOrgCityName(deviceBo.getOrgCityName());
                bindRecordBo.setOrgCityCode(deviceBo.getOrgCityCode());
                bindRecordBo.setOrgProvinceName(deviceBo.getOrgProvinceName());
                bindRecordBo.setOrgProvinceCode(deviceBo.getOrgProvinceCode());
                bindRecordBo.setStatus(deviceBo.getStatus());
                bindRecordBo.setDeviceMacAddress(deviceBo.getDeviceMacAddress());
                bindRecordBo.setClientVersion(deviceBo.getClientVersion());
                bindRecordBo.setExtendInfo(deviceBo.getExtendInfo());
                bindRecordBo.setCreateBy(deviceBo.getCreateBy());
                bindRecordBo.setCreateByRealName(deviceBo.getCreateByRealName());
                bindRecordBo.setUpdateBy(deviceBo.getUpdateBy());

                AjaxResult bindResult = deviceBindRecordService.addDeviceBindRecord(bindRecordBo);
                if (!bindResult.isSuccess()) {
                    return AjaxResult.fail("设备保存成功，但绑定记录创建失败");
                }
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateDevice(DeviceBo deviceBo) {
        LambdaQueryWrapper<DeviceDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(DeviceDto::getOid, deviceBo.getOid());
        DeviceDto device = getOne(lqw);

        if (device == null) {
            return AjaxResult.fail("设备不存在");
        }

        Long id = device.getId();
        String originalOrgCode = device.getOrgCode();

        BeanUtils.copyProperties(deviceBo, device);
        device.setId(id);

        if (updateById(device)) {
            // 检测orgCode是否发生变更
            String newOrgCode = deviceBo.getOrgCode();
            if (StringUtils.isNotBlank(newOrgCode) && !newOrgCode.equals(originalOrgCode)) {
                // orgCode发生变更，需要新增一条绑定记录
                DeviceBindRecordBo bindRecordBo = new DeviceBindRecordBo();
                bindRecordBo.setDeviceOid(device.getOid());
                bindRecordBo.setOrgCode(deviceBo.getOrgCode());
                bindRecordBo.setOrgName(deviceBo.getOrgName());
                bindRecordBo.setOrgAreaName(deviceBo.getOrgAreaName());
                bindRecordBo.setOrgAreaCode(deviceBo.getOrgAreaCode());
                bindRecordBo.setOrgCityName(deviceBo.getOrgCityName());
                bindRecordBo.setOrgCityCode(deviceBo.getOrgCityCode());
                bindRecordBo.setOrgProvinceName(deviceBo.getOrgProvinceName());
                bindRecordBo.setOrgProvinceCode(deviceBo.getOrgProvinceCode());
                bindRecordBo.setStatus(deviceBo.getStatus());
                bindRecordBo.setDeviceMacAddress(deviceBo.getDeviceMacAddress());
                bindRecordBo.setClientVersion(deviceBo.getClientVersion());
                bindRecordBo.setExtendInfo(deviceBo.getExtendInfo());
                bindRecordBo.setCreateBy(deviceBo.getUpdateBy());
                bindRecordBo.setCreateByRealName(deviceBo.getUpdateByRealName());
                bindRecordBo.setUpdateBy(deviceBo.getUpdateBy());

                AjaxResult bindResult = deviceBindRecordService.addDeviceBindRecord(bindRecordBo);
                if (!bindResult.isSuccess()) {
                    return AjaxResult.fail("设备更新成功，但绑定记录创建失败");
                }
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public DeviceVo getDetail(String oid) {
        LambdaQueryWrapper<DeviceDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(DeviceDto::getOid, oid);
        DeviceDto device = getOne(lqw);
        DeviceVo deviceVo = new DeviceVo();
        if (device != null) {
            BeanUtils.copyProperties(device, deviceVo);
        }
        return deviceVo;
    }

    @Override
    public DeviceVo getDetailByHardwareCode(String oid) {
        LambdaQueryWrapper<DeviceDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(DeviceDto::getHardwareCode, oid);
        DeviceDto device = getOne(lqw);
        DeviceVo deviceVo = new DeviceVo();
        if (device != null) {
            BeanUtils.copyProperties(device, deviceVo);
        }
        return deviceVo;
    }

    @Override
    public DeviceVo getDetailByActivationCode(String activationCode) {
        // 复用现有方法，只传入激活码，硬件序列号传null
        return getDetailByActivationCodeAndHardwareCode(activationCode, null);
    }

    @Override
    public DeviceVo getDetailByActivationCodeAndHardwareCode(String activationCode, String hardwareCode) {
        // 如果都为空则返回
        if (StringUtils.isBlank(activationCode) && StringUtils.isBlank(hardwareCode)) {
            return null;
        }

        LambdaQueryWrapper<DeviceDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        if (StringUtils.isNotBlank(activationCode)) {
            lqw.eq(DeviceDto::getActivationCode, activationCode);
        }
        if (StringUtils.isNotBlank(hardwareCode)) {
            lqw.eq(DeviceDto::getHardwareCode, hardwareCode);
        }
        lqw.ne(DeviceDto::getStatus, DeviceStatusEnum.DISABLED.getCode());
        DeviceDto device = getOne(lqw);
        DeviceVo deviceVo = new DeviceVo();
        if (device != null) {
            BeanUtils.copyProperties(device, deviceVo);
            return deviceVo;
        }
        return null;
    }

    @Override
    public DeviceVo getDetailByActivationCodeAndHardwareCodeForActivated(String activationCode, String hardwareCode) {
        // 如果都为空则返回
        if (StringUtils.isBlank(activationCode) && StringUtils.isBlank(hardwareCode)) {
            return null;
        }

        LambdaQueryWrapper<DeviceDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        // 只查询已激活的设备
        lqw.eq(DeviceDto::getIsActivated, DeviceActivationStatusEnum.ACTIVATED.getCode());
        if (StringUtils.isNotBlank(activationCode)) {
            lqw.eq(DeviceDto::getActivationCode, activationCode);
        }
        if (StringUtils.isNotBlank(hardwareCode)) {
            lqw.eq(DeviceDto::getHardwareCode, hardwareCode);
        }
        // 排除禁用状态的设备
        lqw.ne(DeviceDto::getStatus, DeviceStatusEnum.DISABLED.getCode());
        DeviceDto device = getOne(lqw);
        DeviceVo deviceVo = new DeviceVo();
        if (device != null) {
            BeanUtils.copyProperties(device, deviceVo);
            return deviceVo;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult activateDevice(DeviceBo deviceBo) {
        // 1. 参数校验
        if (StringUtils.isBlank(deviceBo.getActivationCode())) {
            return AjaxResult.fail("软件激活码不能为空");
        }
        if (StringUtils.isBlank(deviceBo.getHardwareCode())) {
            return AjaxResult.fail("硬件序列号不能为空");
        }
        log.info("设备激活开始，激活码：{}，硬件序列号：{}", deviceBo.getActivationCode(), deviceBo.getHardwareCode());

        // 2. 根据激活码查询设备
        DeviceVo existingDevice = getDetailByActivationCodeAndHardwareCode(deviceBo.getActivationCode(), null);
        // 2.1 如果查询为空则直接返回报错：未找到对应的设备激活码不存在
        if (existingDevice == null) {
            log.warn("设备激活失败，未找到对应的设备，激活码：{}，硬件序列号：{}", deviceBo.getActivationCode(), deviceBo.getHardwareCode());
            return AjaxResult.fail("设备激活失败，激活码错误或不存在");
        }
        // 2.2 如果不为空且设备序列号不为空且和现在不一样，且设备是已激活状态，则直接返回报错：激活码已经被其他设备使用
        if (StringUtils.isNotBlank(existingDevice.getHardwareCode())
            && !existingDevice.getHardwareCode().equals(deviceBo.getHardwareCode())
            && DeviceActivationStatusEnum.isActivated(existingDevice.getIsActivated())) {
            log.warn("设备激活失败，激活码已经被其他设备使用，激活码：{}，硬件序列号：{}", deviceBo.getActivationCode(), deviceBo.getHardwareCode());
            return AjaxResult.fail("设备激活失败，激活码已经被其他设备使用");
        }
        // 2.3 如果序列号不为空且和现在一样，且已激活，则直接返回激活成功
        if (StringUtils.isNotBlank(existingDevice.getHardwareCode())
            && existingDevice.getHardwareCode().equals(deviceBo.getHardwareCode())
            && DeviceActivationStatusEnum.isActivated(existingDevice.getIsActivated())) {
            log.info("设备已激活，无需再次激活，设备OID：{}", existingDevice.getOid());
            return AjaxResult.success("设备已激活，无需再次激活");
        }

        // 3. 执行激活操作，根据激活码查询到的记录，修改激活设备序列号，同时设置激活状态为已激活，并返回激活成功
        try {
            return performDeviceActivation(existingDevice, deviceBo);
        } catch (Exception e) {
            log.error("设备激活失败，设备OID：{}，错误：{}", existingDevice.getOid(), e.getMessage(), e);
            // 4. 其他情况返回报错
            throw new RuntimeException("设备激活失败：" + e.getMessage(), e);
        }
    }

    /**
     * 执行设备激活流程
     */
    private AjaxResult performDeviceActivation(DeviceVo existingDevice, DeviceBo deviceBo) {
        // 准备激活数据
        DeviceBo activateDeviceBo = new DeviceBo();
        BeanUtils.copyProperties(existingDevice, activateDeviceBo);

        // 设置激活状态和设备状态
        activateDeviceBo.setIsActivated(DeviceActivationStatusEnum.ACTIVATED.getCode());
        // 如果设备状态为空或禁用，设置为正常状态
        if (activateDeviceBo.getStatus() == null || DeviceStatusEnum.isDisabled(activateDeviceBo.getStatus())) {
            activateDeviceBo.setStatus(DeviceStatusEnum.NORMAL.getCode());
        }

        // 更新激活相关信息
        activateDeviceBo.setDeviceMacAddress(deviceBo.getDeviceMacAddress());
        activateDeviceBo.setClientVersion(deviceBo.getClientVersion());
        activateDeviceBo.setExtendInfo(deviceBo.getExtendInfo());
        activateDeviceBo.setUpdateBy(deviceBo.getUpdateBy());
        activateDeviceBo.setHardwareCode(deviceBo.getHardwareCode());
        activateDeviceBo.setUpdateByRealName(deviceBo.getUpdateByRealName());

        // 复用现有的updateDevice方法更新设备信息
        AjaxResult deviceUpdateResult = updateDevice(activateDeviceBo);
        if (!deviceUpdateResult.isSuccess()) {
            log.error("设备激活失败，设备信息更新失败，设备OID：{}，错误：{}", existingDevice.getOid(), deviceUpdateResult.getMsg());
            return AjaxResult.fail("设备激活失败：" + deviceUpdateResult.getMsg());
        }
        log.info("设备激活成功，设备OID：{}", existingDevice.getOid());
        return AjaxResult.success("设备激活成功");
    }

    /**
     * 更新设备绑定记录
     */
    private AjaxResult updateDeviceBindRecord(DeviceVo existingDevice, DeviceBo deviceBo) {
        try {
            // 如果设备没有绑定机构，跳过绑定记录更新
            if (StringUtils.isBlank(existingDevice.getOrgCode())) {
                log.info("设备未绑定机构，跳过绑定记录更新，设备OID：{}", existingDevice.getOid());
                return AjaxResult.success("跳过绑定记录更新");
            }

            // 复用现有接口查询绑定记录
            DeviceBindRecordConditionBo condition = new DeviceBindRecordConditionBo();
            condition.setDeviceOid(existingDevice.getOid());
            condition.setOrgCode(existingDevice.getOrgCode());
            condition.setIsDelete(StatusEnum.NOTDELETE.getCode());

            DeviceBindRecordVo bindRecord = deviceBindRecordService.getDetailByCondition(condition);
            if (bindRecord == null || bindRecord.getId() == null) {
                log.warn("未找到设备绑定记录，设备OID：{}，机构代码：{}", existingDevice.getOid(), existingDevice.getOrgCode());
                return AjaxResult.success("未找到绑定记录，跳过更新");
            }

            // 准备更新绑定记录
            DeviceBindRecordBo updateBindRecordBo = new DeviceBindRecordBo();
            BeanUtils.copyProperties(bindRecord, updateBindRecordBo);
            updateBindRecordBo.setDeviceMacAddress(deviceBo.getDeviceMacAddress());
            updateBindRecordBo.setClientVersion(deviceBo.getClientVersion());
            updateBindRecordBo.setExtendInfo(deviceBo.getExtendInfo());
            updateBindRecordBo.setUpdateBy(deviceBo.getUpdateBy());

            // 复用现有的updateDeviceBindRecord方法
            return deviceBindRecordService.updateDeviceBindRecord(updateBindRecordBo);
        } catch (Exception e) {
            log.error("更新设备绑定记录异常，设备OID：{}，异常：{}", existingDevice.getOid(), e.getMessage(), e);
            return AjaxResult.fail("绑定记录更新失败：" + e.getMessage());
        }
    }

    /**
     * 生成唯一的设备激活码 使用重试机制确保激活码唯一性，最多重试3次
     *
     * @return 唯一的激活码
     * @throws WarningException 当重试3次后仍然生成重复激活码时抛出异常
     */
    private String generateUniqueActivationCode() {
        final int maxRetries = 3;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            // 生成6位数字+大写字母的随机字符串
            String activationCode = RandomUtil.randomStringUpper(AISzzyConstants.ACTIVATION_CODE_LENGTH);

            // 检查激活码是否已存在
            DeviceVo existingDevice = getDetailByActivationCodeAndHardwareCode(activationCode, null);
            if (existingDevice == null || existingDevice.getId() == null) {
                log.debug("成功生成唯一激活码: {}, 尝试次数: {}", activationCode, attempt);
                return activationCode;
            }

            log.warn("激活码重复: {}, 第{}次尝试", activationCode, attempt);
        }

        // 重试3次后仍然重复，抛出异常
        log.error("激活码生成失败：重试{}次后仍然生成重复激活码", maxRetries);
        throw new WarningException("激活码生成失败，请稍后重试");
    }

}