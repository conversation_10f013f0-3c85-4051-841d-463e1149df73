package com.light.aiszzy.practiceBook.event;

import com.light.aiszzy.practiceBook.service.IPracticeBookService;
import com.light.aiszzy.practiceBook.service.IPracticeBookCatalogService;
import com.light.core.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class PracticeBookCatalogListener {

    @Resource
    private IPracticeBookService practiceBookService;

    @Resource
    private IPracticeBookCatalogService practiceBookCatalogService;

    /**
     *  目录新增｜｜ 删除  更新教辅状态
     * @param event the event info
     */
    @EventListener(value = {PracticeBookCatalogAddEvent.class})
    public void resetPracticeBookCatalogStatus(PracticeBookCatalogAddEvent event) {
        int catalogStatus = StatusEnum.YES.getCode();
        String practiceBookOid = event.getPracticeBookOid();
        log.info("【重置教辅目录设置状态】 重置教辅目录设置状态, 教辅 OID：{}", practiceBookOid);
        // 重置 教辅章节设置状态
        this.practiceBookService.updateCatalogStatusByOid(practiceBookOid, catalogStatus);
    }

    @EventListener(value = { PracticeBookCatalogDelEvent.class})
    public void resetPracticeBookCatalogStatus(PracticeBookCatalogDelEvent event) {
        int catalogStatus = StatusEnum.YES.getCode();
        String practiceBookOid = event.getPracticeBookOid();
        Integer count = this.practiceBookCatalogService.queryCountByPracticeBookOid(practiceBookOid);
        if(count == 0) {
            catalogStatus = StatusEnum.NO.getCode();
        }
        log.info("【重置教辅目录设置状态】 重置教辅目录设置状态, 教辅 OID：{}", practiceBookOid);
        // 重置 教辅章节设置状态
        this.practiceBookService.updateCatalogStatusByOid(practiceBookOid, catalogStatus);
    }





}
