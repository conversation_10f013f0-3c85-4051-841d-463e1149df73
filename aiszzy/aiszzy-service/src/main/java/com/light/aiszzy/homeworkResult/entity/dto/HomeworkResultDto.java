package com.light.aiszzy.homeworkResult.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 校本作业结果表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_result")
public class HomeworkResultDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 学校id
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 班级id
	 */
	@TableField("class_id")
	private Long classId;

	/**
	 * 学生oid
	 */
	@TableField("stu_oid")
	private String stuOid;

	/**
	 * 学生姓名
	 */
	@TableField("stu_name")
	private String stuName;

	/**
	 * 学号
	 */
	@TableField("stu_no")
	private String stuNo;

	/**
	 * 页码个数合集
	 */
	@TableField("page_nos")
	private String pageNos;

	/**
	 * 学生作答存储地址
	 */
	@TableField("stu_answer_urls")
	private String stuAnswerUrls;

	/**
	 * 学生作答图片信息json存储
	 */
	@TableField("stu_answer_page_info_json")
	private String stuAnswerPageInfoJson;

	/**
	 * 是否完整，所有页都扫描，0：否，1：是
	 */
	@TableField("is_complete")
	private Integer isComplete;

	/**
	 * 作业正确题目个数
	 */
	@TableField("right_num")
	private Integer rightNum;

	/**
	 * 作业错误题目个数
	 */
	@TableField("wrong_num")
	private Integer wrongNum;

	/**
	 * 本页（含正反页）未知是否正确题目个数
	 */
	@TableField("unknown_num")
	private Integer unknownNum;
	/**
	 * 本页（含正反页）未知是否正确题目个数
	 */
	@TableField("total_num")
	private Integer totalNum;

	/**
	 * 作业题目正确率，万分之，页面展示需要除以100
	 */
	@TableField("accuracy_rate")
	private Integer accuracyRate;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
