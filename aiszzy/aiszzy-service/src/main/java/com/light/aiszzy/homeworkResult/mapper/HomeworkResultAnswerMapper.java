package com.light.aiszzy.homeworkResult.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo;

/**
 * 学生题目答案表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkResultAnswerMapper extends BaseMapper<HomeworkResultAnswerDto> {

	List<HomeworkResultAnswerVo> getHomeworkResultAnswerListByCondition(HomeworkResultAnswerConditionBo condition);

}
