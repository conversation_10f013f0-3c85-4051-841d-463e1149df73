package com.light.aiszzy.practiceBook.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教辅目录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("practice_book_catalog")
public class PracticeBookCatalogDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 父目录OID，表示层级关系，NULL表示根目录
	 */
	@TableField("parent_oid")
	private String parentOid;


	/**
	 * 祖籍 OID 集合 多个逗号分割
	 */
	@TableField("superiors_oids")
	private String superiorsOids;

	/**
	 * 目录名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 关联的教辅OID
	 */
	@TableField("practice_book_oid")
	private String practiceBookOid;

	/**
	 * 起始页
	 */
	@TableField("page_start")
	private Long pageStart;

	/**
	 * 结束页
	 */
	@TableField("page_end")
	private Long pageEnd;

	/**
	 * 层级
	 */
	@ApiModelProperty("层级")
	@TableField("level")
	private Integer level;

	/**
	 * 排序
	 */
	@TableField("order_num")
	private Integer orderNum;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
