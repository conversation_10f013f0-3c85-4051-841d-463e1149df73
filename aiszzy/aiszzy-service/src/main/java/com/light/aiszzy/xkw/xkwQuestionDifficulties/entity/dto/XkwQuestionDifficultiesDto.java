package com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 试题难度等级
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("xkw_question_difficulties")
public class XkwQuestionDifficultiesDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 难度名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 难度档上限值
	 */
	@TableField("ceiling")
	private String ceiling;

	/**
	 * 难度档下限值
	 */
	@TableField("flooring")
	private String flooring;

}
