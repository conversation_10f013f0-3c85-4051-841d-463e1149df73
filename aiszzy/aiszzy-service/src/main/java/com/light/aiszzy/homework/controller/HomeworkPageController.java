package com.light.aiszzy.homework.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkPageConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkPageBo;
import com.light.aiszzy.homework.entity.vo.HomeworkPageVo;
import com.light.aiszzy.homework.service.IHomeworkPageService;

import com.light.aiszzy.homework.api.HomeworkPageApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 作业每页题目坐标信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@RestController
@Validated
@Api(value = "", tags = "作业每页题目坐标信息接口")
public class HomeworkPageController implements HomeworkPageApi {

    @Autowired
    private IHomeworkPageService homeworkPageService;

    public AjaxResult<PageInfo<HomeworkPageVo>> getHomeworkPagePageListByCondition(@RequestBody HomeworkPageConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkPageVo> pageInfo = new PageInfo<>(homeworkPageService.getHomeworkPageListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkPageVo>> getHomeworkPageListByCondition(@RequestBody HomeworkPageConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkPageService.getHomeworkPageListByCondition(condition));
    }

    public AjaxResult addHomeworkPage(@Validated @RequestBody HomeworkPageBo homeworkPageBo) {
        return homeworkPageService.addHomeworkPage(homeworkPageBo);
    }

    public AjaxResult updateHomeworkPage(@Validated @RequestBody HomeworkPageBo homeworkPageBo) {
        if (null == homeworkPageBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkPageService.updateHomeworkPage(homeworkPageBo);
    }

    public AjaxResult<HomeworkPageVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkPageService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkPageBo homeworkPageBo = new HomeworkPageBo();
            homeworkPageBo.setOid(oid);
            homeworkPageBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkPageService.updateHomeworkPage(homeworkPageBo);
    }
}
