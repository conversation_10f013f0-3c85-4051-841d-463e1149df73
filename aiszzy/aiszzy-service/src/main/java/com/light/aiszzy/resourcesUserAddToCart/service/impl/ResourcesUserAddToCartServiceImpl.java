package com.light.aiszzy.resourcesUserAddToCart.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.aiszzy.question.entity.dto.QuestionDto;
import com.light.aiszzy.question.mapper.QuestionMapper;
import com.light.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.resourcesUserAddToCart.entity.dto.ResourcesUserAddToCartDto;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartConditionBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.vo.ResourcesUserAddToCartVo;
import com.light.aiszzy.resourcesUserAddToCart.service.IResourcesUserAddToCartService;
import com.light.aiszzy.resourcesUserAddToCart.mapper.ResourcesUserAddToCartMapper;
import com.light.core.entity.AjaxResult;

/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class ResourcesUserAddToCartServiceImpl extends ServiceImpl<ResourcesUserAddToCartMapper, ResourcesUserAddToCartDto> implements IResourcesUserAddToCartService {

    @Resource
    private ResourcesUserAddToCartMapper resourcesUserAddToCartMapper;

    @Resource
    private QuestionMapper questionMapper;

    @Override
    public List<ResourcesUserAddToCartVo> getResourcesUserAddToCartListByCondition(ResourcesUserAddToCartConditionBo condition) {
        return resourcesUserAddToCartMapper.getResourcesUserAddToCartListByCondition(condition);
    }

    @Override
    public AjaxResult addResourcesUserAddToCart(ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
        if (StringUtils.isEmpty(resourcesUserAddToCartBo.getQuestionOid())) {
            return AjaxResult.fail("参数错误");
        }
        LambdaUpdateWrapper<ResourcesUserAddToCartDto> ud = new LambdaUpdateWrapper<>();
        ud.eq(ResourcesUserAddToCartDto::getUserOid, resourcesUserAddToCartBo.getUserOid());
        ud.eq(ResourcesUserAddToCartDto::getOrgCode, resourcesUserAddToCartBo.getOrgCode());
        ud.eq(ResourcesUserAddToCartDto::getSubject, resourcesUserAddToCartBo.getSubject());
        ud.eq(ResourcesUserAddToCartDto::getQuestionOid, resourcesUserAddToCartBo.getQuestionOid());
        ud.eq(ResourcesUserAddToCartDto::getIsDelete,  StatusEnum.NOTDELETE.getCode());

        if (CollectionUtil.isNotEmpty(list(ud))) {
            return AjaxResult.fail("已经添加，添加失败");
        }
        ResourcesUserAddToCartDto resourcesUserAddToCart = new ResourcesUserAddToCartDto();
        BeanUtils.copyProperties(resourcesUserAddToCartBo, resourcesUserAddToCart);
        LambdaQueryWrapper<QuestionDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(QuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(QuestionDto::getOid, resourcesUserAddToCartBo.getQuestionOid());
        QuestionDto question = questionMapper.selectOne(lqw);
        if (question == null) {
            return AjaxResult.fail("获取题目失败");
        }
        BeanUtils.copyProperties(question, resourcesUserAddToCart);
        resourcesUserAddToCart.setId(null);
        resourcesUserAddToCart.setCreateBy(null);
        resourcesUserAddToCart.setUpdateBy(null);
        resourcesUserAddToCart.setCreateTime(null);
        resourcesUserAddToCart.setUpdateTime(null);
        resourcesUserAddToCart.setIsDelete(StatusEnum.NOTDELETE.getCode());
        resourcesUserAddToCart.setOid(IdUtil.simpleUUID());
        if (save(resourcesUserAddToCart)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateResourcesUserAddToCart(ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
        LambdaQueryWrapper<ResourcesUserAddToCartDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ResourcesUserAddToCartDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ResourcesUserAddToCartDto::getOid, resourcesUserAddToCartBo.getOid());
        ResourcesUserAddToCartDto resourcesUserAddToCart = getOne(lqw);
        Long id = resourcesUserAddToCart.getId();
        if (resourcesUserAddToCart == null) {
            return AjaxResult.fail("保存失败");
        }
        BeanUtils.copyProperties(resourcesUserAddToCartBo, resourcesUserAddToCart);
        resourcesUserAddToCart.setId(id);
        if (updateById(resourcesUserAddToCart)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult deleteQuestion(ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
        LambdaUpdateWrapper<ResourcesUserAddToCartDto> ud = new LambdaUpdateWrapper<>();
        ud.eq(ResourcesUserAddToCartDto::getUserOid, resourcesUserAddToCartBo.getUserOid());
        ud.eq(ResourcesUserAddToCartDto::getOrgCode, resourcesUserAddToCartBo.getOrgCode());
        ud.eq(ResourcesUserAddToCartDto::getSubject, resourcesUserAddToCartBo.getSubject());
        if (resourcesUserAddToCartBo.getQuestionTypeId() != null) {
            ud.eq(ResourcesUserAddToCartDto::getQuestionTypeId, resourcesUserAddToCartBo.getQuestionTypeId());
        }
        if (StringUtils.isNotEmpty(resourcesUserAddToCartBo.getQuestionOid())) {
            ud.eq(ResourcesUserAddToCartDto::getQuestionOid, resourcesUserAddToCartBo.getQuestionOid());
        }
        ud.eq(ResourcesUserAddToCartDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        ud.set(ResourcesUserAddToCartDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        update(ud);
        return AjaxResult.success();
    }


    @Override
    public List<ResourcesUserAddToCartVo> getDetail(String userOid, String orgCode, Integer subject) {
        LambdaQueryWrapper<ResourcesUserAddToCartDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ResourcesUserAddToCartDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ResourcesUserAddToCartDto::getUserOid, userOid);
        lqw.eq(ResourcesUserAddToCartDto::getOrgCode, orgCode);
        lqw.eq(ResourcesUserAddToCartDto::getSubject, subject);
        List<ResourcesUserAddToCartDto> resourcesUserAddToCart = list(lqw);
        List<ResourcesUserAddToCartVo> arr = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(resourcesUserAddToCart)) {
            for (ResourcesUserAddToCartDto dto : resourcesUserAddToCart) {
                ResourcesUserAddToCartVo resourcesUserAddToCartVo = new ResourcesUserAddToCartVo();
                if (resourcesUserAddToCart != null) {
                    BeanUtils.copyProperties(dto, resourcesUserAddToCartVo);
                }
                arr.add(resourcesUserAddToCartVo);
            }

        }

        return arr;
    }

}