package com.light.aiszzy.practiceBook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageConditionBo;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookPageDto;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo;
import com.light.aiszzy.practiceBook.mapper.PracticeBookPageMapper;
import com.light.aiszzy.practiceBook.service.IPracticeBookPageService;
import com.light.aiszzy.practiceBook.service.IPracticeBookQuestionService;
import com.light.aiszzy.practiceBook.service.IPracticeBookService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 教辅目录每页图片记录表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class PracticeBookPageServiceImpl extends ServiceImpl<PracticeBookPageMapper, PracticeBookPageDto> implements IPracticeBookPageService {

	@Resource
	private PracticeBookPageMapper practiceBookPageMapper;

	@Resource
	private IPracticeBookQuestionService practiceBookQuestionService;

	@Resource
	private IPracticeBookService iPracticeBookService;

	
    @Override
	public List<PracticeBookPageVo> getPracticeBookPageListByCondition(PracticeBookPageConditionBo condition) {
        return practiceBookPageMapper.getPracticeBookPageListByCondition(condition);
	}

	@Override
	public AjaxResult addPracticeBookPage(PracticeBookPageBo practiceBookPageBo) {
		PracticeBookPageDto practiceBookPage = new PracticeBookPageDto();
		BeanUtils.copyProperties(practiceBookPageBo, practiceBookPage);
		practiceBookPage.setIsDelete(StatusEnum.NOTDELETE.getCode());
		practiceBookPage.setOid(IdUtil.simpleUUID());
		if(save(practiceBookPage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updatePracticeBookPage(PracticeBookPageBo practiceBookPageBo) {
		LambdaQueryWrapper<PracticeBookPageDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(PracticeBookPageDto::getOid, practiceBookPageBo.getOid());
		PracticeBookPageDto practiceBookPage = getOne(lqw);
		Long id = practiceBookPage.getId();
		if(practiceBookPage == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(practiceBookPageBo, practiceBookPage);
		practiceBookPage.setId(id);
		if(updateById(practiceBookPage)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public PracticeBookPageVo getDetail(String oid) {
		LambdaQueryWrapper<PracticeBookPageDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(PracticeBookPageDto::getOid, oid);
		PracticeBookPageDto practiceBookPage = getOne(lqw);
	    PracticeBookPageVo practiceBookPageVo = new PracticeBookPageVo();
		if(practiceBookPage != null){
			BeanUtils.copyProperties(practiceBookPage, practiceBookPageVo);
		}
		return practiceBookPageVo;
	}

	@Override
	public AjaxResult incrementQuestionNum(String pageOid) {
		if (pageOid == null || pageOid.trim().isEmpty()) {
			return AjaxResult.fail("页面OID不能为空");
		}

		// 使用数据库级别的CAS操作保证并发安全
		// 同时更新question_num和status字段
		LambdaUpdateWrapper<PracticeBookPageDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(PracticeBookPageDto::getOid, pageOid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.setSql("question_num = question_num + 1")
				.setSql("status = CASE WHEN finish_question_num = question_num + 1 THEN 1 ELSE 0 END");

		boolean updateResult = update(updateWrapper);
		if (updateResult) {
			return AjaxResult.success("页面题目数量更新成功");
		} else {
			return AjaxResult.fail("页面题目数量更新失败");
		}
	}

	@Override
	public AjaxResult incrementFinishQuestionNum(String pageOid) {
		if (pageOid == null || pageOid.trim().isEmpty()) {
			return AjaxResult.fail("页面OID不能为空");
		}

		// 使用数据库级别的CAS操作保证并发安全
		// 同时更新finish_question_num和status字段
		LambdaUpdateWrapper<PracticeBookPageDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(PracticeBookPageDto::getOid, pageOid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.setSql("finish_question_num = finish_question_num + 1")
				.setSql("status = CASE WHEN finish_question_num + 1 = question_num THEN 1 ELSE status END");

		boolean updateResult = update(updateWrapper);
		if (updateResult) {
			return AjaxResult.success("页面完成题目数量更新成功");
		} else {
			return AjaxResult.fail("页面完成题目数量更新失败");
		}
	}

	@Override
	public AjaxResult decrementFinishQuestionNum(String pageOid) {
		if (pageOid == null || pageOid.trim().isEmpty()) {
			return AjaxResult.fail("页面OID不能为空");
		}

		// 先获取当前记录，检查finish_question_num值
		PracticeBookPageDto currentPage = getOne(new LambdaQueryWrapper<PracticeBookPageDto>()
				.eq(PracticeBookPageDto::getOid, pageOid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

		if (currentPage == null) {
			return AjaxResult.fail("页面记录不存在");
		}

		if (currentPage.getFinishQuestionNum() == null || currentPage.getFinishQuestionNum() <= 0) {
			return AjaxResult.fail("页面完成题目数量已为0，无法继续减少");
		}

		// 计算新的值，避免在SQL中进行可能导致下溢的减法操作
		long newFinishQuestionNum = currentPage.getFinishQuestionNum() - 1;
		long newStatus = (currentPage.getQuestionNum() != null &&
						 newFinishQuestionNum == currentPage.getQuestionNum()) ? 1 : 0;

		// 使用乐观锁更新，确保并发安全
		LambdaUpdateWrapper<PracticeBookPageDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(PracticeBookPageDto::getOid, pageOid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.eq(PracticeBookPageDto::getFinishQuestionNum, currentPage.getFinishQuestionNum()) // 乐观锁
				.set(PracticeBookPageDto::getFinishQuestionNum, newFinishQuestionNum)
				.set(PracticeBookPageDto::getStatus, newStatus);

		boolean updateResult = update(updateWrapper);
		if (updateResult) {
			return AjaxResult.success("页面完成题目数量减少成功");
		} else {
			return AjaxResult.fail("页面完成题目数量减少失败，可能数据已被其他操作修改");
		}
	}

	@Override
	public AjaxResult decrementQuestionNum(String pageOid) {
		if (pageOid == null || pageOid.trim().isEmpty()) {
			return AjaxResult.fail("页面OID不能为空");
		}

		// 先获取当前记录，检查question_num值
		PracticeBookPageDto currentPage = getOne(new LambdaQueryWrapper<PracticeBookPageDto>()
				.eq(PracticeBookPageDto::getOid, pageOid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

		if (currentPage == null) {
			return AjaxResult.fail("页面记录不存在");
		}

		if (currentPage.getQuestionNum() == null || currentPage.getQuestionNum() <= 0) {
			return AjaxResult.fail("页面题目数量已为0，无法继续减少");
		}

		// 计算新的值，避免在SQL中进行可能导致下溢的减法操作
		long newQuestionNum = currentPage.getQuestionNum() - 1;
		long newStatus = (currentPage.getFinishQuestionNum() != null &&
						 currentPage.getFinishQuestionNum().equals(newQuestionNum)) ? 1 : 0;

		// 使用乐观锁更新，确保并发安全
		LambdaUpdateWrapper<PracticeBookPageDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(PracticeBookPageDto::getOid, pageOid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.eq(PracticeBookPageDto::getQuestionNum, currentPage.getQuestionNum()) // 乐观锁
				.set(PracticeBookPageDto::getQuestionNum, newQuestionNum)
				.set(PracticeBookPageDto::getStatus, newStatus);

		boolean updateResult = update(updateWrapper);
		if (updateResult) {
			return AjaxResult.success("页面题目数量减少成功");
		} else {
			return AjaxResult.fail("页面题目数量减少失败，可能数据已被其他操作修改");
		}
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult saveBatchByPracticeBookOid(String practiceBookOid, List<PracticeBookPageBo> list) {

		// 保存分页数据
		List<PracticeBookPageDto> pageDtoList = list.stream().map(x -> {
			x.setOid(IdUtil.simpleUUID());
			PracticeBookPageDto practiceBookPageDto = BeanUtil.toBean(x, PracticeBookPageDto.class);
			practiceBookPageDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
			practiceBookPageDto.setPracticeBookOid(practiceBookOid);
			return practiceBookPageDto;
		}).collect(Collectors.toList());
		this.saveBatch(pageDtoList);

		long questionNum = pageDtoList.stream().filter(x -> x.getAnalysisQuestionNum() != null).mapToLong(PracticeBookPageDto::getAnalysisQuestionNum).sum();
		// 保存题目信息数据
		list.stream().filter(x-> CollUtil.isNotEmpty(x.getPracticeBookQuestionList())).forEach(x-> {
			x.getPracticeBookQuestionList().forEach(q-> {
				q.setPageNo(x.getPageNo().intValue());
				q.setPracticeBookOid(practiceBookOid);
			});
			this.practiceBookQuestionService.delAndSaveBatchByPracticeBookPageOid(x.getOid(), x.getPracticeBookQuestionList());
		});

		// 增加题目数量
		this.iPracticeBookService.addTotalQuestionNumByOid(practiceBookOid, questionNum);

		return AjaxResult.success();
	}


	/**
	 *  根据教辅 OID 删除数据
	 * @param practiceBookOid the 教辅 OID
	 * @return boolean
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByPracticeBookOid(String practiceBookOid) {

		// 删除页码信息
		LambdaUpdateWrapper<PracticeBookPageDto> lqw = new LambdaUpdateWrapper<>();
		lqw.eq(PracticeBookPageDto::getPracticeBookOid, practiceBookOid);
		lqw.set(PracticeBookPageDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		boolean update = this.update(lqw);

		// 删除 页码对应题目数据
		this.practiceBookQuestionService.deleteByPracticeBookOid(practiceBookOid);

		// 设置题目 数量
		this.iPracticeBookService.updateQuestionNumInfoByOid(practiceBookOid, 0L, 0L);

		return update;
	}

	@Override
	public PracticeBookPageVo queryByOid(String oid) {
		QueryWrapper<PracticeBookPageDto> qw = new QueryWrapper<>();
		qw.lambda().eq(PracticeBookPageDto::getOid, oid);
		qw.lambda().eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		PracticeBookPageDto practiceBookPageDto = this.baseMapper.selectOne(qw);
		if(practiceBookPageDto == null) {
			return null;
		}
		return BeanUtil.toBean(practiceBookPageDto, PracticeBookPageVo.class);
	}

	@Override
	public boolean updateQuestionJson(String oid, String questionJson) {
		UpdateWrapper<PracticeBookPageDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookPageDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookPageDto::getQuestionJson, questionJson);
		return this.update(updateWrapper);
	}

	@Override
	public boolean updateQuestionJsonAndDecrementNum(String oid, String questionJson) {
		if (oid == null || oid.trim().isEmpty()) {
			return false;
		}

		// 先检查当前记录的question_num值
		PracticeBookPageDto currentPage = getOne(new LambdaQueryWrapper<PracticeBookPageDto>()
				.eq(PracticeBookPageDto::getOid, oid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

		if (currentPage == null || currentPage.getQuestionNum() == null || currentPage.getQuestionNum() <= 0) {
			return false; // 如果question_num为0或null，直接返回false
		}

		// 计算新的question_num和status值，避免在SQL中进行可能导致下溢的减法操作
		long newQuestionNum = currentPage.getQuestionNum() - 1;
		long newStatus = (currentPage.getFinishQuestionNum() != null &&
						 currentPage.getFinishQuestionNum().equals(newQuestionNum)) ? 1 : 0;

		// 使用安全的更新方式，直接设置计算后的值
		LambdaUpdateWrapper<PracticeBookPageDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(PracticeBookPageDto::getOid, oid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.eq(PracticeBookPageDto::getQuestionNum, currentPage.getQuestionNum()) // 使用原值作为乐观锁
				.set(PracticeBookPageDto::getQuestionJson, questionJson)
				.set(PracticeBookPageDto::getQuestionNum, newQuestionNum)
				.set(PracticeBookPageDto::getStatus, newStatus);

		return this.update(updateWrapper);
	}

	@Override
	public boolean updateQuestionJsonAndAddNum(String oid, String questionJson) {
		if (oid == null || oid.trim().isEmpty()) {
			return false;
		}

		// 先检查当前记录的question_num值
		PracticeBookPageDto currentPage = getOne(new LambdaQueryWrapper<PracticeBookPageDto>()
				.eq(PracticeBookPageDto::getOid, oid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

		if (currentPage == null) {
			return false; // 如果question_num为0或null，直接返回false
		}

		// 计算新的question_num和status值，避免在SQL中进行可能导致下溢的减法操作
		long newQuestionNum = currentPage.getQuestionNum() + 1;

		// 使用安全的更新方式，直接设置计算后的值
		LambdaUpdateWrapper<PracticeBookPageDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(PracticeBookPageDto::getOid, oid)
				.eq(PracticeBookPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.eq(PracticeBookPageDto::getQuestionNum, currentPage.getQuestionNum()) // 使用原值作为乐观锁
				.set(PracticeBookPageDto::getQuestionJson, questionJson)
				.set(PracticeBookPageDto::getQuestionNum, newQuestionNum)
				.set(PracticeBookPageDto::getStatus, StatusEnum.NO.getCode());

		return this.update(updateWrapper);
	}
}