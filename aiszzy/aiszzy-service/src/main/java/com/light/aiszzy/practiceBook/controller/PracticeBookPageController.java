package com.light.aiszzy.practiceBook.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookPageService;

import com.light.aiszzy.practiceBook.api.PracticeBookPageApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 教辅目录每页图片记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "教辅目录每页图片记录表接口")
public class PracticeBookPageController implements PracticeBookPageApi {

    @Autowired
    private IPracticeBookPageService practiceBookPageService;

    public AjaxResult<PageInfo<PracticeBookPageVo>> getPracticeBookPagePageListByCondition(@RequestBody PracticeBookPageConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<PracticeBookPageVo> pageInfo = new PageInfo<>(practiceBookPageService.getPracticeBookPageListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<PracticeBookPageVo>> getPracticeBookPageListByCondition(@RequestBody PracticeBookPageConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(practiceBookPageService.getPracticeBookPageListByCondition(condition));
    }

    public AjaxResult addPracticeBookPage(@Validated @RequestBody PracticeBookPageBo practiceBookPageBo) {
        return practiceBookPageService.addPracticeBookPage(practiceBookPageBo);
    }

    public AjaxResult updatePracticeBookPage(@Validated @RequestBody PracticeBookPageBo practiceBookPageBo) {
        if (null == practiceBookPageBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return practiceBookPageService.updatePracticeBookPage(practiceBookPageBo);
    }

    public AjaxResult<PracticeBookPageVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(practiceBookPageService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            PracticeBookPageBo practiceBookPageBo = new PracticeBookPageBo();
            practiceBookPageBo.setOid(oid);
            practiceBookPageBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return practiceBookPageService.updatePracticeBookPage(practiceBookPageBo);
    }

    @Override
    public AjaxResult saveBatchByPracticeBookOid(@PathVariable("practiceBookOid") String practiceBookOid, @RequestBody List<PracticeBookPageBo> list) {
        return this.practiceBookPageService.saveBatchByPracticeBookOid(practiceBookOid, list);
    }

    @Override
    public AjaxResult<Void> deleteByPraticeBookOid(@PathVariable("practiceBookOid") String practiceBookOid) {
        this.practiceBookPageService.deleteByPracticeBookOid(practiceBookOid);
        return AjaxResult.success();
    }
}
