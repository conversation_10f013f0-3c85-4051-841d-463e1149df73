package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkClassInfoDto;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassInfoBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 作业班级信息，包含疑问项汇总接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 17:01:24
 */
public interface IHomeworkClassInfoService extends IService<HomeworkClassInfoDto> {

    List<HomeworkClassInfoVo> getHomeworkClassInfoListByCondition(HomeworkClassInfoConditionBo condition);

	AjaxResult addHomeworkClassInfo(HomeworkClassInfoBo homeworkClassInfoBo);

	AjaxResult updateHomeworkClassInfo(HomeworkClassInfoBo homeworkClassInfoBo);

    HomeworkClassInfoVo getDetail(String oid);

}

