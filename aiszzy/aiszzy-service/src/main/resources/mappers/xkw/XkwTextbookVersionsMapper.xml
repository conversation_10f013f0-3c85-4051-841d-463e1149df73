<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.xkw.xkwTextbookVersions.mapper.XkwTextbookVersionsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.xkw.xkwTextbookVersions.entity.dto.XkwTextbookVersionsDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="courseId" column="course_id"/>
        <result property="year" column="year"/>
        <result property="ordinal" column="ordinal"/>
        <result property="name" column="name"/>
    </resultMap>

	<select id="getXkwTextbookVersionsListByCondition" resultType="com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo">
		select t.* from (
			select a.* from xkw_textbook_versions as a
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
				<if test="courseId != null and courseId != ''">and course_id = #{courseId}</if>
				<if test="year != null and year != ''">and year = #{year}</if>
				<if test="ordinal != null and ordinal != ''">and ordinal = #{ordinal}</if>
				<if test="name != null and name != ''">and name = #{name}</if>
		    </where>
	</select>
</mapper>