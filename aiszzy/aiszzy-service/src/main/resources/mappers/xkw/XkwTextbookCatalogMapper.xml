<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.xkw.xkwTextbookCatalog.mapper.XkwTextbookCatalogMapper">


	<select id="getXkwTextbookCatalogListByCondition" resultType="com.light.aiszzy.xkw.xkwTextbookCatalog.entity.vo.XkwTextbookCatalogVo">
		select t.* from (
			select a.* from xkw_textbook_catalog as a
		    inner join xkw_textbook t on a.textbook_id = t.id
			<where>
				<if test="versionId != null">
					and t.version_id = #{versionId}
				</if>
				<if test="grade != null">
					and t.grade_id = #{grade}
				</if>
				<if test="term != null and term != ''">
					and t.term = #{term}
				</if>
			</where>
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="textbookId != null">and textbook_id = #{textbookId}</if>
				<if test="ordinal != null">and ordinal = #{ordinal}</if>
				<if test="parentId != null">and parent_id = #{parentId}</if>
				<if test="type != null and type != ''">and type = #{type}</if>
				<if test="name != null and name != ''">and name = #{name}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
		    </where>
		order by ordinal
	</select>
</mapper>