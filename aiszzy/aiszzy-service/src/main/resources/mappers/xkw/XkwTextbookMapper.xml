<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.xkw.xkwTextbook.mapper.XkwTextbookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.xkw.xkwTextbook.entity.vo.XkwTextbookVo" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="courseId" column="course_id"/>
        <result property="ordinal" column="ordinal"/>
        <result property="volume" column="volume"/>
        <result property="versionId" column="version_id"/>
        <result property="gradeId" column="grade_id"/>
        <result property="term" column="term"/>
        <result property="name" column="name"/>
    </resultMap>

	<select id="getXkwTextbookListByCondition" resultType="com.light.aiszzy.xkw.xkwTextbook.entity.vo.XkwTextbookVo">
		select t.* from (
			select a.* from xkw_textbook as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="courseId != null">and course_id = #{courseId}</if>
				<if test="ordinal != null">and ordinal = #{ordinal}</if>
				<if test="volume != null and volume != ''">and volume = #{volume}</if>
				<if test="versionId != null">and version_id = #{versionId}</if>
				<if test="gradeId != null">and grade_id = #{gradeId}</if>
				<if test="term != null and term != ''">and term = #{term}</if>
				<if test="name != null and name != ''">and name = #{name}</if>
		    </where>
	</select>
</mapper>