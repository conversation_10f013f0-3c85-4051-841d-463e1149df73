<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.resourcesQuestion.mapper.ResourcesQuestionSimilarRelationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.resourcesQuestion.entity.dto.ResourcesQuestionSimilarRelationDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="originalResourcesQuestionOid" column="original_resources_question_oid"/>
        <result property="similarResourcesQuestionOid" column="similar_resources_question_oid"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getResourcesQuestionSimilarRelationListByCondition" resultType="com.light.aiszzy.resourcesQuestion.entity.vo.ResourcesQuestionSimilarRelationVo">
		select t.* from (
			select a.* from resources_question_similar_relation as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="originalResourcesQuestionOid != null and originalResourcesQuestionOid != ''">and original_resources_question_oid = #{originalResourcesQuestionOid}</if>
				<if test="similarResourcesQuestionOid != null and similarResourcesQuestionOid != ''">and similar_resources_question_oid = #{similarResourcesQuestionOid}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>