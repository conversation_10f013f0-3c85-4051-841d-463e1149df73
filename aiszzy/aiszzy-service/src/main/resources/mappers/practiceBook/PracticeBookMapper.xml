<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.practiceBook.mapper.PracticeBookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.practiceBook.entity.dto.PracticeBookDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="coverImage" column="cover_image"/>
        <result property="description" column="description"/>
        <result property="publisher" column="publisher"/>
        <result property="subject" column="subject"/>
        <result property="grade" column="grade"/>
        <result property="isbn" column="isbn"/>
        <result property="textBookVersionId" column="text_book_version_id"/>
        <result property="year" column="year"/>
        <result property="term" column="term"/>
        <result property="series" column="series"/>
        <result property="category" column="category"/>
        <result property="visibility" column="visibility"/>
        <result property="fileUrl" column="file_url"/>
        <result property="fileType" column="file_type"/>
        <result property="isMarketization" column="is_marketization"/>
        <result property="isHighShots" column="is_high_shots"/>
        <result property="catalogFilePath" column="catalog_file_path"/>
        <result property="catalogStatus" column="catalog_status"/>
        <result property="reviewStatus" column="review_status"/>
        <result property="reviewComment" column="review_comment"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="finishQuestionNum" column="finish_question_num"/>
        <result property="totalQuestionNum" column="total_question_num"/>
        <result property="xkwZsId" column="xkw_zs_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getPracticeBookListByCondition" resultType="com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo">
		select t.* from (
			select a.* from practice_book as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
			    <if test="code != null and code != ''">and code = #{code}</if>
				<if test="name != null and name != ''">and name = #{name}</if>
				<if test="coverImage != null and coverImage != ''">and cover_image = #{coverImage}</if>
				<if test="description != null and description != ''">and description = #{description}</if>
				<if test="publisher != null and publisher != ''">and publisher = #{publisher}</if>
				<if test="subject != null">and subject = #{subject}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="isbn != null and isbn != ''">and isbn = #{isbn}</if>
				<if test="textBookVersionId != null">and text_book_version_id = #{textBookVersionId}</if>
				<if test="year != null and year != ''">and year = #{year}</if>
				<if test="term != null">and term = #{term}</if>
				<if test="series != null and series != ''">and series = #{series}</if>
				<if test="category != null and category != ''">and category = #{category}</if>
				<if test="visibility != null">and visibility = #{visibility}</if>
				<if test="fileUrl != null and fileUrl != ''">and file_url = #{fileUrl}</if>
				<if test="fileType != null">and file_type = #{fileType}</if>
				<if test="isMarketization != null">and is_marketization = #{isMarketization}</if>
				<if test="isHighShots != null">and is_high_shots = #{isHighShots}</if>
				<if test="catalogFilePath != null and catalogFilePath != ''">and catalog_file_path = #{catalogFilePath}</if>
				<if test="catalogStatus != null">and catalog_status = #{catalogStatus}</if>
				<if test="reviewStatus != null">and review_status = #{reviewStatus}</if>
				<if test="reviewComment != null and reviewComment != ''">and review_comment = #{reviewComment}</if>
				<if test="status != null">and status = #{status}</if>
<!--				<if test="remark != null and remark != ''">and remark = #{remark}</if>-->
				<if test="finishQuestionNum != null">and finish_question_num = #{finishQuestionNum}</if>
				<if test="totalQuestionNum != null">and total_question_num = #{totalQuestionNum}</if>
				<if test="xkwZsId != null and xkwZsId != ''">and xkw_zs_id = #{xkwZsId}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>

			<if test="reviewStatusList != null and reviewStatusList.size() != 0">
				and review_status in
				<foreach collection="reviewStatusList" item="item" open="(" close=")" separator="," >
					#{item}
				</foreach>
			</if>
		</where>
		order by create_time desc
	</select>
</mapper>