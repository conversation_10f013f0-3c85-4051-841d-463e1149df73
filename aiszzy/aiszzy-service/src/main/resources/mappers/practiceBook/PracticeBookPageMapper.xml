<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.practiceBook.mapper.PracticeBookPageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.practiceBook.entity.dto.PracticeBookPageDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="practiceBookCode" column="practice_book_code"/>
        <result property="practiceBookOid" column="practice_book_oid"/>
        <result property="pageNo" column="page_no"/>
        <result property="imageUrl" column="image_url"/>
        <result property="questionNum" column="question_num"/>
        <result property="finishQuestionNum" column="finish_question_num"/>
        <result property="analysisQuestionNum" column="analysis_question_num"/>
        <result property="analysisJson" column="analysis_json"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getPracticeBookPageListByCondition" resultType="com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo">
		select t.* from (
			select a.* from practice_book_page as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
			    <if test="practiceBookCode != null and practiceBookCode != ''">and practice_book_code = #{practiceBookCode}</if>
				<if test="practiceBookOid != null and practiceBookOid != ''">and practice_book_oid = #{practiceBookOid}</if>
				<if test="pageNoSearch != null">and page_no = #{pageNoSearch}</if>
				<if test="imageUrl != null and imageUrl != ''">and image_url = #{imageUrl}</if>
				<if test="questionNum != null">and question_num = #{questionNum}</if>
				<if test="analysisQuestionNum != null">and analysis_question_num = #{analysisQuestionNum}</if>
				<if test="analysisJson != null and analysisJson != ''">and analysis_json = #{analysisJson}</if>
				<if test="status != null">and status = #{status}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>

		<if test="orderBy != null">
			order by ${orderBy}
		</if>
	</select>
</mapper>