<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.practiceBook.mapper.PracticeBookCatalogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.practiceBook.entity.dto.PracticeBookCatalogDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="parentOid" column="parent_oid"/>
        <result property="superiorsOids" column="superiors_oids"/>
        <result property="name" column="name"/>
        <result property="practiceBookOid" column="practice_book_oid"/>
        <result property="level" column="level"/>
        <result property="pageStart" column="page_start"/>
        <result property="pageEnd" column="page_end"/>
        <result property="orderNum" column="order_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getPracticeBookCatalogListByCondition" resultType="com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo">
		select t.* from (
			select a.* from practice_book_catalog as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="parentOid != null and parentOid != ''">and parent_oid = #{parentOid}</if>
				<if test="superiorsOids != null and superiorsOids != ''">and superiors_oids = #{superiorsOids}</if>
				<if test="name != null and name != ''">and name = #{name}</if>
				<if test="practiceBookOid != null and practiceBookOid != ''">and practice_book_oid = #{practiceBookOid}</if>
				<if test="level != null">and level = #{level}</if>
				<if test="pageStart != null">and page_start = #{pageStart}</if>
				<if test="pageEnd != null">and page_end = #{pageEnd}</if>
				<if test="orderNum != null">and order_num = #{orderNum}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>

    <update id="deleteByOid">
		update   practice_book_catalog  set is_delete = 1 where find_in_set(#{oid}, superiors_oids) or oid = #{oid}
    </update>

    <select id="selectMaxOrderNumByParentOid" resultType="java.lang.Integer">
		select ifnull(max(order_num),0) from practice_book_catalog where is_delete = 0 and parent_oid = #{parentOid} and practice_book_oid = #{practiceBookOid}
	</select>

    <select id="selectListByOidList" resultType="com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo">
		select * from practice_book_catalog where is_delete = 0 and oid in
			<foreach collection="oidList" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
	</select>

    <select id="selectByPracticeBookOid"
			resultType="com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo">
		select * from practice_book_catalog where is_delete = 0 and practice_book_oid = #{practiceBookOid}
	</select>
</mapper>