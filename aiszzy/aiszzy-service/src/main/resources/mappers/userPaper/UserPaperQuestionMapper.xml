<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.userPaper.mapper.UserPaperQuestionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.userPaper.entity.dto.UserPaperQuestionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="userPaperOid" column="user_paper_oid"/>
        <result property="userPaperPageOid" column="user_paper_page_oid"/>
        <result property="schoolResourceQuestionOid" column="school_resource_question_oid"/>
        <result property="imageUrl" column="image_url"/>
        <result property="position" column="position"/>
        <result property="pageNum" column="page_num"/>
        <result property="orderNum" column="order_num"/>
        <result property="thirdSourceType" column="third_source_type"/>
        <result property="thirdOutId" column="third_out_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getUserPaperQuestionListByCondition" resultType="com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo">
		select t.* from (
			select a.* from user_paper_question as a

		) t
	    <where>
			and is_delete = 0
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="userPaperOid != null and userPaperOid != ''">and user_paper_oid = #{userPaperOid}</if>
				<if test="userPaperPageOid != null and userPaperPageOid != ''">and user_paper_page_oid = #{userPaperPageOid}</if>
				<if test="schoolResourceQuestionOid != null and schoolResourceQuestionOid != ''">and school_resource_question_oid = #{schoolResourceQuestionOid}</if>
				<if test="imageUrl != null and imageUrl != ''">and image_url = #{imageUrl}</if>
				<if test="position != null and position != ''">and position = #{position}</if>
				<if test="pageNum != null">and page_num = #{pageNum}</if>
				<if test="orderNum != null">and order_num = #{orderNum}</if>
				<if test="thirdSourceType != null and thirdSourceType != ''">and third_source_type = #{thirdSourceType}</if>
				<if test="thirdOutId != null and thirdOutId != ''">and third_out_id = #{thirdOutId}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>

		    </where>
	</select>

    <select id="selectMaxOrderByUserPaperPageOid" resultType="java.lang.Integer">
		select max(a.order_num) from user_paper_question as a where a.is_delete = 0 and user_paper_page_oid = #{userPaperPageOid}
    </select>

	<!-- 可根据自己的需求，是否要使用 -->
	<resultMap type="com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo" id="selectByUserPaperOidResult">
		<result property="id" column="id"/>
		<result property="oid" column="oid"/>
		<result property="orgCode" column="org_code"/>
		<result property="userPaperOid" column="user_paper_oid"/>
		<result property="userPaperPageOid" column="user_paper_page_oid"/>
		<result property="schoolResourceQuestionOid" column="school_resource_question_oid"/>
		<result property="imageUrl" column="image_url"/>
		<result property="position" column="position"/>
		<result property="pageNum" column="page_num"/>
		<result property="orderNum" column="order_num"/>
		<result property="thirdSourceType" column="third_source_type"/>
		<result property="thirdOutId" column="third_out_id"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="createBy" column="create_by"/>
		<result property="updateBy" column="update_by"/>
		<association  property="schoolResourcesQuestion" javaType="com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo"  column="school_resource_question_oid" select="com.light.aiszzy.schoolResourcesQuestion.mapper.SchoolResourcesQuestionMapper.selectByOid"/>
	</resultMap>
    <select id="selectByUserPaperOid" resultMap="selectByUserPaperOidResult">
		select a.* from user_paper_question as a where is_delete = 0 and user_paper_oid = #{userPaperOid}
		order by page_num,order_num
	</select>

    <select id="selectByUserPaperPageOid" resultMap="selectByUserPaperOidResult">
		select a.* from user_paper_question as a where is_delete = 0 and user_paper_page_oid = #{userPaperPageOid}
		order by page_num,order_num
	</select>
</mapper>