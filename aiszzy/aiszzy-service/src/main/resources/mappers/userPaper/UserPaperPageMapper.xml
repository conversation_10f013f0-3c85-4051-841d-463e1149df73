<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.userPaper.mapper.UserPaperPageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.userPaper.entity.dto.UserPaperPageDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="userPaperOid" column="user_paper_oid"/>
        <result property="pageNo" column="page_no"/>
        <result property="imageUrl" column="image_url"/>
        <result property="questionNum" column="question_num"/>
        <result property="analysisQuestionNum" column="analysis_question_num"/>
        <result property="analysisJson" column="analysis_json"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getUserPaperPageListByCondition" resultType="com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo">
		select t.* from (
			select a.* from user_paper_page as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="userPaperOid != null and userPaperOid != ''">and user_paper_oid = #{userPaperOid}</if>
				<if test="pageNo != null">and page_no = #{pageNo}</if>
				<if test="imageUrl != null and imageUrl != ''">and image_url = #{imageUrl}</if>
				<if test="questionNum != null">and question_num = #{questionNum}</if>
				<if test="analysisQuestionNum != null">and analysis_question_num = #{analysisQuestionNum}</if>
				<if test="analysisJson != null and analysisJson != ''">and analysis_json = #{analysisJson}</if>
				<if test="status != null">and status = #{status}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>

    <select id="selectByUserPaperOid" resultType="com.light.aiszzy.userPaper.entity.vo.UserPaperPageVo">
		select * from user_paper_page where user_paper_oid = #{userPaperOid} and is_delete = 0 order by page_no
	</select>
</mapper>