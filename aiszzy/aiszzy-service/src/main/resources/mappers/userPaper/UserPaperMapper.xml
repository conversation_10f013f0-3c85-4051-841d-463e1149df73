<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.userPaper.mapper.UserPaperMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.userPaper.entity.dto.UserPaperDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="paperName" column="paper_name"/>
        <result property="orgCode" column="org_code"/>
        <result property="year" column="year"/>
        <result property="term" column="term"/>
        <result property="grade" column="grade"/>
        <result property="subject" column="subject"/>
        <result property="userOid" column="user_oid"/>
        <result property="paperImgUrls" column="paper_img_urls"/>
        <result property="questionNum" column="question_num"/>
        <result property="fileUrl" column="file_url"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getUserPaperListByCondition" resultType="com.light.aiszzy.userPaper.entity.vo.UserPaperVo">

			select a.* from user_paper as a

	    <where>
			and is_delete = 0
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="paperName != null and paperName != ''">and paper_name like concat('%',#{paperName},'%')</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="year != null and year != ''">and year = #{year}</if>
				<if test="term != null">and term = #{term}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="gradeList != null and gradeList.size() !=0">
					and grade in
					<foreach collection="gradeList" open="(" close=")" separator="," item="item">
						${item}
					</foreach>
				</if>
				<if test="category != null and category != ''">and category = #{category}</if>
				<if test="subject != null">and subject = #{subject}</if>
				<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
				<if test="paperImgUrls != null and paperImgUrls != ''">and paper_img_urls = #{paperImgUrls}</if>
				<if test="questionNum != null">and question_num = #{questionNum}</if>
				<if test="fileUrl != null and fileUrl != ''">and file_url = #{fileUrl}</if>
				<if test="status != null">and `status` = #{status}</if>
				<if test="isPublish != null">and `is_publish` = #{isPublish}</if>
				<if test="startCreateTime != null and startCreateTime != ''">
					and create_time &gt;= #{startCreateTime}
				</if>
				<if test="endCreateTime != null and endCreateTime != ''">
					and create_time &lt;= #{endCreateTime}
				</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
		    </where>
	</select>
</mapper>