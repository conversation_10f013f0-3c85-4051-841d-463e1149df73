<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.apiRequestLog.mapper.ApiRequestLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.apiRequestLog.entity.dto.ApiRequestLogDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="url" column="url"/>
        <result property="method" column="method"/>
        <result property="params" column="params"/>
        <result property="response" column="response"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getApiRequestLogListByCondition" resultType="com.light.aiszzy.apiRequestLog.entity.vo.ApiRequestLogVo">
		select t.* from (
			select a.* from api_request_log as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="url != null and url != ''">and url = #{url}</if>
				<if test="method != null and method != ''">and method = #{method}</if>
				<if test="params != null and params != ''">and params = #{params}</if>
				<if test="response != null and response != ''">and response = #{response}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>