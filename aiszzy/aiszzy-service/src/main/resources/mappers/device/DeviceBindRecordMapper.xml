<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.device.mapper.DeviceBindRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.device.entity.dto.DeviceBindRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="deviceOid" column="device_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="orgAreaName" column="org_area_name"/>
        <result property="orgAreaCode" column="org_area_code"/>
        <result property="orgCityName" column="org_city_name"/>
        <result property="orgCityCode" column="org_city_code"/>
        <result property="orgProvinceName" column="org_province_name"/>
        <result property="orgProvinceCode" column="org_province_code"/>
        <result property="status" column="status"/>
        <result property="deviceMacAddress" column="device_mac_address"/>
        <result property="clientVersion" column="client_version"/>
        <result property="extendInfo" column="extend_info"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createByRealName" column="create_by_real_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getDeviceBindRecordListByCondition" resultType="com.light.aiszzy.device.entity.vo.DeviceBindRecordVo">
		select t.* from (
			select a.* from device_bind_record as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="deviceOid != null and deviceOid != ''">and device_oid = #{deviceOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="orgName != null and orgName != ''">and org_name = #{orgName}</if>
				<if test="orgAreaName != null and orgAreaName != ''">and org_area_name = #{orgAreaName}</if>
				<if test="orgAreaCode != null and orgAreaCode != ''">and org_area_code = #{orgAreaCode}</if>
				<if test="orgCityName != null and orgCityName != ''">and org_city_name = #{orgCityName}</if>
				<if test="orgCityCode != null and orgCityCode != ''">and org_city_code = #{orgCityCode}</if>
				<if test="orgProvinceName != null and orgProvinceName != ''">and org_province_name = #{orgProvinceName}</if>
				<if test="orgProvinceCode != null and orgProvinceCode != ''">and org_province_code = #{orgProvinceCode}</if>
				<if test="status != null">and status = #{status}</if>
				<if test="deviceMacAddress != null and deviceMacAddress != ''">and device_mac_address = #{deviceMacAddress}</if>
				<if test="clientVersion != null and clientVersion != ''">and client_version = #{clientVersion}</if>
				<if test="extendInfo != null and extendInfo != ''">and extend_info = #{extendInfo}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="createByRealName != null and createByRealName != ''">and create_by_real_name = #{createByRealName}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<select id="getDetailByCondition" resultType="com.light.aiszzy.device.entity.vo.DeviceBindRecordVo">
		select t.* from (
		select a.* from device_bind_record as a
		) t
		<where>
			<if test="id != null">and id = #{id}</if>
			<if test="oid != null and oid != ''">and oid = #{oid}</if>
			<if test="deviceOid != null and deviceOid != ''">and device_oid = #{deviceOid}</if>
			<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
			<if test="orgName != null and orgName != ''">and org_name = #{orgName}</if>
			<if test="orgAreaName != null and orgAreaName != ''">and org_area_name = #{orgAreaName}</if>
			<if test="orgAreaCode != null and orgAreaCode != ''">and org_area_code = #{orgAreaCode}</if>
			<if test="orgCityName != null and orgCityName != ''">and org_city_name = #{orgCityName}</if>
			<if test="orgCityCode != null and orgCityCode != ''">and org_city_code = #{orgCityCode}</if>
			<if test="orgProvinceName != null and orgProvinceName != ''">and org_province_name = #{orgProvinceName}</if>
			<if test="orgProvinceCode != null and orgProvinceCode != ''">and org_province_code = #{orgProvinceCode}</if>
			<if test="status != null">and status = #{status}</if>
			<if test="deviceMacAddress != null and deviceMacAddress != ''">and device_mac_address = #{deviceMacAddress}</if>
			<if test="clientVersion != null and clientVersion != ''">and client_version = #{clientVersion}</if>
			<if test="extendInfo != null and extendInfo != ''">and extend_info = #{extendInfo}</if>
			<if test="createTime != null">and create_time = #{createTime}</if>
			<if test="updateTime != null">and update_time = #{updateTime}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
			<if test="createByRealName != null and createByRealName != ''">and create_by_real_name = #{createByRealName}</if>
			<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
			<if test="isDelete != null">and is_delete = #{isDelete}</if>
		</where>
		order by id desc limit 1
	</select>
</mapper>