<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.resultUploadFile.mapper.ResultUploadFileMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.resultUploadFile.entity.dto.ResultUploadFileDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="orgCode" column="org_code"/>
        <result property="pathOne" column="path_one"/>
        <result property="pathTwo" column="path_two"/>
        <result property="hardwareCode" column="hardware_code"/>
        <result property="isDeal" column="is_deal"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getResultUploadFileListByCondition" resultType="com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo">
		select t.* from (
			select a.* from result_upload_file as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="pathOne != null and pathOne != ''">and path_one = #{pathOne}</if>
				<if test="pathTwo != null and pathTwo != ''">and path_two = #{pathTwo}</if>
				<if test="hardwareCode != null and hardwareCode != ''">and hardware_code = #{hardwareCode}</if>
				<if test="isDeal != null">and is_deal = #{isDeal}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>