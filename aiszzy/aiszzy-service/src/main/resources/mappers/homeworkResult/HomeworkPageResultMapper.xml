<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homeworkResult.mapper.HomeworkPageResultMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="grade" column="grade"/>
        <result property="classId" column="class_id"/>
        <result property="stuOid" column="stu_oid"/>
        <result property="ocrStuName" column="ocr_stu_name"/>
        <result property="ocrStuNo" column="ocr_stu_no"/>
        <result property="pageNo" column="page_no"/>
        <result property="stuAnswerUrlOne" column="stu_answer_url_one"/>
        <result property="stuAnswerUrlTwo" column="stu_answer_url_two"/>
        <result property="homeworkResultOid" column="homework_result_oid"/>
        <result property="doubtType" column="doubt_type"/>
        <result property="isDoubt" column="is_doubt"/>
        <result property="stuAnswerCorrectResult" column="stu_answer_correct_result"/>
        <result property="stuAnswerCorrectResultPrint" column="stu_answer_correct_result_print"/>
        <result property="stuAnswerPageInfoJson" column="stu_answer_page_info_json"/>
        <result property="rightNum" column="right_num"/>
        <result property="wrongNum" column="wrong_num"/>
        <result property="unknownNum" column="unknown_num"/>
        <result property="totalNum" column="total_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkPageResultListByCondition" resultType="com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo">
		select t.* from (
			select a.* from homework_page_result as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="classId != null and classId != ''">and class_id = #{classId}</if>
				<if test="stuOid != null and stuOid != ''">and stu_oid = #{stuOid}</if>
				<if test="ocrStuName != null and ocrStuName != ''">and ocr_stu_name = #{ocrStuName}</if>
				<if test="ocrStuNo != null and ocrStuNo != ''">and ocr_stu_no = #{ocrStuNo}</if>
				<if test="searchPageNo != null">and page_no = #{searchPageNo}</if>
				<if test="stuAnswerUrlOne != null and stuAnswerUrlOne != ''">and stu_answer_url_one = #{stuAnswerUrlOne}</if>
				<if test="stuAnswerUrlTwo != null and stuAnswerUrlTwo != ''">and stu_answer_url_two = #{stuAnswerUrlTwo}</if>
				<if test="homeworkResultOid != null and homeworkResultOid != ''">and homework_result_oid = #{homeworkResultOid}</if>
				<if test="doubtType != null">and doubt_type = #{doubtType}</if>
				<if test="isDoubt != null">and is_doubt = #{isDoubt}</if>
				<if test="stuAnswerCorrectResult != null and stuAnswerCorrectResult != ''">and stu_answer_correct_result = #{stuAnswerCorrectResult}</if>
				<if test="stuAnswerCorrectResultPrint != null and stuAnswerCorrectResultPrint != ''">and stu_answer_correct_result_print = #{stuAnswerCorrectResultPrint}</if>
				<if test="stuAnswerPageInfoJson != null and stuAnswerPageInfoJson != ''">and stu_answer_page_info_json = #{stuAnswerPageInfoJson}</if>
				<if test="rightNum != null">and right_num = #{rightNum}</if>
				<if test="wrongNum != null">and wrong_num = #{wrongNum}</if>
				<if test="unknownNum != null">and unknown_num = #{unknownNum}</if>
				<if test="totalNum != null">and total_num = #{totalNum}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>