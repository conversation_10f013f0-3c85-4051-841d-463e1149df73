<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homeworkResult.mapper.HomeworkResultDoubtMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDoubtDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="grade" column="grade"/>
        <result property="classId" column="class_id"/>
        <result property="stuOid" column="stu_oid"/>
        <result property="stuAnswerUrl" column="stu_answer_url"/>
        <result property="isDoubt" column="is_doubt"/>
        <result property="doubtType" column="doubt_type"/>
        <result property="studentAnswerTime" column="student_answer_time"/>
        <result property="repeatId" column="repeat_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkResultDoubtListByCondition" resultType="com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultDoubtVo">
		select t.* from (
			select a.* from homework_result_doubt as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="classId != null and classId != ''">and class_id = #{classId}</if>
				<if test="stuOid != null and stuOid != ''">and stu_oid = #{stuOid}</if>
				<if test="stuAnswerUrl != null and stuAnswerUrl != ''">and stu_answer_url = #{stuAnswerUrl}</if>
				<if test="isDoubt != null">and is_doubt = #{isDoubt}</if>
				<if test="doubtType != null">and doubt_type = #{doubtType}</if>
				<if test="studentAnswerTime != null">and student_answer_time = #{studentAnswerTime}</if>
				<if test="repeatId != null and repeatId != ''">and repeat_id = #{repeatId}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>