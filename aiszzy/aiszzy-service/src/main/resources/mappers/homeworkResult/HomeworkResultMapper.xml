<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homeworkResult.mapper.HomeworkResultMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="grade" column="grade"/>
        <result property="classId" column="class_id"/>
        <result property="stuOid" column="stud_oid"/>
        <result property="stuName" column="stud_name"/>
        <result property="stuNo" column="stu_no"/>
        <result property="pageNos" column="page_nos"/>
        <result property="stuAnswerUrls" column="stu_answer_urls"/>
        <result property="stuAnswerPageInfoJson" column="stu_answer_page_info_json"/>
        <result property="isComplete" column="is_complete"/>
        <result property="rightNum" column="right_num"/>
        <result property="wrongNum" column="wrong_num"/>
        <result property="unknownNum" column="unknown_num"/>
        <result property="totalNum" column="total_num"/>
        <result property="accuracyRate" column="accuracy_rate"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkResultListByCondition" resultType="com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo">
		select t.* from (
			select a.* from homework_result as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="classId != null">and class_id = #{classId}</if>
				<if test="stuOid != null and studOid != ''">and stud_oid = #{studOid}</if>
				<if test="stuName != null and studName != ''">and stud_name = #{studName}</if>
				<if test="stuNo != null and stuNo != ''">and stu_no = #{stuNo}</if>
				<if test="pageNos != null and pageNos != ''">and page_nos = #{pageNos}</if>
				<if test="stuAnswerUrls != null and stuAnswerUrls != ''">and stu_answer_urls = #{stuAnswerUrls}</if>
				<if test="stuAnswerPageInfoJson != null and stuAnswerPageInfoJson != ''">and stu_answer_page_info_json = #{stuAnswerPageInfoJson}</if>
				<if test="isComplete != null">and is_complete = #{isComplete}</if>
				<if test="rightNum != null">and right_num = #{rightNum}</if>
				<if test="wrongNum != null">and wrong_num = #{wrongNum}</if>
				<if test="unknownNum != null">and unknown_num = #{unknownNum}</if>
				<if test="totalNum != null">and total_num = #{totalNum}</if>
				<if test="accuracyRate != null">and accuracy_rate = #{accuracyRate}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<select id="getDuplicateHomeworkResultListByCondition" resultType="com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo">
		SELECT
			h.*
		FROM
			homework_result h
		JOIN
			    ( SELECT homework_oid, stu_oid FROM homework_result WHERE homework_oid = #{homeworkOid} AND is_delete = #{isDelete} GROUP BY homework_oid, stu_oid HAVING COUNT(*) > 1 ) d
			        ON h.homework_oid = d.homework_oid AND h.stu_oid = d.stu_oid
		WHERE
			h.homework_oid = #{homeworkOid}
			AND h.is_delete = #{isDelete}
		ORDER BY
			h.homework_oid,
			h.stu_oid,
			h.create_time
	</select>
</mapper>