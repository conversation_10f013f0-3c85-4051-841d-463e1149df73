<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homeworkResult.mapper.HomeworkResultErrorDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultErrorDetailDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="homeworkResultOid" column="homework_result_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="grade" column="grade"/>
        <result property="classId" column="class_id"/>
        <result property="stuOid" column="stu_oid"/>
        <result property="stuUrl" column="stu_url"/>
        <result property="stuPageNo" column="stu_page_no"/>
        <result property="questionOid" column="question_oid"/>
        <result property="questionNum" column="question_num"/>
        <result property="bigNum" column="big_num"/>
        <result property="smallNum" column="small_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkResultErrorDetailListByCondition" resultType="com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultErrorDetailVo">
		select t.* from (
			select a.* from homework_result_error_detail as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="homeworkResultOid != null and homeworkResultOid != ''">and homework_result_oid = #{homeworkResultOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="classId != null">and class_id = #{classId}</if>
				<if test="stuOid != null and stuOid != ''">and stu_oid = #{stuOid}</if>
				<if test="stuUrl != null and stuUrl != ''">and stu_url = #{stuUrl}</if>
				<if test="stuPageNo != null">and stu_page_no = #{stuPageNo}</if>
				<if test="questionOid != null and questionOid != ''">and question_oid = #{questionOid}</if>
				<if test="questionNum != null and questionNum != ''">and question_num = #{questionNum}</if>
				<if test="bigNum != null and bigNum != ''">and big_num = #{bigNum}</if>
				<if test="smallNum != null and smallNum != ''">and small_num = #{smallNum}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>