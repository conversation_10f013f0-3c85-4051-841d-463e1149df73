<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.schoolBook.mapper.SchoolBookRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.schoolBook.entity.dto.SchoolBookRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="schoolBookOid" column="school_book_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="orgAreaName" column="org_area_name"/>
        <result property="orgAreaCode" column="org_area_code"/>
        <result property="orgCityName" column="org_city_name"/>
        <result property="orgCityCode" column="org_city_code"/>
        <result property="orgProvinceName" column="org_province_name"/>
        <result property="orgProvinceCode" column="org_province_code"/>
        <result property="bookOid" column="book_oid"/>
        <result property="status" column="status"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getSchoolBookRecordListByCondition" resultType="com.light.aiszzy.schoolBook.entity.vo.SchoolBookRecordVo">
		select t.* from (
			select a.* from school_book_record as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="schoolBookOid != null and schoolBookOid != ''">and school_book_oid = #{schoolBookOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="orgName != null and orgName != ''">and org_name = #{orgName}</if>
				<if test="orgAreaName != null and orgAreaName != ''">and org_area_name = #{orgAreaName}</if>
				<if test="orgAreaCode != null and orgAreaCode != ''">and org_area_code = #{orgAreaCode}</if>
				<if test="orgCityName != null and orgCityName != ''">and org_city_name = #{orgCityName}</if>
				<if test="orgCityCode != null and orgCityCode != ''">and org_city_code = #{orgCityCode}</if>
				<if test="orgProvinceName != null and orgProvinceName != ''">and org_province_name = #{orgProvinceName}</if>
				<if test="orgProvinceCode != null and orgProvinceCode != ''">and org_province_code = #{orgProvinceCode}</if>
				<if test="bookOid != null and bookOid != ''">and book_oid = #{bookOid}</if>
				<if test="status != null">and status = #{status}</if>
				<if test="startDate != null">and start_date = #{startDate}</if>
				<if test="endDate != null">and end_date = #{endDate}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>