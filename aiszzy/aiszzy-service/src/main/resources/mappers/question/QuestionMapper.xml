<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.question.mapper.QuestionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.question.entity.dto.QuestionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="questionTypeId" column="question_type_id"/>
        <result property="questionTypeName" column="question_type_name"/>
        <result property="subject" column="subject"/>
        <result property="year" column="year"/>
        <result property="grade" column="grade"/>
        <result property="difficultId" column="difficult_id"/>
        <result property="quesBody" column="ques_body"/>
        <result property="publicQues" column="public_ques"/>
        <result property="quesAnswer" column="ques_answer"/>
        <result property="analysisAnswer" column="analysis_answer"/>
        <result property="quesBodyType" column="ques_body_type"/>
        <result property="publicQuesType" column="public_ques_type"/>
        <result property="quesAnswerType" column="ques_answer_type"/>
        <result property="analysisAnswerType" column="analysis_answer_type"/>
        <result property="thirdSourceType" column="third_source_type"/>
        <result property="thirdOutId" column="third_out_id"/>
        <result property="knowledgePointsId" column="knowledge_points_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="sectionId" column="section_id"/>
        <result property="insideSourceType" column="inside_source_type"/>
        <result property="insideLinkOid" column="inside_link_oid"/>
        <result property="similarRecommendResult" column="similar_recommend_result"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getQuestionListByCondition" resultType="com.light.aiszzy.question.entity.vo.QuestionVo">
		select t.* from (
			select a.* from question as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="questionTypeId != null and questionTypeId != ''">and question_type_id = #{questionTypeId}</if>
				<if test="questionTypeName != null and questionTypeName != ''">and question_type_name = #{questionTypeName}</if>
				<if test="subject != null">and subject = #{subject}</if>
				<if test="year != null and year != ''">and year = #{year}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="difficultId != null">and difficult_id = #{difficultId}</if>
				<if test="quesBody != null and quesBody != ''">and ques_body = #{quesBody}</if>
				<if test="publicQues != null and publicQues != ''">and public_ques = #{publicQues}</if>
				<if test="quesAnswer != null and quesAnswer != ''">and ques_answer = #{quesAnswer}</if>
				<if test="analysisAnswer != null and analysisAnswer != ''">and analysis_answer = #{analysisAnswer}</if>
				<if test="quesBodyType != null">and ques_body_type = #{quesBodyType}</if>
				<if test="publicQuesType != null">and public_ques_type = #{publicQuesType}</if>
				<if test="quesAnswerType != null">and ques_answer_type = #{quesAnswerType}</if>
				<if test="analysisAnswerType != null">and analysis_answer_type = #{analysisAnswerType}</if>
				<if test="thirdSourceType != null and thirdSourceType != ''">and third_source_type = #{thirdSourceType}</if>
				<if test="thirdOutId != null and thirdOutId != ''">and third_out_id = #{thirdOutId}</if>
				<if test="knowledgePointsId != null and knowledgePointsId != ''">and knowledge_points_id = #{knowledgePointsId}</if>
				<if test="chapterId != null and chapterId != ''">and chapter_id = #{chapterId}</if>
				<if test="sectionId != null and sectionId != ''">and section_id = #{sectionId}</if>
				<if test="insideSourceType != null and insideSourceType != ''">and inside_source_type = #{insideSourceType}</if>
				<if test="insideLinkOid != null and insideLinkOid != ''">and inside_link_oid = #{insideLinkOid}</if>
				<if test="similarRecommendResult != null and similarRecommendResult != ''">and similar_recommend_result = #{similarRecommendResult}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<select id="getQuestionListByPracticeBookOid" resultType="com.light.aiszzy.question.entity.vo.QuestionVo">
		select c.* from practice_book_question b on a.oid = b.practice_book_oid
				 left join question c on b.question_oid = c.oid and c.is_delete = 0
		where b.practice_book_oid = #{practiceBookOid}
		  and b.practice_book_catalog_oid = #{practiceBookCatalogOid}
		  and b.is_delete = 0
		order by b.page_no asc,b.page_no_order_num asc
	</select>

	<select id="getQuestionListBySchoolPaperOid" resultType="com.light.aiszzy.question.entity.vo.QuestionVo">
		select c.* from user_paper_question a
			   left join school_resources_question  b on a.school_resource_question_oid = b.oid and b.is_delete = 0
				 left join question c on b.question_oid = c.oid and c.is_delete = 0
		where a.user_paper_oid = #{userPaperOid}
		  and a.is_delete = 0 and c.oid is not null
		order by a.order_num asc
	</select>

	<select id="getQuestionSimilarListByPracticeBookOid" resultType="string">
		select c.similar_recommend_result from  practice_book_question b
		           left join question c on b.question_oid = c.oid and c.is_delete = 0
		where b.practice_book_oid = #{practiceBookOid} and b.practice_book_catalog_oid = #{practiceBookCatalogOid} and b.is_delete = 0
		and (c.similar_recommend_result is not null or c.similar_recommend_result != '')
		order by b.page_no asc,b.page_no_order_num asc
	</select>

	<select id="getQuestionWithPracticeListByPracticeBookOid" resultType="com.light.aiszzy.question.entity.bo.QuestionWithPracticeBo">
		SELECT
			a.practice_book_oid,
			a.practice_book_catalog_oid,
			a.practice_book_page_oid,
			b.*
		FROM
			practice_book_question AS a
				JOIN question AS b ON a.question_oid = b.oid
		WHERE
			a.practice_book_oid = #{practiceBookOid}
		  AND a.is_delete = '0'
		  AND b.is_delete = '0'
		ORDER BY
			a.page_no,
			a.page_no_order_num
	</select>

</mapper>