<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.statistics.mapper.SchoolGradeQuestionStatisticsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.statistics.entity.dto.SchoolGradeQuestionStatisticsDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="questionOid" column="question_oid"/>
        <result property="questionTypeId" column="question_type_id"/>
        <result property="questionTypeName" column="question_type_name"/>
        <result property="subject" column="subject"/>
        <result property="knowledgePointsId" column="knowledge_points_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="sectionId" column="section_id"/>
        <result property="orgCode" column="org_code"/>
        <result property="grade" column="grade"/>
        <result property="rightNum" column="right_num"/>
        <result property="wrongNum" column="wrong_num"/>
        <result property="totalNum" column="total_num"/>
        <result property="accuracyRate" column="accuracy_rate"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getSchoolGradeQuestionStatisticsListByCondition" resultType="com.light.aiszzy.statistics.entity.vo.SchoolGradeQuestionStatisticsVo">
		select t.* from (
			select a.* from school_grade_question_statistics as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="questionOid != null and questionOid != ''">and question_oid = #{questionOid}</if>
				<if test="questionTypeId != null and questionTypeId != ''">and question_type_id = #{questionTypeId}</if>
				<if test="questionTypeName != null and questionTypeName != ''">and question_type_name = #{questionTypeName}</if>
				<if test="subject != null">and subject = #{subject}</if>
				<if test="knowledgePointsId != null and knowledgePointsId != ''">and knowledge_points_id = #{knowledgePointsId}</if>
				<if test="chapterId != null and chapterId != ''">and chapter_id = #{chapterId}</if>
				<if test="sectionId != null and sectionId != ''">and section_id = #{sectionId}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="rightNum != null">and right_num = #{rightNum}</if>
				<if test="wrongNum != null">and wrong_num = #{wrongNum}</if>
				<if test="totalNum != null">and total_num = #{totalNum}</if>
				<if test="accuracyRate != null and accuracyRate != ''">and accuracy_rate = #{accuracyRate}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>