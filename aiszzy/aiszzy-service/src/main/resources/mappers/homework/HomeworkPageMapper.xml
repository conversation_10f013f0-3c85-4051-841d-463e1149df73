<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homework.mapper.HomeworkPageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homework.entity.dto.HomeworkPageDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="pageNo" column="page_no"/>
        <result property="homeworkCode" column="homework_code"/>
        <result property="questionJson" column="question_json"/>
        <result property="pageUrl" column="page_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkPageListByCondition" resultType="com.light.aiszzy.homework.entity.vo.HomeworkPageVo">
		select t.* from (
			select a.* from homework_page as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkCode != null and homeworkCode != ''">and homework_code = #{homeworkCode}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="pageNoSearch != null">and page_no = #{pageNoSearch}</if>
				<if test="questionJson != null and questionJson != ''">and question_json = #{questionJson}</if>
				<if test="pageUrl != null and pageUrl != ''">and page_url = #{pageUrl}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>