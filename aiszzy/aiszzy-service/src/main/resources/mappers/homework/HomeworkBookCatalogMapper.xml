<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homework.mapper.HomeworkBookCatalogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkBookOid" column="homework_book_oid"/>
        <result property="parentOid" column="parent_oid"/>
        <result property="name" column="name"/>
        <result property="orderNum" column="order_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkBookCatalogListByCondition" resultType="com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogVo">
		select t.* from (
			select a.* from homework_book_catalog as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkBookOid != null and homeworkBookOid != ''">and homework_book_oid = #{homeworkBookOid}</if>
				<if test="parentOid != null and parentOid != ''">and parent_oid = #{parentOid}</if>
				<if test="name != null and name != ''">and name = #{name}</if>
				<if test="orderNum != null">and order_num = #{orderNum}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
		<if test="sort != null and sort != ''">
			order by ${sort}
		</if>
	</select>
</mapper>