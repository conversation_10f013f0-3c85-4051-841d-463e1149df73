<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homework.mapper.HomeworkBookCatalogInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogInfoDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkBookCatalogOid" column="homework_book_catalog_oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="orderNum" column="order_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkBookCatalogInfoListByCondition" resultType="com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogInfoVo">
		select t.* from (
			select a.* from homework_book_catalog_info as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkBookCatalogOid != null and homeworkBookCatalogOid != ''">and homework_book_catalog_oid = #{homeworkBookCatalogOid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="orderNum != null">and order_num = #{orderNum}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>