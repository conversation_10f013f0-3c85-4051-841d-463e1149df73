<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homework.mapper.HomeworkBookUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
	<resultMap type="com.light.aiszzy.homework.entity.dto.HomeworkBookUserDto" id="BaseResultMap">
		<result property="id" column="id"/>
		<result property="oid" column="oid"/>
		<result property="orgCode" column="org_code"/>
		<result property="homeworkBookOid" column="homework_book_oid"/>
		<result property="isCurrent" column="is_current"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="createBy" column="create_by"/>
		<result property="updateBy" column="update_by"/>
		<result property="isDelete" column="is_delete"/>
	</resultMap>

	<select id="getHomeworkBookUserListByCondition" resultType="com.light.aiszzy.homework.entity.vo.HomeworkBookVo">
		select t.* from (
		select b.*,a.is_current,
		b.subject AS `subjectCodeDic.dictValue`,
		b.grade AS `gradeCodeDic.dictValue`,
		b.term AS `termDic.dictValue`,
		c.name textBookVersionName,
		b.exercise_type AS `exerciseTypeDic.dictValue`,
		a.create_by createUser
		from homework_book_user as a
		left join homework_book b on b.oid = a.homework_book_oid
		left join xkw_textbook_versions c on c.id = b.text_book_version
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkBookOid != null and homeworkBookOid != ''">and homework_book_oid = #{homeworkBookOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="isCurrent != null">and is_current = #{isCurrent}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>