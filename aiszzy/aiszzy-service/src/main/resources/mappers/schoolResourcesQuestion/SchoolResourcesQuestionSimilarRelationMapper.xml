<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.schoolResourcesQuestion.mapper.SchoolResourcesQuestionSimilarRelationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.schoolResourcesQuestion.entity.dto.SchoolResourcesQuestionSimilarRelationDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="schoolResourcesQuestionOid" column="school_resources_question_oid"/>
        <result property="thirdOutId" column="third_out_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getSchoolResourcesQuestionSimilarRelationListByCondition" resultType="com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionSimilarRelationVo">
		select t.* from (
			select a.* from school_resources_question_similar_relation as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="schoolResourcesQuestionOid != null and schoolResourcesQuestionOid != ''">and school_resources_question_oid = #{schoolResourcesQuestionOid}</if>
				<if test="thirdOutId != null and thirdOutId != ''">and third_out_id = #{thirdOutId}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>