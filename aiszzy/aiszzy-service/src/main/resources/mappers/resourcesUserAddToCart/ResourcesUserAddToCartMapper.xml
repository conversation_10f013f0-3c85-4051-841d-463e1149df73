<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.resourcesUserAddToCart.mapper.ResourcesUserAddToCartMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.resourcesUserAddToCart.entity.dto.ResourcesUserAddToCartDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="userOid" column="user_oid"/>
        <result property="questionOid" column="question_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="subject" column="subject"/>
        <result property="questionTypeId" column="question_type_id"/>
        <result property="questionTypeName" column="question_type_name"/>
        <result property="bigNum" column="big_num"/>
        <result property="smallNum" column="small_num"/>
        <result property="quesBody" column="ques_body"/>
        <result property="quesBodyType" column="ques_body_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getResourcesUserAddToCartListByCondition" resultType="com.light.aiszzy.resourcesUserAddToCart.entity.vo.ResourcesUserAddToCartVo">
		select t.* from (
			select a.* from resources_user_add_to_cart as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
				<if test="questionOid != null and questionOid != ''">and question_oid = #{questionOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="subject != null">and subject = #{subject}</if>
				<if test="questionTypeId != null and questionTypeId != ''">and question_type_id = #{questionTypeId}</if>
				<if test="questionTypeName != null and questionTypeName != ''">and question_type_name = #{questionTypeName}</if>
				<if test="bigNum != null and bigNum != ''">and big_num = #{bigNum}</if>
				<if test="smallNum != null and smallNum != ''">and small_num = #{smallNum}</if>
				<if test="quesBody != null and quesBody != ''">and ques_body = #{quesBody}</if>
				<if test="quesBodyType != null">and ques_body_type = #{quesBodyType}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>