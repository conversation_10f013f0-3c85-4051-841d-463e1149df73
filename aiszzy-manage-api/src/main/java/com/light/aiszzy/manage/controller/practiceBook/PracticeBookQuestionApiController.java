package com.light.aiszzy.manage.controller.practiceBook;

import javax.validation.constraints.NotNull;

import cn.hutool.core.util.StrUtil;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import com.github.pagehelper.PageInfo;

import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionChangeBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.practiceBook.service.PracticeBookQuestionApiService;

/**
 * 教辅题目表，用于存储题目信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:30
 */
@RestController
@Validated
@Api(value = "", tags = "教辅题目表，用于存储题目信息接口")
public class PracticeBookQuestionApiController {

    @Autowired
    private PracticeBookQuestionApiService practiceBookQuestionApiService;

    @PostMapping("/practiceBookQuestion/pageList")
    @ApiOperation(value = "分页查询教辅题目表，用于存储题目信息", httpMethod = "POST")
    public AjaxResult<PageInfo<PracticeBookQuestionVo>>
        getPracticeBookQuestionPageListByCondition(@RequestBody PracticeBookQuestionConditionBo condition) {
        return practiceBookQuestionApiService.getPracticeBookQuestionPageListByCondition(condition);
    }

    @PostMapping("/practiceBookQuestion/list")
    @ApiOperation(value = "查询所有教辅题目表，用于存储题目信息", httpMethod = "POST")
    public AjaxResult<List<PracticeBookQuestionVo>>
        getPracticeBookQuestionAllListByCondition(@RequestBody PracticeBookQuestionConditionBo condition) {
        return practiceBookQuestionApiService.getPracticeBookQuestionListByCondition(condition);
    }

    @PostMapping("/practiceBookQuestion/saveDataByPosition")
    @ApiOperation(value = "新增教辅题目表，用于存储题目信息", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "新增教辅题目表，用于存储题目信息", operationType = OperationLogConstants.OP_TERM_APP,
        operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult saveDataByPosition(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo) {
        return practiceBookQuestionApiService.saveDataByPosition(practiceBookQuestionBo);
    }

    @PostMapping("/practiceBookQuestion/update")
    @ApiOperation(value = "修改教辅题目表，用于存储题目信息", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "修改教辅题目表，用于存储题目信息", operationType = OperationLogConstants.OP_TYPE_UPDATE,
        operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult
        updatePracticeBookQuestion(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo) {
        return practiceBookQuestionApiService.updatePracticeBookQuestion(practiceBookQuestionBo);
    }

    @GetMapping("/practiceBookQuestion/detail")
    @ApiOperation(value = "查询教辅题目表，用于存储题目信息详情", httpMethod = "GET")
    @ApiImplicitParam(name = "practiceBookQuestionId", value = "oid", required = true, dataType = "String",
        paramType = "query")
    public AjaxResult<PracticeBookQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
        return practiceBookQuestionApiService.getDetail(oid);
    }

    @GetMapping("/practiceBookQuestion/delete")
    @ApiOperation(value = "删除教辅题目表，用于存储题目信息", httpMethod = "GET")
    @ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
    @OperationLogAnnotation(moduleName = "删除教辅题目表，用于存储题目信息", operationType = OperationLogConstants.OP_TYPE_DELETE,
        operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
        return practiceBookQuestionApiService.delete(oid);
    }

    /**
     * 根据 OID 获取题目信息，如没有题目信息，进行（学科网|好未来）查询 题目数据进行  question_content_json 数据保存,并在 resource_question表中新增
     *
     * @param oid the practice book question oid
     * @param position the practice book question
     * @return {@link AjaxResult }<{@link PracticeBookQuestionVo }>
     */
    @ApiOperation(value = "根据 OID 获取题目信息")
    @GetMapping("/practiceBookQuestion/getOrInitQuestionInfoByOid")
    @OperationLogAnnotation(moduleName = "根据OID获取题目信息", operationType = OperationLogConstants.OP_TYPE_UPDATE)
    public AjaxResult getOrInitQuestionInfoByOid(@RequestParam("oid") String oid ,@RequestParam("practiceBookPageOid") String practiceBookPageOid,  @RequestParam("position") String position) {
        PracticeBookQuestionBo bo = new PracticeBookQuestionBo();
        bo.setOid(oid);
        bo.setPracticeBookPageOid(practiceBookPageOid);
        bo.setPosition(position);
        return this.practiceBookQuestionApiService.getOrInitQuestionInfoByOid(oid, bo);
    }

    /**
     *  根据 OID 重新获取题目
     *  注： 坐标更改 将从第三方（学科网/好未来) 获取数据进行存储并返回
     * @param bo the practice book question oid 教辅题目 OID {oid, position }
     * @return {@link AjaxResult }
     */
    @ApiOperation(value = "根据 OID 重新获取题目")
    @PostMapping("/practiceBookQuestion/reFetchQuestionInfoByOid")
    @OperationLogAnnotation(moduleName = "根据OID重新加载题目信息", operationType = OperationLogConstants.OP_TYPE_UPDATE)
    public AjaxResult reFetchQuestionInfoByOid( @RequestBody PracticeBookQuestionBo bo) {
        String oid = bo.getOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("OID 不能为空");
        }
        String position = bo.getPosition();
        if(StrUtil.isEmpty(position)) {
            return AjaxResult.fail("坐标不能为空");
        }
        return this.practiceBookQuestionApiService.reFetchQuestionInfoByOid(bo);
    }


    /**
     * 完成标注功能：1、修改本题的mark_status为1完成，同时修改out_question_relation_type_status为1确认。
     * 2、同时修改practice_book_page的finish_question_num为finish_question_num+1（注意行级锁，或通过数据库级别的cas解决一致性问题）。
     * 2.1、同时修改practice_book的finish_question_num为finish_question_num+1。
     * 2.2、当practice_book_page表的finish_question_num和question_num相等的时候，设置status为1完成。
     * 3、完成标注后需要同时复制教辅题目信息从practice_book_question表复制到question表中，注意question插入后又要回来更新practice_book_question的question_oid字段。
     */
    @PostMapping("/practiceBookQuestion/mark-done")
    @ApiOperation(value = "完成标注", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "完成标注", operationType = OperationLogConstants.OP_TYPE_UPDATE,
        operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult markPracticeBookQuestion(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo) {
        return practiceBookQuestionApiService.markPracticeBookQuestion(practiceBookQuestionBo);
    }

    /**
     * 取消划题功能：1、软删除practice_book_question。2、修改practice_book_page的question_num为question_num-1。3、修改practice_book的total_question_num为total_question_num-1
     */
    @PostMapping("/practiceBookQuestion/cancel")
    @ApiOperation(value = "取消划题", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "取消划题", operationType = OperationLogConstants.OP_TYPE_DELETE,
        operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult cancelPracticeBookQuestion(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo) {
        return practiceBookQuestionApiService.cancelPracticeBookQuestion(practiceBookQuestionBo);
    }

    /**
     * 查询相似题：根据题目oid、难度，查询出N个指定的学科网题目，实际是查询学科网相似题.
     */
    @PostMapping("/practiceBookQuestion/query-similar-question")
    @ApiOperation(value = "查询相似题", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "查询相似题", operationType = OperationLogConstants.OP_TYPE_QUERY,
        operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult querySimilarQuestion(@Validated @RequestBody PracticeBookQuestionChangeBo practiceBookQuestionChangeBo) {
        return practiceBookQuestionApiService.querySimilarQuestion(practiceBookQuestionChangeBo);
    }

    /**
     * 设为类题：设置某个学科网的题目为：题目oid、指定难度的类题、新题目的json
     */

    /**
     * 设为相似题：使用指定的JSON格式题目替换当前题目
     */
    @PostMapping("/practiceBookQuestion/set-similar-question")
    @ApiOperation(value = "设为相似题", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "设为相似题", operationType = OperationLogConstants.OP_TYPE_UPDATE,
        operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult setSimilarQuestion(@Validated @RequestBody PracticeBookQuestionChangeBo practiceBookQuestionChangeBo) {
        return practiceBookQuestionApiService.setSimilarQuestion(practiceBookQuestionChangeBo);
    }

}
