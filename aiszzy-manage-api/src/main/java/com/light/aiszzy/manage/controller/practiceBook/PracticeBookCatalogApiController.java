package com.light.aiszzy.manage.controller.practiceBook;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookBo;
import com.light.aiszzy.practiceBook.service.PracticeBookApiService;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogExcel;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.aiszzy.practiceBook.service.PracticeBookCatalogApiService;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.attachment.service.AttachmentApiService;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.netty.util.internal.ThrowableUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 教辅目录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:30
 */
@Slf4j
@RestController
@Validated
@Api(value = "", tags = "教辅目录表接口" )
public class PracticeBookCatalogApiController {
	
    @Autowired
    private PracticeBookCatalogApiService practiceBookCatalogApiService;


	@Resource
	private AttachmentApiService attachmentApiService;
	@Resource
	private PracticeBookApiService practiceBookApiService;


//	@PostMapping("/practiceBookCatalog/pageList")
//	@ApiOperation(value = "分页查询教辅目录表",httpMethod = "POST")
//	public AjaxResult<PageInfo<PracticeBookCatalogVo>> getPracticeBookCatalogPageListByCondition(@RequestBody PracticeBookCatalogConditionBo condition){
//		return practiceBookCatalogApiService.getPracticeBookCatalogPageListByCondition(condition);
//    }
//
//	@PostMapping("/practiceBookCatalog/list")
//	@ApiOperation(value = "查询所有教辅目录表",httpMethod = "POST")
//	public AjaxResult<List<PracticeBookCatalogVo>> getPracticeBookCatalogAllListByCondition(@RequestBody PracticeBookCatalogConditionBo condition){
//		condition.setPageNo(SystemConstants.NO_PAGE);
//		return practiceBookCatalogApiService.getPracticeBookCatalogListByCondition(condition);
//	}


	@ApiOperation(value = "根据教辅 OID 获取目录树形结构",httpMethod = "POST")
	@GetMapping("tree/{practiceBookOid}")
	public AjaxResult<List<PracticeBookCatalogVo>> queryTreeByPracticeBookOid(@PathVariable String practiceBookOid) {
		return this.practiceBookCatalogApiService.queryTreeByPracticeBookOid(practiceBookOid);
	}

	@PostMapping("/practiceBookCatalog/add")
	@ApiOperation(value = "新增教辅目录表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增教辅目录表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addPracticeBookCatalog(@Validated @RequestBody PracticeBookCatalogBo practiceBookCatalogBo){
		String practiceBookOid = practiceBookCatalogBo.getPracticeBookOid();
		if(StrUtil.isBlank(practiceBookOid)){
			return AjaxResult.fail("教辅 OID 不能为空");
		}
		return practiceBookCatalogApiService.addPracticeBookCatalog(practiceBookCatalogBo);
    }

	@PostMapping("/practiceBookCatalog/update")
	@ApiOperation(value = "修改教辅目录表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改教辅目录表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updatePracticeBookCatalog(@Validated @RequestBody PracticeBookCatalogBo practiceBookCatalogBo) {
		return practiceBookCatalogApiService.updatePracticeBookCatalog(practiceBookCatalogBo);
	}

	@GetMapping("/practiceBookCatalog/detail")
	@ApiOperation(value = "查询教辅目录表详情")
	public AjaxResult<PracticeBookCatalogVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return practiceBookCatalogApiService.getDetail(oid);
	}

	@GetMapping("/practiceBookCatalog/delete")
	@ApiOperation(value = "删除教辅目录表")
	@OperationLogAnnotation(moduleName = "删除教辅目录表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return practiceBookCatalogApiService.delete(oid);
	}

	@ApiOperation(value = "下载教辅模板")
	@GetMapping("practiceBookCatalog/downloadExcelTemp")
	public void downloadExcelTemp(HttpServletResponse response,@RequestParam("fileName") String fileName) {

		// Configure export parameters with title and sheet name
		ExportParams exportParams = new ExportParams("批量录入教辅目录", "Sheet1");
		exportParams.setCreateHeadRows(true);

		// Add instructions as a second title row
		exportParams.setSecondTitle("说明：1）带*号为必填；\n" +
				"      2)“目录选择”栏为单击选择目录； \n" +
				"      3）子目录（二级、三级目录）需和主目录（一级目录）放一起来表示归属关系；");
		exportParams.setSecondTitleHeight((short) 20);

        try(Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PracticeBookCatalogExcel.class, Collections.emptyList())) {
			// Set response headers for file download
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");

			// Write workbook to response output stream
			workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


	/**
	 *  导入教辅目录
	 * @param practiceBookOid the practice book oid 教辅 OID
	 * @return {@link AjaxResult }<{@link Void }>
	 */
	@ApiOperation(value = "导入教辅目录")
	@PostMapping("practiceBookCatalog/importExcelByPracticeBookOid/{practiceBookOid}")
	public AjaxResult<Void> importExcelByPracticeBookOid(@RequestPart("file") MultipartFile file, @PathVariable String practiceBookOid) {
		AjaxResult<AttachmentVo> attachmentVoAjaxResult = this.attachmentApiService.uploadFile(file);
		if(attachmentVoAjaxResult.isFail() || attachmentVoAjaxResult.getData() == null){
			return AjaxResult.fail("目录文件上传失败");
		}
		AttachmentVo attachmentVo = attachmentVoAjaxResult.getData();
		ImportParams params = new ImportParams();
		params.setTitleRows(2);
		List<PracticeBookCatalogExcel> catalogExcels = null;
        try (InputStream inputStream = file.getInputStream()){
			catalogExcels = ExcelImportUtil.importExcel(inputStream, PracticeBookCatalogExcel.class, params);
        } catch (Exception e) {
			log.error("【导入教辅目录】 教辅 OID:{}, 异常信息:{}", practiceBookOid, ThrowableUtil.stackTraceToString(e));
            return AjaxResult.fail("目录导入失败");
        }

		// 进行存储
		AjaxResult<Void> result = this.practiceBookCatalogApiService.saveExcelDataByPracticeBookOid(practiceBookOid, catalogExcels);
		if(result.isSuccess()) {
			// 更新教辅目录文件地址
			PracticeBookBo bookBo = new PracticeBookBo();
			bookBo.setOid(practiceBookOid);
			bookBo.setCatalogFilePath(attachmentVo.getNginxUrl());
			this.practiceBookApiService.updateCatalogPathByOid(bookBo);
		}
		return result;
	}


	/**
	 *  同级别目录排序
	 * @param practiceBookCatalogBo the practice book catalog bo
	 * @return {@link AjaxResult }<{@link Void }>
	 */
	@ApiOperation(value = "同级别重置排序")
	@PostMapping("resetOrderNum")
	@OperationLogAnnotation(moduleName = "同级别重置排序", operationType = OperationLogConstants.OP_TYPE_UPDATE)
	public AjaxResult<Void> resetOrderNumByParentOid(@RequestBody PracticeBookCatalogBo practiceBookCatalogBo) {
		List<String> catalogList = practiceBookCatalogBo.getCatalogOidList();
		if(CollUtil.isEmpty(catalogList)) {
			return AjaxResult.fail("目录集合不能为空");
		}
		return this.practiceBookCatalogApiService.resetOrderNumByParentOid(practiceBookCatalogBo);
	}

}
