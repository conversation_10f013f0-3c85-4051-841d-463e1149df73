package com.light.aiszzy.manage.controller.practiceBook;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.service.PracticeBookApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.exception.WarningException;
import com.light.enums.PracticeBookReviewStatus;
import com.light.enums.PracticeBookStatus;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;


/**
 * 教辅信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:30
 */
@RestController
@Validated
@Api(value = "", tags = "教辅信息表接口" )
public class PracticeBookApiController {
	
    @Autowired
    private PracticeBookApiService practiceBookApiService;

	@Resource
	private CurrentUserService currentUserService;

	@PostMapping("/practiceBook/pageList")
	@ApiOperation(value = "分页查询教辅信息表",httpMethod = "POST")
	public AjaxResult<PageInfo<PracticeBookVo>> getPracticeBookPageListByCondition(@RequestBody PracticeBookConditionBo condition){
		return practiceBookApiService.getPracticeBookPageListByCondition(condition);
    }

	@PostMapping("/practiceBook/reviewPageList")
	@ApiOperation(value = "分页查询教辅审核表",httpMethod = "POST")
	public AjaxResult<PageInfo<PracticeBookVo>> reviewPageList(@RequestBody PracticeBookConditionBo condition){
		List<Integer> reviewStatus = Arrays.asList(PracticeBookReviewStatus.IN_REVIEW.getCode(),PracticeBookReviewStatus.APPROVED.getCode(),PracticeBookReviewStatus.REJECTED.getCode());
		condition.setReviewStatusList(reviewStatus);
		return practiceBookApiService.getPracticeBookPageListByCondition(condition);
	}

	@PostMapping("/practiceBook/list")
	@ApiOperation(value = "查询所有教辅信息表",httpMethod = "POST")
	public AjaxResult<List<PracticeBookVo>> getPracticeBookAllListByCondition(@RequestBody PracticeBookConditionBo condition){
		return practiceBookApiService.getPracticeBookListByCondition(condition);
	}

	@PostMapping("/practiceBook/add")
	@ApiOperation(value = "新增教辅信息表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增教辅信息表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addPracticeBook(@Validated @RequestBody PracticeBookBo practiceBookBo){
		return practiceBookApiService.addPracticeBook(practiceBookBo);
    }

	@PostMapping("/practiceBook/changeHighShots")
	@ApiOperation(value = "更改是否高拍",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "更改是否高拍", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult changeHighShots(@Validated @RequestBody PracticeBookBo practiceBookBo) {
		String oid = Optional.ofNullable(practiceBookBo.getOid()).orElseThrow(()-> new WarningException("OID 不能为空"));
		Integer isHighShots = Optional.ofNullable(practiceBookBo.getIsHighShots()).orElseThrow(()-> new WarningException("参数不能为空"));
		return practiceBookApiService.updateIsHighShotsByOid(practiceBookBo);
	}

	@PostMapping("/practiceBook/commit")
	@ApiOperation(value = "提交审核",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "提交审核", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult commit(@Validated @RequestBody PracticeBookBo practiceBookBo) {
		String oid = Optional.ofNullable(practiceBookBo.getOid()).orElseThrow(()-> new WarningException("OID 不能为空"));
		practiceBookBo.setUpdateBy(this.currentUserService.getCurrentOid());
		return practiceBookApiService.commit(practiceBookBo);
	}


	@PostMapping("/practiceBook/review")
	@ApiOperation(value = "教辅审核",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "教辅审核", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult review(@Validated @RequestBody PracticeBookBo practiceBookBo) {
		String oid = Optional.ofNullable(practiceBookBo.getOid()).orElseThrow(()-> new WarningException("OID 不能为空"));
		practiceBookBo.setUpdateBy(this.currentUserService.getCurrentOid());
		return practiceBookApiService.review(practiceBookBo);
	}


	@PostMapping("/practiceBook/changeStatus")
	@ApiOperation(value = "上下架",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "上下架", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult changeStatus(@Validated @RequestBody PracticeBookBo practiceBookBo) {
		String oid = Optional.ofNullable(practiceBookBo.getOid()).orElseThrow(()-> new WarningException("OID 不能为空"));
		Integer status = Optional.ofNullable(practiceBookBo.getStatus()).orElseThrow(()-> new WarningException("状态 不能为空"));
		Optional.ofNullable(PracticeBookStatus.fromCode(status)).orElseThrow(()-> new WarningException("未知状态"));
		practiceBookBo.setUpdateBy(this.currentUserService.getCurrentOid());
		return practiceBookApiService.changeStatus(practiceBookBo);
	}


	@PostMapping("/practiceBook/update")
	@ApiOperation(value = "修改教辅信息表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改教辅信息表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updatePracticeBook(@Validated @RequestBody PracticeBookBo practiceBookBo) {
		return practiceBookApiService.updatePracticeBook(practiceBookBo);
	}

	@GetMapping("/practiceBook/detail")
	@ApiOperation(value = "查询教辅信息表详情",httpMethod = "GET")
	public AjaxResult<PracticeBookVo> getDetail( @RequestParam("oid") String oid) {
		return practiceBookApiService.getDetail(oid);
	}

	@GetMapping("/practiceBook/delete")
	@ApiOperation(value = "删除教辅信息表",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除教辅信息表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return practiceBookApiService.delete(oid);
	}


}
