package com.light.aiszzy.manage.controller.ocr;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.light.core.entity.AjaxResult;
import com.light.core.exception.util.ThrowableUtil;
import com.light.ocr.proccesson.ProcessonApi;
import com.light.ocr.tencent.TencentOcrApi;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.models.TextFormula;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "ocr相关接口")
@RestController
@RequestMapping("ocr")
public class OcrController {

    @Resource
    private TencentOcrApi tencentOcrApi;

    @Resource
    private ProcessonApi processonApi;

    @ApiOperation("腾讯公式Ocr识别")
    @PostMapping("tencentFormulaOCR")
    public AjaxResult tencentFormulaOCR(@RequestBody Map<String, Object> body) {
        Object o = body.get("imgBase64");
        if(ObjectUtil.isEmpty(o)) {
            return AjaxResult.fail("图片base64数据不能为空");
        }
        try {
            TextFormula[] textFormulas = this.tencentOcrApi.matchOcr(o.toString());
            List<String> collect = Arrays.stream(textFormulas).map(TextFormula::getDetectedText).collect(Collectors.toList());
            return AjaxResult.success(collect);
        } catch (TencentCloudSDKException e) {
            log.error("【腾讯公式识别】 腾讯公式识别异常:{}", ThrowableUtil.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    @ApiOperation("processon公式Ocr识别")
    @PostMapping("proFormulaOCR")
    public AjaxResult proFormulaOCR(@RequestBody Map<String, Object> body) {
        Object o = body.get("imgBase64");
        if(ObjectUtil.isEmpty(o)) {
            return AjaxResult.fail("图片base64数据不能为空");
        }
        String s = this.processonApi.commonOcr(Base64.getDecoder().decode(o.toString()));
        if(ObjectUtil.isEmpty(s)) {
            return AjaxResult.success();
        }
        return AjaxResult.success(Collections.singletonList(s));
    }
}
