package com.light.aiszzy.manage.controller.homework;

import javax.validation.constraints.NotNull;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import com.light.core.constants.SystemConstants;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import com.github.pagehelper.PageInfo;

import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogVo;
import com.light.aiszzy.homework.service.HomeworkBookCatalogApiService;


/**
 * 作业本目录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:31
 */
@RestController
@Validated
@Api(value = "", tags = "作业本目录接口" )
public class HomeworkBookCatalogApiController {
	
    @Autowired
    private HomeworkBookCatalogApiService homeworkBookCatalogApiService;

	@PostMapping("/homeworkBookCatalog/pageList")
	@ApiOperation(value = "分页查询作业本目录",httpMethod = "POST")
	public AjaxResult<PageInfo<HomeworkBookCatalogVo>> getHomeworkBookCatalogPageListByCondition(@RequestBody HomeworkBookCatalogConditionBo condition){
		return homeworkBookCatalogApiService.getHomeworkBookCatalogPageListByCondition(condition);
    }

	@PostMapping("/homeworkBookCatalog/list")
	@ApiOperation(value = "查询所有作业本目录",httpMethod = "POST")
	public AjaxResult<List<HomeworkBookCatalogVo>> getHomeworkBookCatalogAllListByCondition(@RequestBody HomeworkBookCatalogConditionBo condition){
		return homeworkBookCatalogApiService.getHomeworkBookCatalogListByCondition(condition);
	}

	@PostMapping("/homeworkBookCatalog/listAllChild")
	@ApiOperation(value = "查询作业本目录子目录和作业",httpMethod = "POST")
	public AjaxResult listAllChild(@RequestBody HomeworkBookCatalogConditionBo condition){
		condition.setPageNo(SystemConstants.NO_PAGE);
		condition.setSort("order_num asc");
		return homeworkBookCatalogApiService.listAllChild(condition);
	}

	@PostMapping("/homeworkBookCatalog/listAllChildByBook")
	@ApiOperation(value = "根据作业本查所有目录和作业",httpMethod = "POST")
	public AjaxResult listAllChildByBook(@RequestBody HomeworkBookCatalogConditionBo condition){
		condition.setPageNo(SystemConstants.NO_PAGE);
		condition.setSort("order_num asc");
		return homeworkBookCatalogApiService.listAllChildByBook(condition);
	}

	@PostMapping("/homeworkBookCatalog/sort")
	@ApiOperation(value = "排序目录",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "排序目录", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult sortHomeworkBookCatalog(@Validated @RequestBody HomeworkBookCatalogBo homeworkBookCatalogBo) {
		return homeworkBookCatalogApiService.sortHomeworkBookCatalog(homeworkBookCatalogBo);
	}

	@PostMapping("/homeworkBookCatalog/add")
	@ApiOperation(value = "新增作业本目录",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增作业本目录", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addHomeworkBookCatalog(@Validated @RequestBody HomeworkBookCatalogBo homeworkBookCatalogBo){
		return homeworkBookCatalogApiService.addHomeworkBookCatalog(homeworkBookCatalogBo);
    }

	@PostMapping("/homeworkBookCatalog/update")
	@ApiOperation(value = "修改作业本目录",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改作业本目录", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateHomeworkBookCatalog(@Validated @RequestBody HomeworkBookCatalogBo homeworkBookCatalogBo) {
		return homeworkBookCatalogApiService.updateHomeworkBookCatalog(homeworkBookCatalogBo);
	}

	@GetMapping("/homeworkBookCatalog/detail")
	@ApiOperation(value = "查询作业本目录详情",httpMethod = "GET")
	@ApiImplicitParam(name = "homeworkBookCatalogId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<HomeworkBookCatalogVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return homeworkBookCatalogApiService.getDetail(oid);
	}

	@GetMapping("/homeworkBookCatalog/delete")
	@ApiOperation(value = "删除作业本目录",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除作业本目录", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return homeworkBookCatalogApiService.delete(oid);
	}

	@GetMapping("/homeworkBookCatalog/deleteByBookOid")
	@ApiOperation(value = "删除作业本目录",httpMethod = "GET")
	@OperationLogAnnotation(moduleName = "删除作业本目录", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult deleteByBookOid(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return homeworkBookCatalogApiService.deleteByBookOid(oid);
	}
}
