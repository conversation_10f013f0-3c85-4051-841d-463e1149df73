package com.light.aiszzy.manage.controller.device;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.device.entity.bo.DeviceBo;
import com.light.aiszzy.device.entity.bo.DeviceConditionBo;
import com.light.aiszzy.device.entity.vo.DeviceVo;
import com.light.aiszzy.device.service.DeviceApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.security.service.CurrentAdminService;
import com.light.security.service.CurrentUserService;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.admin.entity.vo.LoginAdminVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 设备表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@RestController
@Validated
@Api(value = "", tags = "设备表接口" )
public class DeviceApiController {
	
    @Autowired
    private DeviceApiService deviceApiService;
	@Resource
	private CurrentAdminService currentAdminService;

	@PostMapping("/device/pageList")
	@ApiOperation(value = "分页查询设备表",httpMethod = "POST")
	public AjaxResult<PageInfo<DeviceVo>> getDevicePageListByCondition(@RequestBody DeviceConditionBo condition){
		return deviceApiService.getDevicePageListByCondition(condition);
    }

	@PostMapping("/device/list")
	@ApiOperation(value = "查询所有设备表",httpMethod = "POST")
	public AjaxResult<List<DeviceVo>> getDeviceAllListByCondition(@RequestBody DeviceConditionBo condition){
		condition.setPageNo(SystemConstants.NO_PAGE);
		return deviceApiService.getDeviceListByCondition(condition);
	}

	@PostMapping("/device/add")
	@ApiOperation(value = "新增设备表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增设备表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addDevice(@Validated @RequestBody DeviceBo deviceBo){
		LoginAdminVo currentAdmin = currentAdminService.getCurrentAdmin();
		String userRealName = currentAdmin.getAdminName();
		deviceBo.setCreateByRealName(userRealName);
		return deviceApiService.addDevice(deviceBo);
    }

	@PostMapping("/device/update")
	@ApiOperation(value = "修改设备表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改设备表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateDevice(@Validated @RequestBody DeviceBo deviceBo) {
		LoginAdminVo currentAdmin = currentAdminService.getCurrentAdmin();
		String userRealName = currentAdmin.getAdminName();
		deviceBo.setUpdateByRealName(userRealName);
		return deviceApiService.updateDevice(deviceBo);
	}

	@GetMapping("/device/detail")
	@ApiOperation(value = "查询设备表详情",httpMethod = "GET")
	@ApiImplicitParam(name = "deviceId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<DeviceVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return deviceApiService.getDetail(oid);
	}

	@GetMapping("/device/delete")
	@ApiOperation(value = "删除设备表",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除设备表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return deviceApiService.delete(oid);
	}
}
