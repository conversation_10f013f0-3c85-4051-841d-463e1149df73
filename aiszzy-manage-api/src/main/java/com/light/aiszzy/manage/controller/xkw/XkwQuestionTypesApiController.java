package com.light.aiszzy.manage.controller.xkw;

import com.light.aiszzy.xkw.xkwQuestionTypes.api.XkwQuestionTypesApi;
import com.light.aiszzy.xkw.xkwQuestionTypes.entity.vo.XkwQuestionTypesVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("xkwQuestionTypes")
@Api(tags = "题型")
public class XkwQuestionTypesApiController {


    @Resource
    private XkwQuestionTypesApi xkwQuestionTypesApi;

    @ApiOperation("根据年级学科获取题型")
    @GetMapping("getByGradeAndSubject")
    public AjaxResult<List<XkwQuestionTypesVo>> getByGradeAndSubject(@RequestParam("grade") Integer grade, @RequestParam("subject") String subject) {
        return  this.xkwQuestionTypesApi.getByGradeAndSubject(grade, subject);
    }
}
