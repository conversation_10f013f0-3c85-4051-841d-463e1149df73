package com.light.aiszzy.manage.controller.resultUploadFile;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.light.aiszzy.device.api.DeviceApi;
import com.light.aiszzy.device.entity.vo.DeviceVo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileBo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileConditionBo;
import com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo;
import com.light.aiszzy.resultUploadFile.service.ResultUploadFileApiService;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.utils.UploadFileMqUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 扫描上传图片
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
@RestController
@Validated
@Api(value = "", tags = "扫描上传图片接口" )
public class ResultUploadFileApiController {
	
    @Autowired
    private ResultUploadFileApiService resultUploadFileApiService;

	@Autowired
	private DeviceApi deviceApi;

	@PostMapping("/homeworkResultUploadFile/pageList")
	@ApiOperation(value = "分页查询扫描上传图片",httpMethod = "POST")
	public AjaxResult<PageInfo<ResultUploadFileVo>> getHomeworkResultUploadFilePageListByCondition(@RequestBody ResultUploadFileConditionBo condition){
		return resultUploadFileApiService.getResultUploadFilePageListByCondition(condition);
	}

	@PostMapping("/homeworkResultUploadFile/list")
	@ApiOperation(value = "查询所有扫描上传图片",httpMethod = "POST")
	public AjaxResult<List<ResultUploadFileVo>> getHomeworkResultUploadFileAllListByCondition(@RequestBody ResultUploadFileConditionBo condition){
		return resultUploadFileApiService.getResultUploadFileListByCondition(condition);
	}

	@PostMapping("/homeworkResultUploadFile/upload")
	@ApiOperation(value = "新增扫描上传图片", httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增扫描上传图片", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addHomeworkResultUploadFile(@Validated @RequestBody ResultUploadFileBo resultUploadFileBo) {
		AjaxResult<DeviceVo> detailByHardwareCode = deviceApi.getDetailByHardwareCode(resultUploadFileBo.getHardwareCode());
		if(detailByHardwareCode.isFail()){
			return detailByHardwareCode;
		}
		DeviceVo deviceVo = detailByHardwareCode.getData();
		resultUploadFileBo.setHardwareCode(resultUploadFileBo.getHardwareCode());
		UploadFileMqUtil.publishMessage(JSONUtil.toJsonStr(resultUploadFileBo));
		Map res = new HashMap();
		res.put("status", deviceVo.getStatus());
		return AjaxResult.success(res);
	}
}
