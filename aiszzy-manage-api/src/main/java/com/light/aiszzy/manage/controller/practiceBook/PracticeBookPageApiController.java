package com.light.aiszzy.manage.controller.practiceBook;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.light.aiszzy.manage.processor.PracticeBookAsyncProcessor;
import com.light.aiszzy.practiceBook.api.PracticeBookQuestionApi;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo;
import com.light.aiszzy.practiceBook.service.PracticeBookApiService;
import com.light.aiszzy.practiceBook.service.PracticeBookPageApiService;
import com.light.base.attachment.service.AttachmentApiService;
import com.light.contants.AISzzyConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.WarningException;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.redis.component.RedisComponent;
import com.light.utils.ProgressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * 教辅目录每页图片记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:30
 */
@Slf4j
@RestController
@Validated
@Api(value = "", tags = "教辅目录每页图片记录表接口" )
public class PracticeBookPageApiController {
	
    @Autowired
    private PracticeBookPageApiService practiceBookPageApiService;

	@Resource
	private AttachmentApiService attachmentApiService;

	@Resource
	private PracticeBookAsyncProcessor practiceBookAsyncProcessor;

	@Resource
	private PracticeBookApiService practiceBookApiService;


	@Resource
	private ProgressService progressService;



	@Resource
	private PracticeBookQuestionApi practiceBookQuestionApi;

	@Resource
	private RedisComponent redisComponent;

	@PostMapping("/practiceBookPage/pageList")
	@ApiOperation(value = "分页查询教辅目录每页图片记录表",httpMethod = "POST")
	public AjaxResult<PageInfo<PracticeBookPageVo>> getPracticeBookPagePageListByCondition(@RequestBody PracticeBookPageConditionBo condition){
		condition.setOrderBy("page_no asc");
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return practiceBookPageApiService.getPracticeBookPagePageListByCondition(condition);
    }

	@PostMapping("/practiceBookPage/list")
	@ApiOperation(value = "查询所有教辅目录每页图片记录表",httpMethod = "POST")
	public AjaxResult<List<PracticeBookPageVo>> getPracticeBookPageAllListByCondition(@RequestBody PracticeBookPageConditionBo condition){
		condition.setOrderBy("page_no asc");
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return practiceBookPageApiService.getPracticeBookPageListByCondition(condition);
	}

	@PostMapping("/practiceBookPage/add")
	@ApiOperation(value = "新增教辅目录每页图片记录表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增教辅目录每页图片记录表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addPracticeBookPage(@Validated @RequestBody PracticeBookPageBo practiceBookPageBo){
		return practiceBookPageApiService.addPracticeBookPage(practiceBookPageBo);
    }

	@PostMapping("/practiceBookPage/update")
	@ApiOperation(value = "修改教辅目录每页图片记录表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改教辅目录每页图片记录表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updatePracticeBookPage(@Validated @RequestBody PracticeBookPageBo practiceBookPageBo) {
		return practiceBookPageApiService.updatePracticeBookPage(practiceBookPageBo);
	}

	@GetMapping("/practiceBookPage/detail")
	@ApiOperation(value = "查询教辅目录每页图片记录表详情",httpMethod = "GET")
	@ApiImplicitParam(name = "practiceBookPageId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<PracticeBookPageVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return practiceBookPageApiService.getDetail(oid);
	}

	@GetMapping("/practiceBookPage/delete")
	@ApiOperation(value = "删除教辅目录每页图片记录表",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除教辅目录每页图片记录表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return practiceBookPageApiService.delete(oid);
	}


	/**
	 *  解析教辅文件信息
	 * @param practiceBookOid the practice book oid 教辅 OID
	 * @return {@link AjaxResult }<{@link Void }>
	 */
	@ApiOperation(value = "解析教辅文件信息")
	@PostMapping("practiceBookPage/analysisByPracticeBookOid/{practiceBookOid}")
	public AjaxResult<Void> analysisByPracticeBookOid(@RequestBody PracticeBookBo bo, @PathVariable String practiceBookOid)  {
		String fileUrl = bo.getFileUrl();
		if(StrUtil.isEmpty(fileUrl)) {
			return AjaxResult.fail("地址不能为空");
		}
		if(!fileUrl.startsWith("http")) {
			fileUrl = "http://" + fileUrl;
		}
		String originalFilename = FileUtil.getName(fileUrl);
		String extName = FileUtil.extName(originalFilename);
        if (extName == null || !Arrays.asList("zip", "pdf").contains(extName)) {
            return AjaxResult.fail("文件类型不符合");
        }
        try {
            this.progressService.create(practiceBookOid, ProgressService.Business.PRACTICE_BOOK_ANALYSIS);
        } catch (WarningException e) {
            return AjaxResult.fail(e.getMessage());
        }
        log.info("【教材解析】 教材解析开始");
		this.practiceBookAsyncProcessor.analysisByPracticeBookOid(fileUrl, practiceBookOid);

		// 更新教辅文件地址
		PracticeBookBo practiceBookBo = new PracticeBookBo();
		practiceBookBo.setOid(practiceBookOid);
		practiceBookBo.setFileType(AISzzyConstants.getPracticeBookFileValByType(extName));
		practiceBookBo.setFileUrl(fileUrl);
		this.practiceBookApiService.updateFileInfoByOid(practiceBookBo);

		return AjaxResult.success();
	}



	@ApiOperation("获取解析进度")
	@GetMapping("practiceBookPage/analysisProcess/{practiceBookOid}")
	public AjaxResult<Map<String, Object>> getTaskProcess( @PathVariable String practiceBookOid) {
		return AjaxResult.success(this.progressService.getProgress(practiceBookOid, ProgressService.Business.PRACTICE_BOOK_ANALYSIS));
	}

}
