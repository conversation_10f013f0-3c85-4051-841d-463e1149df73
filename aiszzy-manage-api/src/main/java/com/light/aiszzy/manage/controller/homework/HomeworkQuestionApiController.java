package com.light.aiszzy.manage.controller.homework;

import javax.validation.constraints.NotNull;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import com.light.core.constants.SystemConstants;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import java.util.List;
import com.github.pagehelper.PageInfo;

import com.light.aiszzy.homework.entity.bo.HomeworkQuestionConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.aiszzy.homework.service.HomeworkQuestionApiService;


/**
 * 作业题目信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 19:28:21
 */
@RestController
@Validated
@Api(value = "", tags = "作业题目信息接口" )
public class HomeworkQuestionApiController {
	
    @Autowired
    private HomeworkQuestionApiService homeworkQuestionApiService;

	@PostMapping("/homeworkQuestion/pageList")
	@ApiOperation(value = "分页查询作业题目信息",httpMethod = "POST")
	public AjaxResult<PageInfo<HomeworkQuestionVo>> getHomeworkQuestionPageListByCondition(@RequestBody HomeworkQuestionConditionBo condition){
		return homeworkQuestionApiService.getHomeworkQuestionPageListByCondition(condition);
    }

	@PostMapping("/homeworkQuestion/list")
	@ApiOperation(value = "查询所有作业题目信息",httpMethod = "POST")
	public AjaxResult<List<HomeworkQuestionVo>> getHomeworkQuestionAllListByCondition(@RequestBody HomeworkQuestionConditionBo condition){
		condition.setPageNo(SystemConstants.NO_PAGE);
		return homeworkQuestionApiService.getHomeworkQuestionListByCondition(condition);
	}

	@PostMapping("/homeworkQuestion/add")
	@ApiOperation(value = "新增作业题目信息",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增作业题目信息", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addHomeworkQuestion(@Validated @RequestBody HomeworkQuestionBo homeworkQuestionBo){
		return homeworkQuestionApiService.addHomeworkQuestion(homeworkQuestionBo);
    }

	@PostMapping("/homeworkQuestion/update")
	@ApiOperation(value = "修改作业题目信息",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改作业题目信息", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateHomeworkQuestion(@Validated @RequestBody HomeworkQuestionBo homeworkQuestionBo) {
		return homeworkQuestionApiService.updateHomeworkQuestion(homeworkQuestionBo);
	}

	@GetMapping("/homeworkQuestion/detail")
	@ApiOperation(value = "查询作业题目信息详情",httpMethod = "GET")
	@ApiImplicitParam(name = "homeworkQuestionId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<HomeworkQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return homeworkQuestionApiService.getDetail(oid);
	}

	@GetMapping("/homeworkQuestion/delete")
	@ApiOperation(value = "删除作业题目信息",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除作业题目信息", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return homeworkQuestionApiService.delete(oid);
	}
}
