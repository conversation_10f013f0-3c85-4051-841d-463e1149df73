package com.light.aiszzy.manage.controller.xkw;

import com.light.aiszzy.xkw.xkwQuestionDifficulties.api.XkwQuestionDifficultiesApi;
import com.light.aiszzy.xkw.xkwQuestionDifficulties.entity.vo.XkwQuestionDifficultiesVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("XkwQuestionDifficulties")
@Api(tags = "难易度")
public class XkwQuestionDifficultiesApiController {

    @Resource
    private XkwQuestionDifficultiesApi xkwQuestionDifficultiesApi;

    @ApiOperation("所有难易度")
    @GetMapping("all")
    public AjaxResult<List<XkwQuestionDifficultiesVo>> findAll() {
        return  this.xkwQuestionDifficultiesApi.findAll();
    }
}
