package com.light.aiszzy.manage.controller.baseInfo;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.light.aiszzy.baseInfo.entity.dto.OrganizationBoExt;
import com.light.aiszzy.baseInfo.entity.vo.OrganizationVoExt;
import com.light.core.entity.AjaxResult;
import com.light.user.account.api.AccountApi;
import com.light.user.admin.api.AdminApi;
import com.light.user.admin.entity.vo.AdminVo;
import com.light.user.organization.api.OrganizationApi;
import com.light.user.organization.api.OrganizationSetupApi;
import com.light.user.organization.entity.bo.OrganizationConditionBo;
import com.light.user.organization.entity.bo.OrganizationSetupBo;
import com.light.user.organization.entity.vo.OrganizationSetupVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.user.api.UserApi;
import com.light.user.user.api.UserRoleApi;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApiOperation("学校管理")
@RestController
@RequestMapping("school")
public class SchoolApiController {

    @Resource
    private OrganizationApi organizationApi;

    @Resource
    private OrganizationSetupApi organizationSetupApi;

    @Resource
    private AdminApi adminApi;

    @ApiOperation("分页列表")
    @PostMapping("pageList")
    public AjaxResult pageList(@RequestBody OrganizationConditionBo organizationConditionBo) {
        AjaxResult ajaxResult = this.organizationApi.getOrganizationListByCondition(organizationConditionBo);
        return ajaxResult;
    }


    /**
     *  根据组织机构 获取管理员信息
     * @param orgIdList
     * @return {@link Map }<{@link Long }, {@link List }<{@link AdminVo }>>
     */
    private Map<Long, List<AdminVo>> queryMapByOrgIdList(List<Long> orgIdList) {
        AjaxResult<Map<Long, List<AdminVo>>> mapAjaxResult = this.adminApi.queryMapByOrgIdList(orgIdList);
        if(mapAjaxResult.isFail()) {
            return Maps.newHashMap();
        }

        return mapAjaxResult.getData();

    }




    /**
     * 获取组织机构详情
     *
     * @param organizationId 组织机构id
     * @return org detail
     * @throws Exception the exception
     */
    @ApiOperation(value = "获取组织机构详情", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public AjaxResult getOrgDetail(@RequestParam("organizationId") Long organizationId) throws Exception {

        AjaxResult detail = organizationApi.getDetail(organizationId);
        if (detail.isFail() || detail.getData() == null) {
            return detail;
        }
        Map<String, Object> map = (Map<String, Object>) detail.getData();
        OrganizationVoExt organizationVo = JSONObject.parseObject(JSONObject.toJSONString(map.get("organizationVo")), OrganizationVoExt.class);
        if (null != organizationVo) {
            // 副标题，logo,建校日期存org_setup
            AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationId);
            if (byOrgId.isSuccess() && byOrgId.getData() != null) {
                OrganizationSetupVo organizationSetupVo = byOrgId.getData();
                organizationVo.setLogo(organizationSetupVo.getLogo());
                organizationVo.setWebName(organizationSetupVo.getWebName());
                organizationVo.setOtherConfig(organizationSetupVo.getOtherConfig());
            }
            map.put("organizationVo", organizationVo);
        }
        return detail;
    }


    /**
     * 编辑组织机构
     *
     * @param organizationBo 组织机构信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-31 15:37:40
     */
    @ApiOperation(value = "修改组织机构信息", httpMethod = "POST")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult updateOrg(@RequestBody OrganizationBoExt organizationBo) throws Exception {
        AjaxResult ajaxResult = organizationApi.updateOrganization(organizationBo);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationBo.getId());
        if (byOrgId.isFail()) {
            return byOrgId;
        }

        OrganizationSetupVo data = byOrgId.getData();
        OrganizationSetupBo organizationSetupBo = new OrganizationSetupBo();
        organizationSetupBo.setOrganizationId(organizationBo.getId());
        organizationSetupBo.setLogo(organizationBo.getLogo());
        organizationSetupBo.setWebName(organizationBo.getWebName());
        organizationSetupBo.setOtherConfig(organizationBo.getOtherConfig());
        if (data == null) {
            organizationSetupApi.saveOrganizationSetup(organizationSetupBo);
        } else {
            organizationSetupBo.setId(data.getId());
            organizationSetupApi.updateOrganizationSetup(organizationSetupBo);
        }

        return ajaxResult;
    }
}
