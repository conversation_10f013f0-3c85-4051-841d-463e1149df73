package com.light.aiszzy.manage.controller.schoolBook;

import javax.validation.constraints.NotNull;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import com.light.core.constants.SystemConstants;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import java.util.List;
import com.github.pagehelper.PageInfo;

import com.light.aiszzy.schoolBook.entity.bo.SchoolBookConditionBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookBo;
import com.light.aiszzy.schoolBook.entity.bo.BatchSchoolBookBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookVo;
import com.light.aiszzy.schoolBook.entity.vo.PracticeBookWithSchoolVo;
import com.light.aiszzy.schoolBook.service.SchoolBookApiService;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookWithSchoolCountVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookExportVo;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import org.apache.poi.ss.usermodel.Workbook;


/**
 * 教辅或作业本开通记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:31
 */
@RestController
@Validated
@Api(value = "", tags = "教辅或作业本开通记录表接口" )
public class SchoolBookApiController {
	
    @Autowired
    private SchoolBookApiService schoolBookApiService;

	@PostMapping("/schoolBook/pageList")
	@ApiOperation(value = "分页查询教辅或作业本开通记录表",httpMethod = "POST")
	public AjaxResult<PageInfo<SchoolBookVo>> getSchoolBookPageListByCondition(@RequestBody SchoolBookConditionBo condition){
		return schoolBookApiService.getSchoolBookPageListByCondition(condition);
    }

	@PostMapping("/schoolBook/list")
	@ApiOperation(value = "查询所有教辅或作业本开通记录表",httpMethod = "POST")
	public AjaxResult<List<SchoolBookVo>> getSchoolBookAllListByCondition(@RequestBody SchoolBookConditionBo condition){
		return schoolBookApiService.getSchoolBookListByCondition(condition);
	}

	@PostMapping("/schoolBook/add")
	@ApiOperation(value = "新增教辅或作业本开通记录表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增教辅或作业本开通记录表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addSchoolBook(@Validated @RequestBody SchoolBookBo schoolBookBo){
		return schoolBookApiService.addSchoolBook(schoolBookBo);
    }

	@PostMapping("/schoolBook/batchAdd")
	@ApiOperation(value = "批量开通教辅或作业本",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "批量开通教辅或作业本", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult batchAddSchoolBook(@Validated @RequestBody BatchSchoolBookBo batchSchoolBookBo){
		return schoolBookApiService.batchAddSchoolBook(batchSchoolBookBo);
    }

	@PostMapping("/schoolBook/update")
	@ApiOperation(value = "修改教辅或作业本开通记录表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改教辅或作业本开通记录表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateSchoolBook(@Validated @RequestBody SchoolBookBo schoolBookBo) {
		return schoolBookApiService.updateSchoolBook(schoolBookBo);
	}

	@GetMapping("/schoolBook/detail")
	@ApiOperation(value = "查询教辅或作业本开通记录表详情",httpMethod = "GET")
	@ApiImplicitParam(name = "schoolBookId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<SchoolBookVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return schoolBookApiService.getDetail(oid);
	}

	@GetMapping("/schoolBook/delete")
	@ApiOperation(value = "删除教辅或作业本开通记录表",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除教辅或作业本开通记录表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return schoolBookApiService.delete(oid);
	}

	/**
	 * 教辅或作业开通列表（所有教辅列表）：1、先查询教辅列表-分页（复用practiceBookApiService.getPracticeBookPageListByCondition）。2、查询当前页的开通学校数量和使用中（有效期内）的学校数量
	 */
	@PostMapping("/schoolBook/practice-book-with-school-count")
	@ApiOperation(value = "教辅或作业开通列表", httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "教辅或作业开通列表", operationType = OperationLogConstants.OP_TYPE_QUERY,
		operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult<PageInfo<PracticeBookWithSchoolCountVo>> getPracticeBookPageListWithSchoolCount(
		@Validated @RequestBody PracticeBookConditionBo condition) {
		return schoolBookApiService.getPracticeBookPageListWithSchoolCount(condition);
	}

	/**
	 * 查询教辅列表与学校开通信息关联（根据学校，查询学校开通的所有教辅）：支持org_code和book_oid查询条件
	 */
	@PostMapping("/schoolBook/practice-book-with-school-list")
	@ApiOperation(value = "查询教辅列表与学校开通信息关联", httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "查询教辅列表与学校开通信息关联", operationType = OperationLogConstants.OP_TYPE_QUERY,
		operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult<PageInfo<PracticeBookWithSchoolVo>> getPracticeBookWithSchoolPageListByCondition(
		@Validated @RequestBody SchoolBookConditionBo condition) {
		return schoolBookApiService.getPracticeBookWithSchoolPageListByCondition(condition);
	}

	/**
	 * 导出教辅开通列表
	 */
	@PostMapping("/schoolBook/export-practice-book-with-school-count")
	@ApiOperation(value = "导出教辅开通列表", httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "导出教辅开通列表", operationType = OperationLogConstants.OP_TYPE_QUERY,
		operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public void exportPracticeBookWithSchoolCount(@Validated @RequestBody PracticeBookConditionBo condition, 
		HttpServletResponse response) {
		try {
			// 获取导出数据（不分页）
			AjaxResult<List<PracticeBookExportVo>> result = schoolBookApiService.exportPracticeBookWithSchoolCount(condition);
			List<PracticeBookExportVo> exportList = result.getData();

			// 配置导出参数
			ExportParams exportParams = new ExportParams("教辅开通列表", "Sheet1");
			exportParams.setCreateHeadRows(true);

			// 创建Excel工作簿
			try (Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PracticeBookExportVo.class, exportList)) {
				// 设置响应头
				response.setCharacterEncoding("UTF-8");
				response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
				response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("教辅开通列表", "UTF-8") + ".xlsx");

				// 写入响应流
				workbook.write(response.getOutputStream());
			}
		} catch (Exception e) {
			throw new RuntimeException("导出教辅开通列表失败", e);
		}
	}
}
