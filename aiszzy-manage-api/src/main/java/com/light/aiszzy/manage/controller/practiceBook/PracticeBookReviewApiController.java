package com.light.aiszzy.manage.controller.practiceBook;

import javax.validation.constraints.NotNull;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import com.github.pagehelper.PageInfo;

import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookReviewVo;
import com.light.aiszzy.practiceBook.service.PracticeBookReviewApiService;


/**
 * 教辅信息审核
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:31
 */
@RestController
@Validated
@Api(value = "", tags = "教辅信息审核接口" )
public class PracticeBookReviewApiController {
	
    @Autowired
    private PracticeBookReviewApiService practiceBookReviewApiService;

	@PostMapping("/practiceBookReview/pageList")
	@ApiOperation(value = "分页查询教辅信息审核",httpMethod = "POST")
	public AjaxResult<PageInfo<PracticeBookReviewVo>> getPracticeBookReviewPageListByCondition(@RequestBody PracticeBookReviewConditionBo condition){
		return practiceBookReviewApiService.getPracticeBookReviewPageListByCondition(condition);
    }

	@PostMapping("/practiceBookReview/list")
	@ApiOperation(value = "查询所有教辅信息审核",httpMethod = "POST")
	public AjaxResult<List<PracticeBookReviewVo>> getPracticeBookReviewAllListByCondition(@RequestBody PracticeBookReviewConditionBo condition){
		return practiceBookReviewApiService.getPracticeBookReviewListByCondition(condition);
	}

	@PostMapping("/practiceBookReview/add")
	@ApiOperation(value = "新增教辅信息审核",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增教辅信息审核", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addPracticeBookReview(@Validated @RequestBody PracticeBookReviewBo practiceBookReviewBo){
		return practiceBookReviewApiService.addPracticeBookReview(practiceBookReviewBo);
    }

	@PostMapping("/practiceBookReview/update")
	@ApiOperation(value = "修改教辅信息审核",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改教辅信息审核", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updatePracticeBookReview(@Validated @RequestBody PracticeBookReviewBo practiceBookReviewBo) {
		return practiceBookReviewApiService.updatePracticeBookReview(practiceBookReviewBo);
	}

	@GetMapping("/practiceBookReview/detail")
	@ApiOperation(value = "查询教辅信息审核详情",httpMethod = "GET")
	@ApiImplicitParam(name = "practiceBookReviewId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<PracticeBookReviewVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return practiceBookReviewApiService.getDetail(oid);
	}

	@GetMapping("/practiceBookReview/delete")
	@ApiOperation(value = "删除教辅信息审核",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除教辅信息审核", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return practiceBookReviewApiService.delete(oid);
	}
}
