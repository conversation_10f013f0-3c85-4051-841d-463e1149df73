package com.light.aiszzy.manage.controller.xkw;


import com.light.aiszzy.xkwOpen.api.XkwOpenApi;
import com.light.beans.OcrBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "学科网开放接口")
@RestController
@RequestMapping("xkw-open")
public class XkwApiController {

    @Resource
    private XkwOpenApi xkwOpenApi;

    @ApiOperation("OCR题目识别")
    @PostMapping("ocr")
    public AjaxResult ocr(@RequestBody OcrBo ocrBo) {
        return this.xkwOpenApi.ocr(ocrBo);
    }

}
