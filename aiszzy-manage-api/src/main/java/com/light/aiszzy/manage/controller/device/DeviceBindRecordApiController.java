package com.light.aiszzy.manage.controller.device;

import javax.validation.constraints.NotNull;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import com.light.core.constants.SystemConstants;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import java.util.List;
import com.github.pagehelper.PageInfo;

import com.light.aiszzy.device.entity.bo.DeviceBindRecordConditionBo;
import com.light.aiszzy.device.entity.bo.DeviceBindRecordBo;
import com.light.aiszzy.device.entity.vo.DeviceBindRecordVo;
import com.light.aiszzy.device.service.DeviceBindRecordApiService;


/**
 * 设备表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@RestController
@Validated
@Api(value = "", tags = "设备表接口" )
public class DeviceBindRecordApiController {
	
    @Autowired
    private DeviceBindRecordApiService deviceBindRecordApiService;

	@PostMapping("/deviceBindRecord/pageList")
	@ApiOperation(value = "分页查询设备表",httpMethod = "POST")
	public AjaxResult<PageInfo<DeviceBindRecordVo>> getDeviceBindRecordPageListByCondition(@RequestBody DeviceBindRecordConditionBo condition){
		return deviceBindRecordApiService.getDeviceBindRecordPageListByCondition(condition);
    }

	@PostMapping("/deviceBindRecord/list")
	@ApiOperation(value = "查询所有设备表",httpMethod = "POST")
	public AjaxResult<List<DeviceBindRecordVo>> getDeviceBindRecordAllListByCondition(@RequestBody DeviceBindRecordConditionBo condition){
		return deviceBindRecordApiService.getDeviceBindRecordListByCondition(condition);
	}

	@PostMapping("/deviceBindRecord/add")
	@ApiOperation(value = "新增设备表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增设备表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addDeviceBindRecord(@Validated @RequestBody DeviceBindRecordBo deviceBindRecordBo){
		return deviceBindRecordApiService.addDeviceBindRecord(deviceBindRecordBo);
    }

	@PostMapping("/deviceBindRecord/update")
	@ApiOperation(value = "修改设备表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改设备表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateDeviceBindRecord(@Validated @RequestBody DeviceBindRecordBo deviceBindRecordBo) {
		return deviceBindRecordApiService.updateDeviceBindRecord(deviceBindRecordBo);
	}

	@GetMapping("/deviceBindRecord/detail")
	@ApiOperation(value = "查询设备表详情",httpMethod = "GET")
	@ApiImplicitParam(name = "deviceBindRecordId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<DeviceBindRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return deviceBindRecordApiService.getDetail(oid);
	}

	@GetMapping("/deviceBindRecord/delete")
	@ApiOperation(value = "删除设备表",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除设备表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return deviceBindRecordApiService.delete(oid);
	}
}
