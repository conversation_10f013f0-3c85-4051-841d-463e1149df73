package com.light.aiszzy.manage.controller.schoolBook;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordBo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookRecordConditionBo;
import com.light.aiszzy.schoolBook.entity.vo.SchoolBookRecordVo;
import com.light.aiszzy.schoolBook.service.SchoolBookRecordApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 教辅或作业本开通记录详情表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:33
 */
@RestController
@Validated
@Api(value = "", tags = "教辅或作业本开通记录详情表接口" )
public class SchoolBookRecordApiController {
	
    @Autowired
    private SchoolBookRecordApiService schoolBookRecordApiService;

	@PostMapping("/schoolBookRecord/pageList")
	@ApiOperation(value = "分页查询教辅或作业本开通记录详情表",httpMethod = "POST")
	public AjaxResult<PageInfo<SchoolBookRecordVo>> getSchoolBookRecordPageListByCondition(@RequestBody SchoolBookRecordConditionBo condition){
		return schoolBookRecordApiService.getSchoolBookRecordPageListByCondition(condition);
    }

	@PostMapping("/schoolBookRecord/list")
	@ApiOperation(value = "查询所有教辅或作业本开通记录详情表",httpMethod = "POST")
	public AjaxResult<List<SchoolBookRecordVo>> getSchoolBookRecordAllListByCondition(@RequestBody SchoolBookRecordConditionBo condition){
		condition.setPageNo(SystemConstants.NO_PAGE);
		return schoolBookRecordApiService.getSchoolBookRecordListByCondition(condition);
	}

	@PostMapping("/schoolBookRecord/add")
	@ApiOperation(value = "新增教辅或作业本开通记录详情表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增教辅或作业本开通记录详情表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addSchoolBookRecord(@Validated @RequestBody SchoolBookRecordBo schoolBookRecordBo){
		return schoolBookRecordApiService.addSchoolBookRecord(schoolBookRecordBo);
    }

	@PostMapping("/schoolBookRecord/update")
	@ApiOperation(value = "修改教辅或作业本开通记录详情表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改教辅或作业本开通记录详情表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateSchoolBookRecord(@Validated @RequestBody SchoolBookRecordBo schoolBookRecordBo) {
		return schoolBookRecordApiService.updateSchoolBookRecord(schoolBookRecordBo);
	}

	@GetMapping("/schoolBookRecord/detail")
	@ApiOperation(value = "查询教辅或作业本开通记录详情表详情",httpMethod = "GET")
	@ApiImplicitParam(name = "schoolBookRecordId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<SchoolBookRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return schoolBookRecordApiService.getDetail(oid);
	}

	@GetMapping("/schoolBookRecord/delete")
	@ApiOperation(value = "删除教辅或作业本开通记录详情表",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除教辅或作业本开通记录详情表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return schoolBookRecordApiService.delete(oid);
	}
}
