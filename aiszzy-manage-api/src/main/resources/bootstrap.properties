# spring
spring.application.name=light-aiszzy-manage
spring.profiles.active=@env@

# spring cloud
spring.cloud.nacos.discovery.server-addr=@host@
spring.cloud.nacos.discovery.group=@group@
spring.cloud.nacos.config.server-addr=@host@
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.config.shared-configs[0].group=@group@
spring.cloud.nacos.config.shared-configs[0].data-id=application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
spring.cloud.nacos.config.shared-configs[0].refresh=true
spring.cloud.nacos.config.group=@group@
spring.cloud.nacos.config.namespace=@namespace@
spring.cloud.nacos.discovery.namespace=@namespace@
spring.cloud.nacos.config.timeout=10000
