<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.light</groupId>
    <artifactId>aiszzy-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <name>父应用</name>
    <description>父应用</description>

    <properties>
        <spring-boot.version>2.5.1</spring-boot.version>
        <maven.compiler.version>3.1</maven.compiler.version>
        <spring-cloud.version>2020.0.3</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
        <alibaba.nacos.version>2.0.2</alibaba.nacos.version>
        <spring-boot-admin.version>2.4.1</spring-boot-admin.version>
        <spring-boot.mybatis>2.1.4</spring-boot.mybatis>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <tobato.version>1.26.5</tobato.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.3.1</pagehelper.boot.version>
        <druid.version>1.2.6</druid.version>
        <dynamic-ds.version>3.4.0</dynamic-ds.version>
        <commons.io.version>2.10.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <velocity.version>1.7</velocity.version>
        <fastjson.version>1.2.76</fastjson.version>
        <minio.version>8.2.1</minio.version>
        <common-pool.version>2.6.2</common-pool.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <knife4j.version>2.0.5</knife4j.version>
        <logstash.logback.encoder.version>5.2</logstash.logback.encoder.version>

        <spring-boot-maven-plugin.version>2.6.5</spring-boot-maven-plugin.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Alibaba Nacos 配置 -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${alibaba.nacos.version}</version>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Mybatis 依赖配置 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>



            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- Collection 增强Java集合框架 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- 公共资源池 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${common-pool.version}</version>
            </dependency>

            <!-- logback -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash.logback.encoder.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <!--不同环境Profile的唯一id-->
            <id>aiszzy-dev</id>
            <properties>
                <!--env是自定义的字段（名字随便起），自定义字段可以有多个-->
                <env>dev</env>
                <namespace>5e2b00ac-4db2-473b-aafb-7d2c4d240b57</namespace>
                <host>127.0.0.1:8848</host>
                <group>AISZZY</group>
            </properties>
            <!--activation用来指定激活方式，可以根据jdk环境，环境变量，文件的存在或缺失-->
            <!--            <activation>-->
            <!--                &lt;!&ndash;这个字段表示默认激活&ndash;&gt;-->
            <!--                <activeByDefault>true</activeByDefault>-->
            <!--            </activation>-->
        </profile>

        <profile>
            <!--不同环境Profile的唯一id-->
            <id>aiszzy-cl</id>
            <properties>
                <!--env是自定义的字段（名字随便起），自定义字段可以有多个-->
                <env>dev</env>
                <namespace>0c6e9e79-ecde-41f2-a42b-4054e6a7dcea</namespace>
                <host>***********:8848</host>
                <group>AISZZY</group>
            </properties>
            <!--activation用来指定激活方式，可以根据jdk环境，环境变量，文件的存在或缺失-->
            <!--            <activation>-->
            <!--                &lt;!&ndash;这个字段表示默认激活&ndash;&gt;-->
            <!--                <activeByDefault>true</activeByDefault>-->
            <!--            </activation>-->
        </profile>
        <profile>
            <!--不同环境Profile的唯一id-->
            <id>aiszzy-sunqb</id>
            <properties>
                <!--env是自定义的字段（名字随便起），自定义字段可以有多个-->
                <env>dev</env>
                <namespace>8cc3e1c9-9293-49ed-ba8d-d4e9ce239332</namespace>
                <host>***********:8848</host>
                <group>AISZZY</group>
            </properties>
            <!--activation用来指定激活方式，可以根据jdk环境，环境变量，文件的存在或缺失-->
            <!--            <activation>-->
            <!--                &lt;!&ndash;这个字段表示默认激活&ndash;&gt;-->
            <!--                <activeByDefault>true</activeByDefault>-->
            <!--            </activation>-->
        </profile>
        

    </profiles>

    <modules>
        <module>aiszzy</module>
        <module>aiszzy-manage-api</module>
        <module>aiszzy-web-api</module>
    </modules>
</project>