package com.light.aiszzy.web.controller.xkw;

import com.light.aiszzy.xkw.xkwKnowledgePoints.api.XkwKnowledgePointsApi;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsConditionBo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo;
import com.light.contants.AISzzyConstants;
import com.light.core.entity.AjaxResult;
import com.light.utils.TreeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("xkwKnowledgePoints")
@Api(tags = "学科网知识点")
public class XkwKnowledgePointsApiController {


    @Resource
    private XkwKnowledgePointsApi xkwKnowledgePointsApi;

    @ApiOperation("根据年级学科获取知识点")
    @GetMapping("getTreeByGradeAndSubject")
    public AjaxResult<List<XkwKnowledgePointsVo>> getByGradeAndSubject(@RequestParam("grade") Integer grade, @RequestParam("subject") String subject) {
        XkwKnowledgePointsConditionBo bo = new XkwKnowledgePointsConditionBo();
        bo.setStageId(AISzzyConstants.getXkwStageByGrade(grade));
        bo.setSubject(AISzzyConstants.getXkwSubject(subject));
        AjaxResult<List<XkwKnowledgePointsVo>> result = this.xkwKnowledgePointsApi.getXkwKnowledgePointsListByCondition(bo);
        if(result.isFail()) {
            return result;
        }
        List<XkwKnowledgePointsVo> tree = TreeUtil.tree(result.getData(), "0", x -> {});
        return AjaxResult.success(tree);
    }

    @ApiOperation("根据学段学科获取知识点")
    @GetMapping("getTreeByStageAndSubject")
    public AjaxResult<List<XkwKnowledgePointsVo>> getByStageAndSubject(@RequestParam("stage") Integer stage, @RequestParam("subject") String subject) {
        XkwKnowledgePointsConditionBo bo = new XkwKnowledgePointsConditionBo();
        bo.setStageId(stage);
        bo.setSubject(AISzzyConstants.getXkwSubject(subject));
        AjaxResult<List<XkwKnowledgePointsVo>> result = this.xkwKnowledgePointsApi.getXkwKnowledgePointsListByCondition(bo);
        if(result.isFail()) {
            return result;
        }
        List<XkwKnowledgePointsVo> tree = TreeUtil.tree(result.getData(), "0", x -> {});
        return AjaxResult.success(tree);
    }
}
