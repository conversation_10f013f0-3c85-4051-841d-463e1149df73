package com.light.aiszzy.web.controller.basicInfo;

import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.security.service.CurrentUserService;
import com.light.user.campus.api.CampusApi;
import com.light.user.campus.entity.bo.CampusBo;
import com.light.user.campus.entity.bo.CampusConditionBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 校区管理
 *
 * <AUTHOR>
 * @date 2022 /3/29 14:37
 */
@RestController
@RequestMapping("/campus")
@Api(value = "校区管理", tags = "校区管理")
public class CampusController {

    @Resource
    private CampusApi campusApi;

    @Resource
    private CurrentUserService currentUserService;

    /**
     * 获取校区详情
     *
     * @param campusId 校区
     * @return org detail
     * @throws Exception the exception
     */
    @ApiOperation(value = "获取校区详情", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public AjaxResult getCampusDetail(@RequestParam("campusId") Long campusId) throws Exception {
        return campusApi.getDetail(campusId);
    }

    /**
     * 修改校区
     *
     * @param campusBo 校区信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:40:40
     */
    @ApiOperation(value = "修改校区", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult updateCampus(@RequestBody CampusBo campusBo) throws Exception {
        if(!this.isCurrentOrg(campusBo.getId())) {
            return AjaxResult.fail("无权操作该校区");
        }
        campusBo.setOrganizationId(null);
        return campusApi.updateCampus(campusBo);
    }

    /**
     * 新增校区
     *
     * @param campusBo 校区信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:41:11
     */
    @ApiOperation(value = "新增校区", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public AjaxResult addCampus(@RequestBody CampusBo campusBo) {
        Long organizationId = this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        campusBo.setOrganizationId(organizationId);
        return campusApi.addCampus(campusBo);
    }

    /**
     * 删除校区
     *
     * @param campusId 校区id
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:42:28
     */
    @ApiOperation(value = "删除校区", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/del", method = RequestMethod.GET)
    public AjaxResult delCampus(@RequestParam("campusId") Long campusId) throws Exception {
        if(!this.isCurrentOrg(campusId)) {
            return AjaxResult.fail("无权操作该校区");
        }
        return campusApi.delete(campusId);
    }


    private boolean isCurrentOrg(Long campusId) {
        Long organizationId = this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        CampusConditionBo bo = new CampusConditionBo();
        bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        bo.setOrganizationId(organizationId);
        bo.setId(campusId);
        Optional<Object> data = Optional.ofNullable(this.campusApi.getCampusListByCondition(bo).getData());
        return data.isPresent();
    }

    /**
     * 分页查询校区信息
     *
     * @param campusConditionBo 校区查询条件信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "分页查询织机构信息", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public AjaxResult listCampus(@RequestBody CampusConditionBo campusConditionBo) {
        campusConditionBo.setIsDelete(StatusEnum.NO.getCode());
        campusConditionBo.setOrganizationId(this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId());
        return campusApi.getCampusListByCondition(campusConditionBo);
    }

}
