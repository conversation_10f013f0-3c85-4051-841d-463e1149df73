package com.light.aiszzy.web.controller.client;

import cn.hutool.json.JSONUtil;
import com.light.aiszzy.device.api.DeviceApi;
import com.light.aiszzy.device.entity.bo.DeviceBo;
import com.light.aiszzy.device.entity.vo.DeviceVo;
import com.light.aiszzy.resultUploadFile.entity.bo.ResultUploadFileBo;
import com.light.aiszzy.resultUploadFile.service.ResultUploadFileApiService;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.utils.UploadFileMqUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备客户端接口，给安卓设备使用
 * 
 * <AUTHOR>
 * @date 2025/7/16 18:57
 */
@Slf4j
@RestController
@RequestMapping("/client/device")
@Api(value = "设备客户端接口", tags = "设备客户端接口")
@Validated
public class ClientDeviceController {

    @Resource
    private DeviceApi deviceApi;

    /**
     * 设备激活功能：安卓端通过设备软件序列号激活设备，如果已激活则更新激活信息。需要安卓传参： 1. 软件激活码 2. 硬件序列号
     */
    @PostMapping("/activate")
    @ApiOperation(value = "设备激活", notes = "安卓端通过设备软件序列号激活设备，如果已激活则更新激活信息")
    public AjaxResult activateDevice(@Validated @RequestBody DeviceBo deviceBo) {
        log.info("设备激活请求，软件激活码：{}", deviceBo.getActivationCode());
        try {
            return deviceApi.activateDevice(deviceBo);
        } catch (Exception e) {
            log.error("设备激活失败，软件激活码：{}，错误信息：{}", deviceBo.getActivationCode(), e.getMessage(), e);
            return AjaxResult.fail("设备激活失败：" + e.getMessage());
        }
    }

    /**
     * 设备信息查询功能：安卓端通过设备软件序列号查询设备信息
     */
    @GetMapping("/query")
    @ApiOperation(value = "设备信息查询", notes = "安卓端通过设备软件序列号查询设备信息")
    public AjaxResult<DeviceVo> queryDevice(@ApiParam(value = "软件激活码（设备软件序列号）", required = false) String activationCode,
        @ApiParam(value = "硬件序列号", required = false) String hardwareCode) {
        log.info("设备信息查询请求，软件激活码：{}", activationCode);
        try {
            AjaxResult<DeviceVo> result =
                deviceApi.getDetailByActivationCodeAndHardwareCode(activationCode, hardwareCode);
            if (result.isSuccess() && result.getData() != null && result.getData().getId() != null) {
                return result;
            } else {
                return AjaxResult.fail("未找到对应的设备信息");
            }
        } catch (Exception e) {
            log.error("设备信息查询失败，软件激活码：{}，错误信息：{}", activationCode, e.getMessage(), e);
            return AjaxResult.fail("设备信息查询失败：" + e.getMessage());
        }
    }

    /**
     * 已激活设备信息查询功能：安卓端通过设备软件序列号查询已激活的设备信息
     */
    @GetMapping("/queryActivated")
    @ApiOperation(value = "已激活设备信息查询", notes = "安卓端通过设备软件序列号查询已激活的设备信息")
    public AjaxResult<DeviceVo> queryActivatedDevice(
        @ApiParam(value = "软件激活码（设备软件序列号）", required = false) String activationCode,
        @ApiParam(value = "硬件序列号", required = false) String hardwareCode) {
        log.info("已激活设备信息查询请求，软件激活码：{}", activationCode);
        try {
            AjaxResult<DeviceVo> result =
                deviceApi.getDetailByActivationCodeAndHardwareCodeForActivated(activationCode, hardwareCode);
            if (result.isSuccess() && result.getData() != null && result.getData().getId() != null) {
                return result;
            } else {
                return AjaxResult.fail("未找到对应的已激活设备信息");
            }
        } catch (Exception e) {
            log.error("已激活设备信息查询失败，软件激活码：{}，错误信息：{}", activationCode, e.getMessage(), e);
            return AjaxResult.fail("已激活设备信息查询失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload")
    @ApiOperation(value = "新增扫描上传图片", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "新增扫描上传图片", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addResultUploadFile(@Validated @RequestBody ResultUploadFileBo resultUploadFileBo) {
        AjaxResult<DeviceVo> detailByHardwareCode = deviceApi.getDetailByHardwareCode(resultUploadFileBo.getHardwareCode());
        if(detailByHardwareCode.isFail()){
            return detailByHardwareCode;
        }
        DeviceVo deviceVo = detailByHardwareCode.getData();
        resultUploadFileBo.setOrgCode(deviceVo.getOrgCode());
        UploadFileMqUtil.publishMessage(JSONUtil.toJsonStr(resultUploadFileBo));
        Map res = new HashMap();
        res.put("status", deviceVo.getStatus());
        return AjaxResult.success(res);
    }

}
