package com.light.aiszzy.web.controller.basicInfo;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSONObject;
import com.light.aiszzy.web.model.StudentConditionBoExt;
import com.light.aiszzy.web.model.StudentExportVo;
import com.light.aiszzy.web.model.StudentImportModel;
import com.light.aiszzy.web.utils.DateKit;
import com.light.aiszzy.web.utils.EasyPoiUtil;
import com.light.aiszzy.web.utils.ExcelKit;
import com.light.aiszzy.web.utils.NumberKit;
import com.light.base.area.api.AreaApi;
import com.light.contants.ConstantsInteger;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.enums.Enable;
import com.light.enums.UserIdentityType;
import com.light.user.account.api.AccountApi;
import com.light.user.student.api.StudentApi;
import com.light.user.student.api.StudentTransferApi;
import com.light.user.student.entity.bo.StudentBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.user.api.KeeperRelationApi;
import com.light.user.user.entity.bo.KeeperRelationBo;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.entity.vo.KeeperRelationVo;
import com.light.user.user.entity.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

/**
 * 学生信息管理 // sunqbtodo 陈乐修改公共接口后这里需要调整。目前无法带出基础信息。
 *
 * <AUTHOR>
 * @date 2022 /3/25 14:15
 */
@Slf4j
@RestController
@RequestMapping("/student")
@Api(value = "学生信息管理", tags = "学生信息管理")
public class StudentController {

    /**
     * 导入错误信息提示
     */
    String ERROR_IMPORT_MESSAGE = "请根据模板内容正确填写信息，可能的错误类型如下：\n" +
            "1）表头内容被修改或被删除；\n" +
            "2）新增sheet表格；\n" +
            "3）未删除示例数据。";

    String DATE_FORMAT_STYLE_1 = "yyyy-MM-dd";
    String DATE_FORMAT_STYLE_2 = "yyyy-MM-dd HH:mm:ss";
    String DATE_FORMAT_STYLE_3 = "yyyy年MM月dd日";
    String DATE_FORMAT_STYPE_4="yyyy年MM月";


    @Resource
    private StudentApi studentApi;
    @Resource
    private StudentTransferApi studentTransferApi;
    @Resource
    private KeeperRelationApi keeperRelationApi;
    @Resource
    private AccountApi accountApi;


    @Resource
    private AreaApi areaApi;

    /**
     * 下载学生的导入模板
     *
     * @param response the response
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 -03-30 15:50:18
     */
    @GetMapping("/template")
    @ApiOperation(value = "下载学生导入模板", httpMethod = "GET")
    public void studentTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String studentTemplateName = "学生导入模板" + ".xls";
            URI uri = new URI(null, null, studentTemplateName, null);
            OutputStream out = response.getOutputStream();
            response.reset();
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.addHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
            response.addHeader("Access-Control-Allow-Headers", "*");
            response.addHeader("Access-Control-Allow-Methods", "*");
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Content-Disposition",
                "attachment; filename=" + uri.toASCIIString() + ";filename*=utf-8''" + uri.toASCIIString());
            response.setContentType("application/vnd.ms-excel");
            InputStream in = ResourceUtil.getStream("template/student_import_template.xls");
            // 后续如果需要修改模板可以在这里改写流
            IoUtil.copy(in, out);
        } catch (SecurityException | IllegalArgumentException | URISyntaxException e) {
            log.error("/student/template error1:", e);
        } catch (Exception e) {
            log.error("/student/template error2:", e);
        }
    }

    /**
     * 导入学生
     *
     * @param request the request
     * @param file the file
     * @param organizationId 组织机构id
     * @param classesId 班级id
     * @param enrollmentYear 入学年份
     * @return ajax result
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 -04-01 16:50:53
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入学生", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult importStudentUser(HttpServletRequest request, MultipartFile file, Long organizationId,
        Long classesId, Integer enrollmentYear) throws Exception {
        ImportParams params = new ImportParams();
        // 表头设置为2行
        params.setHeadRows(2);
        params.setTitleRows(1);
        params.setNeedVerify(true);
        ExcelImportResult<StudentImportModel> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), StudentImportModel.class, params);
        } catch (Exception e) {
            return AjaxResult.fail(ERROR_IMPORT_MESSAGE);
        }
        List<StudentImportModel> list = result.getList();

        List<StudentBo> studentBos = new ArrayList<>();
        int errorCount = 0;
        for (StudentImportModel studentImportModel : list) {
            try {
                StudentBo studentBo =
                    transferStudentImportModel2StudentBo(studentImportModel, organizationId, classesId, enrollmentYear);
                studentBos.add(studentBo);
            } catch (Exception e) {
                errorCount++;
            }
        }
        if (CollectionUtils.isNotEmpty(result.getFailList())) {
            errorCount += result.getFailList().stream()
                .filter(obj -> StringUtils.isNotBlank(obj.getRealName()) || StringUtils.isNotBlank(obj.getSex()))
                .count();
        }
        if (CollectionUtils.isEmpty(studentBos)) {
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.setMsg("成功导入0条数据，另有" + errorCount + "条数据由于姓名和手机号字段填写有误，导入失败");
            return ajaxResult;
        }
        AjaxResult ajaxResult = studentApi.addStudentBatch(studentBos);
        if (ajaxResult.isFail()) {
            ajaxResult.setMsg(ajaxResult.getMsg() + ERROR_IMPORT_MESSAGE);
            return ajaxResult;
        }
        ajaxResult = new AjaxResult();
        String sb = "成功导入" + studentBos.size() + "条数据，另有" + errorCount + "条数据由于姓名或性别字段填写有误，导入失败";
        ajaxResult.setMsg(sb);
        return ajaxResult;
    }

    /**
     * 学生导出TODO，未完成
     *
     * @param studentConditionBo the student condition bo
     * @param response the response
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 -04-13 10:36:28
     */
    @PostMapping("/export")
    @ApiOperation(value = "学生导出", httpMethod = "POST")
    public void exportUser(@RequestBody StudentConditionBoExt studentConditionBo, HttpServletResponse response)
        throws Exception {
        studentConditionBo.setIsDelete(Enable.NO.getValue());
        studentConditionBo.setRealName(StringUtils.isBlank(studentConditionBo.getRealName())
            ? studentConditionBo.getSearchName() : studentConditionBo.getRealName());
        studentConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        studentConditionBo.setGrade(null);
        studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult ajaxResult = studentApi.getStudentListByCondition(studentConditionBo);
        if (ajaxResult.isFail()) {
            return;
        }
        Map<String, Object> result = (Map<String, Object>)ajaxResult.getData();
        List<Map> list = (List)result.get("list");
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<StudentVo> studentVoList = JSONObject.parseArray(JSONObject.toJSONString(list), StudentVo.class);
        List<StudentExportVo> studentExportVoList = new ArrayList<>();
        for (StudentVo studentVo : studentVoList) {
            StudentExportVo studentExportVo = transferStudentVo2StudentExportVo(studentVo);
            studentExportVoList.add(studentExportVo);
        }
        String fileName = "学生导出";
        // 判断字段是否有值，有值则前端勾选，不隐藏
        for (StudentExportVo studentExportVo : studentExportVoList) {
            StudentImportModel studentImportModel = studentConditionBo.getStudentImportModel();
            EasyPoiUtil<StudentExportVo> easyPoiUtil = new EasyPoiUtil<>();
            easyPoiUtil.t = studentExportVo;
            Map<String, String> describe = org.apache.commons.beanutils.BeanUtils.describe(studentImportModel);
            Set<String> keySet = describe.keySet();
            for (String s : keySet) {
                // 排除class转换Map时默认新增的class key
                if (!s.equals("class") && !s.equals("organizationId") && !s.equals("classesId")) {
                    if (null == describe.get(s)) {
                        easyPoiUtil.hihdColumn(s, true);
                    } else {
                        easyPoiUtil.hihdColumn(s, false);
                    }
                }
            }
        }

        ExcelKit.exportExcel2Response(studentExportVoList, null, fileName, StudentExportVo.class, fileName, response);
    }

    /**
     * 根据userOid获取学生监护人信息
     *
     * @param userOid the user oid
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "根据userOid获取学生监护人信息", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/keeps", method = RequestMethod.GET)
    public AjaxResult keeps(@RequestParam("userOid") String userOid) throws Exception {
        return keeperRelationApi.getByUserOid(userOid);
    }

    /**
     * 对象转换
     *
     * @param studentImportModel the student import model
     * @param organizationId the organization id
     * @param classesId the classes id
     * @return student bo
     * <AUTHOR>
     * @date 2022 -04-12 14:56:04
     */
    private StudentBo transferStudentImportModel2StudentBo(StudentImportModel studentImportModel, Long organizationId,
        Long classesId, Integer enrollmentYear) {
        StudentBo studentBo = new StudentBo();
        UserBo userBo = new UserBo();
        userBo.setRealName(studentImportModel.getRealName());
        studentBo.setStudentCode(studentImportModel.getStudentCode());
        studentBo.setStudentNo(studentImportModel.getStudentNo());
        userBo.setSex(NumberKit.str2Integer(studentImportModel.getSex()));
        userBo.setBirthday(DateKit.string2Date(studentImportModel.getBirthday(), DATE_FORMAT_STYLE_3));
        userBo.setNation(NumberKit.str2Integer(studentImportModel.getNation()));
        userBo.setNationalityId(NumberKit.str2Long(studentImportModel.getNationalityId()));
        userBo.setNativePlaceId(studentImportModel.getNativePlaceId());
        userBo.setIdentityType(studentImportModel.getIdentityType());
        userBo.setIdentityCardNumber(studentImportModel.getIdentityCardNumber());
        userBo.setOverseasChinese(NumberKit.str2Integer(studentImportModel.getOverseasChinese()));
        userBo.setRegisteredResidence(studentImportModel.getRegisteredResidence());
        userBo.setRegistrationType(NumberKit.str2Integer(studentImportModel.getRegistrationType()));
        userBo.setHomeAddress(studentImportModel.getHomeAddress());
        userBo.setPhone(studentImportModel.getPhone());
        // 不生成账户的手机号
        userBo.setIsUserPhoneAsAccount(Enable.NO.getValue());
        studentBo.setIsSingle(NumberKit.str2Integer(studentImportModel.getIsSingle()));
        userBo.setHealthStatus(NumberKit.str2Integer(studentImportModel.getHealthType()));
        userBo.setOrganizationId(organizationId);
        userBo.setClazzId(classesId);
        userBo.setUserIdentityType(UserIdentityType.STUDENT.getValue());
        studentBo.setClassesId(classesId);
        studentBo.setGeneratorAccount(true);
        studentBo.setEnrollmentYear(enrollmentYear == null ? null : enrollmentYear.longValue());
        studentBo.setSchoolId(organizationId);
        studentBo.setUser(userBo);
        // 监护人1信息
        KeeperRelationBo keeperRelationBoMain = new KeeperRelationBo();
        UserBo mainUserBo = new UserBo();
        mainUserBo.setRealName(studentImportModel.getMainRealName());
        keeperRelationBoMain.setRelationType(NumberKit.str2Integer(studentImportModel.getMainRelationType()));
        mainUserBo.setNation(NumberKit.str2Integer(studentImportModel.getMainNation()));
        mainUserBo.setHealthStatus(NumberKit.str2Integer(studentImportModel.getMainHealthType()));
        mainUserBo.setPhone(studentImportModel.getMainPhone());
        keeperRelationBoMain.setMainUserBo(mainUserBo);
        // 监护人2信息
        KeeperRelationBo keeperRelationBoSecond = new KeeperRelationBo();
        UserBo secondUserBo = new UserBo();
        secondUserBo.setRealName(studentImportModel.getSecondRealName());
        keeperRelationBoSecond.setRelationType(NumberKit.str2Integer(studentImportModel.getSecondRelationType()));
        secondUserBo.setNation(NumberKit.str2Integer(studentImportModel.getSecondNation()));
        secondUserBo.setHealthStatus(NumberKit.str2Integer(studentImportModel.getSecondHealthType()));
        secondUserBo.setPhone(studentImportModel.getSecondPhone());
        keeperRelationBoSecond.setMainUserBo(secondUserBo);
        studentBo.setKeeperRelationList(Arrays.asList(keeperRelationBoMain, keeperRelationBoSecond));
        return studentBo;
    }

    /**
     * 学生信息转换为导出对象
     *
     * @param studentVo the student vo
     * @return student export vo
     * <AUTHOR>
     * @date 2022 -04-13 11:03:57
     */
    public StudentExportVo transferStudentVo2StudentExportVo(StudentVo studentVo) {
        UserVo userVo = studentVo.getUserVo();

        StudentExportVo studentExportVo = new StudentExportVo();
        studentExportVo.setRealName(userVo.getRealName());
        studentExportVo.setEnrollmentYear(NumberKit.long2Str(studentVo.getEnrollmentYear()));
        studentExportVo.setStudentCode(studentVo.getStudentCode());
        studentExportVo.setStudentNo(studentVo.getStudentNo());
        studentExportVo.setSex(NumberKit.integer2Str(userVo.getSex()));
        studentExportVo.setBirthday(DateKit.date2String(userVo.getBirthday(), DATE_FORMAT_STYLE_3));
        studentExportVo.setNation(studentVo.getStudentCode());
        studentExportVo.setNationalityId(NumberKit.long2Str(userVo.getNationalityId()));
        studentExportVo.setNativePlaceId(userVo.getNativePlaceId());
        studentExportVo.setIdentityType(userVo.getIdentityType());
        studentExportVo.setIdentityCardNumber(userVo.getIdentityCardNumber());
        studentExportVo.setOverseasChinese(NumberKit.integer2Str(userVo.getOverseasChinese()));
        String registeredResidence = "";
        if (userVo.getRegisteredResidenceProvince() != null || userVo.getRegisteredResidenceCity() != null
            || userVo.getRegisteredResidenceArea() != null) {
            String prov = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceProvince()).getData();
            String city = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceCity()).getData();
            String area = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceArea()).getData();
            registeredResidence = registeredResidence.concat(prov == null ? "" : prov).concat(",")
                .concat(city == null ? "" : city).concat(",").concat(area == null ? "" : area).concat(" ");
        }
        registeredResidence = userVo.getRegisteredResidence() == null ? registeredResidence
            : registeredResidence.concat(userVo.getRegisteredResidence());
        studentExportVo.setRegisteredResidence(registeredResidence);
        studentExportVo.setRegistrationType(NumberKit.integer2Str(userVo.getRegistrationType()));
        String homeAddress = "";
        if (userVo.getHomeAddressProvince() != null) {
            String prov = areaApi.getAreaNameFromCache(userVo.getHomeAddressProvince()).getData();
            String city = areaApi.getAreaNameFromCache(userVo.getHomeAddressCity()).getData();
            String area = areaApi.getAreaNameFromCache(userVo.getHomeAddressArea()).getData();
            homeAddress = homeAddress.concat(prov == null ? "" : prov).concat(",").concat(city == null ? "" : city)
                .concat(",").concat(area == null ? "" : area).concat(" ");
        }
        homeAddress = userVo.getHomeAddress() == null ? homeAddress : homeAddress.concat(userVo.getHomeAddress());
        studentExportVo.setHomeAddress(homeAddress);
        studentExportVo.setPhone(userVo.getPhone());
        studentExportVo.setIsSingle(NumberKit.integer2Str(studentVo.getIsSingle()));
        studentExportVo.setHealthType(NumberKit.integer2Str(userVo.getHealthStatus()));

        AjaxResult ajaxResult = keeperRelationApi.getByUserOid(studentVo.getUserOid());
        if (ajaxResult.isFail()) {
            return studentExportVo;
        }

        List<KeeperRelationVo> keeperRelationVoList =
            JSONObject.parseArray(JSONObject.toJSONString(ajaxResult.getData()), KeeperRelationVo.class);
        if (CollectionUtil.isEmpty(keeperRelationVoList)) {
            return studentExportVo;
        }
        KeeperRelationVo keeperRelationVoMain = keeperRelationVoList.get(ConstantsInteger.NUM_0);
        studentExportVo.setMainRealName(StringUtils.defaultString(keeperRelationVoMain.getMainUserVo().getRealName()));
        studentExportVo.setMainRelationType(NumberKit.integer2Str(keeperRelationVoMain.getRelationType()));
        studentExportVo.setMainNation(NumberKit.integer2Str(keeperRelationVoMain.getMainUserVo().getNation()));
        studentExportVo
            .setMainHealthType(NumberKit.integer2Str(keeperRelationVoMain.getMainUserVo().getHealthStatus()));
        studentExportVo.setMainPhone(StringUtils.defaultString(keeperRelationVoMain.getMainUserVo().getPhone()));
        if (keeperRelationVoList.size() > ConstantsInteger.NUM_1) {
            KeeperRelationVo keeperRelationVoSecond = keeperRelationVoList.get(ConstantsInteger.NUM_1);
            studentExportVo
                .setSecondRealName(StringUtils.defaultString(keeperRelationVoSecond.getMainUserVo().getRealName()));
            studentExportVo.setSecondRelationType(NumberKit.integer2Str(keeperRelationVoSecond.getRelationType()));
            studentExportVo.setSecondNation(NumberKit.integer2Str(keeperRelationVoSecond.getMainUserVo().getNation()));
            studentExportVo
                .setSecondHealthType(NumberKit.integer2Str(keeperRelationVoSecond.getMainUserVo().getHealthStatus()));
            studentExportVo
                .setSecondPhone(StringUtils.defaultString(keeperRelationVoSecond.getMainUserVo().getPhone()));
        }
        return studentExportVo;
    }
}
