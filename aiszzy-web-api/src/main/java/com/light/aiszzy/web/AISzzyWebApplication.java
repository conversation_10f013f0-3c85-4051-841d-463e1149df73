package com.light.aiszzy.web;

import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;
import com.alibaba.cloud.nacos.registry.NacosRegistration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

import javax.annotation.PostConstruct;


@EnableFeignClients(basePackages = {"com.light"})
@EnableDiscoveryClient
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class },scanBasePackages ={"com.light"})
public class AISzzyWebApplication extends SpringBootServletInitializer {

    @Value("${server.port}")
    private String port;

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(AISzzyWebApplication.class);
    }

    public static void main( String[] args ) {
        SpringApplication.run(AISzzyWebApplication.class);
    }

    public String protocol = Thread.currentThread().getContextClassLoader().getResource("").getProtocol();

    @Autowired
    private NacosRegistration registration;

    @Autowired
    private NacosAutoServiceRegistration nacosAutoServiceRegistration;

    /**
     * 将服务注册到nacos
     */
    @PostConstruct
    public void nacosServerRegister() {
        if (!protocol.equals("jar")&&registration != null) {
            try {
                Integer tomcatPort = new Integer(port);
                //设置端口号
                registration.setPort(tomcatPort);
                //将服务注册到nacos
                nacosAutoServiceRegistration.start();
            } catch (Exception e) {
            }
        }
    }


}