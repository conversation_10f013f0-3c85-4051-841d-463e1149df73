package com.light.aiszzy.web.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 学生信息导出vo
 *
 * <AUTHOR>
 * @date 2022/4/13 10:43
 */
@Data
public class StudentExportVo {

    @Excel(name = "姓名", groupName = "学生基本信息", width = 20.0, orderNum = "0", fixedIndex = 0)
    private String realName;

    /**
     * 导出比导入多一个此字段
     */
    @Excel(name = "入学年份", groupName = "学生基本信息", width = 20.0, orderNum = "1", fixedIndex = 1)
    private String enrollmentYear;

    @Excel(name = "学籍号", groupName = "学生基本信息", width = 20.0, orderNum = "2", fixedIndex = 2)
    private String studentCode;

    @Excel(name = "学号", groupName = "学生基本信息", width = 20.0, fixedIndex = 3, orderNum = "3")
    private String studentNo;

    @Excel(name = "性别", groupName = "学生基本信息", width = 10.0, fixedIndex = 4, orderNum = "4", replace = {"男_1", "女_2", "_null"})
    private String sex;

    @Excel(name = "出生日期", groupName = "学生基本信息", width = 10.0, fixedIndex = 5, orderNum = "5")
    private String birthday;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "民族", groupName = "学生基本信息", width = 10.0, orderNum = "6", fixedIndex = 6, replace = {"汉族_1", "蒙古族_2", "回族_3", "藏族_4", "维吾尔族_5", "苗族_6", "彝族_7", "壮族_8", "布依族_9", "朝鲜族_10", "满族_11", "侗族_12", "瑶族_13", "白族_14", "土家族_15", "哈尼族_16", "哈萨克族_17", "傣族_18", "黎族_19", "傈僳族_20", "佤族_21", "畲族_22", "高山族_23", "拉祜族_24", "水族_25", "东乡族_26", "纳西族_27", "景颇族_28", "柯尔克孜族_29", "土族_30", "达斡尔族_31", "仫佬族_32", "羌族_33", "布朗族_34", "撒拉族_35", "毛难族_36", "仡佬族_37", "锡伯族_38", "阿昌族_39", "普米族_40", "塔吉克族_41", "怒族_42", "乌孜别克族_43", "俄罗斯族_44", "鄂温克族_45", "崩龙族_46", "保安族_47", "裕固族_48", "京族_49", "塔塔尔族_50", "独龙族_51", "鄂伦春族_52", "赫哲族_53", "门巴族_54", "珞巴族_55", "基诺族_56", "其他_57", "_null"})
    private String nation;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "国籍/地区", groupName = "学生基本信息", width = 10.0, orderNum = "7", fixedIndex = 7, replace = {"中国_1", "其他_2", "_null"})
    private String nationalityId;

    /**
     * 改成输入
     */
    @Excel(name = "籍贯（市）", groupName = "学生基本信息", width = 10.0, orderNum = "8", fixedIndex = 8)
    private String nativePlaceId;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "身份证件类型", groupName = "学生基本信息", width = 10.0, orderNum = "9", fixedIndex = 9, replace = {"居民身份证_1", "香港特区护照/身份证明_2", "港澳特区护照/身份证明_3", "台湾居民来往大陆通行证_4", "境外永久居住证_5", "护照_6", "其他_7", "_null"})
    private String identityType;

    @Excel(name = "身份证件号", groupName = "学生基本信息", width = 20.0, orderNum = "10", fixedIndex = 10)
    private String identityCardNumber;

    @Excel(name = "港澳台侨外", groupName = "学生基本信息", width = 10.0, orderNum = "11", fixedIndex = 11, replace = {"是_1", "否_0", "_null"})
    private String overseasChinese;

    @Excel(name = "户口所在地", groupName = "学生基本信息", width = 10.0, orderNum = "12", fixedIndex = 12)
    private String registeredResidence;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "户口性质", groupName = "学生基本信息", width = 10.0, orderNum = "13", fixedIndex = 13, replace = {"未落户常住户口_1", "农业户口_2", "非农业户口_3", "_null"})
    private String registrationType;

    @Excel(name = "现住址", groupName = "学生基本信息", width = 10.0, orderNum = "14", fixedIndex = 14)
    private String homeAddress;

    @Excel(name = "联系电话", groupName = "学生基本信息", width = 10.0, orderNum = "15", fixedIndex = 15)
    private String phone;

    @Excel(name = "是否独生子女", groupName = "学生基本信息", width = 10.0, orderNum = "16", fixedIndex = 16, replace = {"是_1", "否_0", "_null"})
    private String isSingle;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "健康状况", groupName = "学生基本信息", width = 10.0, orderNum = "17", fixedIndex = 17, replace = {"健康_1", "良好_2", "一般_3", "较弱_4", "残疾_5", "_null"})
    private String healthType;

    /**
     * 监护人一姓名
     */
    @Excel(name = "姓名", groupName = "监护人一信息", width = 20.0, orderNum = "18", fixedIndex = 18)
    private String mainRealName;
    /**
     * 监护人一关系
     */
    @Excel(name = "关系", groupName = "监护人一信息", width = 10.0, orderNum = "19", fixedIndex = 19, replace = {"父亲_1", "母亲_2", "祖父_3", "祖母_4", "外祖父_5", "外祖母_6", "其他_7", "_null"})
    private String mainRelationType;
    /**
     * 监护人一民族
     */
    @Excel(name = "民族", groupName = "监护人一信息", width = 10.0, orderNum = "20", fixedIndex = 20, replace = {"汉族_1", "蒙古族_2", "回族_3", "藏族_4", "维吾尔族_5", "苗族_6", "彝族_7", "壮族_8", "布依族_9", "朝鲜族_10", "满族_11", "侗族_12", "瑶族_13", "白族_14", "土家族_15", "哈尼族_16", "哈萨克族_17", "傣族_18", "黎族_19", "傈僳族_20", "佤族_21", "畲族_22", "高山族_23", "拉祜族_24", "水族_25", "东乡族_26", "纳西族_27", "景颇族_28", "柯尔克孜族_29", "土族_30", "达斡尔族_31", "仫佬族_32", "羌族_33", "布朗族_34", "撒拉族_35", "毛难族_36", "仡佬族_37", "锡伯族_38", "阿昌族_39", "普米族_40", "塔吉克族_41", "怒族_42", "乌孜别克族_43", "俄罗斯族_44", "鄂温克族_45", "崩龙族_46", "保安族_47", "裕固族_48", "京族_49", "塔塔尔族_50", "独龙族_51", "鄂伦春族_52", "赫哲族_53", "门巴族_54", "珞巴族_55", "基诺族_56", "其他_57", "_null"})
    private String mainNation;
    /**
     * 监护人一健康状况
     */
    @Excel(name = "健康状况", groupName = "监护人一信息", width = 10.0, orderNum = "21", fixedIndex = 21, replace = {"健康_1", "良好_2", "一般_3", "较弱_4", "残疾_5", "_null"})
    private String mainHealthType;
    /**
     * 监护人一手机号码
     */
    @Excel(name = "手机号码", groupName = "监护人一信息", width = 10.0, orderNum = "22", fixedIndex = 22)
    private String mainPhone;

    /**
     * 监护人二姓名
     */
    @Excel(name = "姓名", groupName = "监护人二信息", width = 20.0, orderNum = "23", fixedIndex = 23)
    private String secondRealName;
    /**
     * 监护人二关系
     */
    @Excel(name = "关系", groupName = "监护人二信息", width = 10.0, orderNum = "24", fixedIndex = 24, replace = {"父亲_1", "母亲_2", "祖父_3", "祖母_4", "外祖父_5", "外祖母_6", "其他_7", "_null"})
    private String secondRelationType;
    /**
     * 监护人二民族
     */
    @Excel(name = "民族", groupName = "监护人二信息", width = 10.0, orderNum = "25", fixedIndex = 25, replace = {"汉族_1", "蒙古族_2", "回族_3", "藏族_4", "维吾尔族_5", "苗族_6", "彝族_7", "壮族_8", "布依族_9", "朝鲜族_10", "满族_11", "侗族_12", "瑶族_13", "白族_14", "土家族_15", "哈尼族_16", "哈萨克族_17", "傣族_18", "黎族_19", "傈僳族_20", "佤族_21", "畲族_22", "高山族_23", "拉祜族_24", "水族_25", "东乡族_26", "纳西族_27", "景颇族_28", "柯尔克孜族_29", "土族_30", "达斡尔族_31", "仫佬族_32", "羌族_33", "布朗族_34", "撒拉族_35", "毛难族_36", "仡佬族_37", "锡伯族_38", "阿昌族_39", "普米族_40", "塔吉克族_41", "怒族_42", "乌孜别克族_43", "俄罗斯族_44", "鄂温克族_45", "崩龙族_46", "保安族_47", "裕固族_48", "京族_49", "塔塔尔族_50", "独龙族_51", "鄂伦春族_52", "赫哲族_53", "门巴族_54", "珞巴族_55", "基诺族_56", "其他_57", "_null"})
    private String secondNation;
    /**
     * 监护人二健康状况
     */
    @Excel(name = "健康状况", groupName = "监护人二信息", width = 10.0, orderNum = "26", fixedIndex = 26, replace = {"健康_1", "良好_2", "一般_3", "较弱_4", "残疾_5", "_null"})
    private String secondHealthType;
    /**
     * 监护人二手机号
     */
    @Excel(name = "手机号码", groupName = "监护人二信息", width = 10.0, orderNum = "27", fixedIndex = 27)
    private String secondPhone;
}
