package com.light.aiszzy.web.controller.userPaper;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.light.aiszzy.userPaper.api.UserPaperApi;
import com.light.aiszzy.userPaper.api.UserPaperQuestionApi;
import com.light.aiszzy.userPaper.entity.bo.UserPaperBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperConditionBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperVo;
import com.light.aiszzy.web.processor.UserPaperAsyncProcessor;
import com.light.contants.AISzzyConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.WarningException;
import com.light.redis.component.RedisComponent;
import com.light.security.service.CurrentUserService;
import com.light.user.user.api.UserApi;
import com.light.user.user.entity.vo.UserVo;
import com.light.utils.ProgressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户试卷
 */
@Api(tags = "校本")
@RestController
@RequestMapping("userPaper")
public class UserPaperApiController {


    @Resource
    private UserPaperApi userPaperApi;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private UserPaperAsyncProcessor userPaperAsyncProcessor;

    @Resource
    private UserPaperQuestionApi userPaperQuestionApi;

    @Resource
    private UserApi userApi;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private ProgressService progressService;

    @ApiOperation("我的校本")
    @PostMapping("myPageList")
    public AjaxResult myPageList(@RequestBody UserPaperConditionBo bo) {
        bo.setUserOid(this.currentUserService.getCurrentOid());
        if(bo.getStage() != null) {
            bo.setGradeList(AISzzyConstants.getXkwGradeListByStage(bo.getStage()));
        }
        bo.setOrderBy("create_time desc");
        return this.userPaperApi.getUserPaperPageListByCondition(bo);
    }



    @ApiOperation("校本卷库")
    @PostMapping("list")
    public AjaxResult list(@RequestBody UserPaperConditionBo bo) {
        String code = this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode();
        bo.setOrgCode(code);
        if(bo.getStage() != null) {
            bo.setGradeList(AISzzyConstants.getXkwGradeListByStage(bo.getStage()));
        }
        bo.setIsPublish(StatusEnum.YES.getCode());
        bo.setOrderBy("publish_time desc");
        AjaxResult<PageInfo<UserPaperVo>> userPaperResult = this.userPaperApi.getUserPaperPageListByCondition(bo);
        if(userPaperResult.isFail()) {
            return userPaperResult;
        }
        // 查询用户姓名
        PageInfo<UserPaperVo> data = userPaperResult.getData();
        List<String> userOidList = data.getList().stream().map(UserPaperVo::getUserOid).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(userOidList)) {
            // 填充用户信息
            Optional.ofNullable(this.userApi.getByOidList(userOidList).getData())
                    // 转换成 key: userOid , val-> userVo
                    .map(x -> x.stream().collect(Collectors.toMap(UserVo::getOid, e -> e)))
                    // 进行填充数据
                    .ifPresent(x -> {
                        data.getList().stream()
                                // 过滤 userOid 不存在
                                .filter(userPaper -> StrUtil.isNotEmpty(userPaper.getUserOid()))
                                // 过滤未获取到的用户信息
                                .filter(userPaperVo -> x.containsKey(userPaperVo.getUserOid()))
                                // 设置 用户昵称
                                .forEach(userPaperVo -> {
                                    userPaperVo.setUserRealName(x.get(userPaperVo.getUserOid()).getRealName());
                                });
                    });
        }
        return AjaxResult.success(data);
    }

    @ApiOperation("详情")
    @GetMapping("detail")
    public AjaxResult detail(@RequestParam("oid") String oid) {
        AjaxResult<UserPaperVo> detail = this.userPaperApi.getDetail(oid);
        UserPaperVo data = detail.getData();
        if(detail.isFail() || data == null) {
            return detail;
        }
        // 查询题目
        List<UserPaperQuestionVo> userPaperQuestionVos = this.userPaperQuestionApi.queryByUserPaperOid(oid).getData();
        data.setUserPaperQuestionList(userPaperQuestionVos);
        // 填充用户信息
        Optional.ofNullable(this.userApi.getByOidList(Collections.singletonList(data.getUserOid())).getData())
                .filter(CollUtil::isNotEmpty)
                // 进行填充数据
                .map(x-> x.get(0))
                .ifPresent(x -> {
                    data.setUserRealName(x.getRealName());
                });
        // 校验是否为当前用户 ，非当前用户 进行是否发布校验 ，未发布 不返回数据
        if (data.getUserOid().equals(this.currentUserService.getCurrentOid()) || data.getIsPublish().equals(StatusEnum.YES.getCode())) {
            return AjaxResult.success(data);
        }
        return AjaxResult.success();
    }


    @ApiOperation("修改发布状态")
    @PostMapping("changePublish")
    public AjaxResult publish(@RequestBody UserPaperBo bo) {
        String oid = bo.getOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("OID 不能为空");
        }
        Integer isPublish = bo.getIsPublish();
        List<Integer> stausList = Arrays.asList(StatusEnum.YES.getCode(), StatusEnum.NO.getCode());
        if(!stausList.contains(isPublish)) {
            return AjaxResult.fail("发布状态不对");
        }
        return this.userPaperApi.updatePublishByOid(oid, isPublish);
    }

    /**
     *  新增试卷
     * @param userPaperBo the user paper 试卷
     * @return {@link AjaxResult }
     */
    @ApiOperation("添加数据")
    @PostMapping("add")
    public AjaxResult add(@RequestBody UserPaperBo userPaperBo) {
        userPaperBo.setUserOid(this.currentUserService.getCurrentOid());
        userPaperBo.setOid(IdUtil.fastSimpleUUID());
        userPaperBo.setOrgCode(this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        // 解析数据
        String fileUrl = userPaperBo.getFileUrl();
        if(StrUtil.isEmpty(fileUrl)) {
            return AjaxResult.fail("资源地址不能为空");
        }
        if(!fileUrl.startsWith("http")) {
            fileUrl = "http://" + fileUrl;
        }
        userPaperBo.setFileUrl(fileUrl);
        // 创建任务
        try {
            this.progressService.create(userPaperBo.getOid(), ProgressService.Business.USER_PAPER_ANALYSIS);
        } catch (WarningException e) {
            return AjaxResult.fail(e.getMessage());
        }
        // 解析
         this.userPaperAsyncProcessor.analysisByUserPaperOid(fileUrl, userPaperBo);

        return this.userPaperApi.addUserPaper(userPaperBo);
    }

    @ApiOperation("获取解析进度")
    @GetMapping("/analysisProcess/{userPaperOid}")
    public AjaxResult getTaskProcess(@PathVariable String userPaperOid) {
        return AjaxResult.success(this.progressService.getProgress(userPaperOid, ProgressService.Business.USER_PAPER_ANALYSIS));
    }

    @ApiOperation("删除数据")
    @PostMapping("delete")
    public AjaxResult delete(@RequestBody UserPaperBo userPaperBo) {
        return this.userPaperApi.deleteByOidAndUserOid(userPaperBo.getOid(), this.currentUserService.getCurrentOid());
    }


}
