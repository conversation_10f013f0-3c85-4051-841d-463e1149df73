package com.light.aiszzy.web.model;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.light.aiszzy.web.config.AccountGenComponent;
import com.light.base.area.service.AreaApiService;
import com.light.core.entity.AjaxResult;
import com.light.core.entity.Progress;
import com.light.redis.component.RedisComponent;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/7/22
 */
@Slf4j
public class StudentImportThread extends Thread {

    private UserApiService userApiService = SpringUtil.getBean(UserApiService.class);

    private RedisComponent redisComponent = SpringUtil.getBean(RedisComponent.class);

    private AreaApiService areaApiService = SpringUtil.getBean(AreaApiService.class);

    private AccountGenComponent accountGenComponent = SpringUtil.getBean(AccountGenComponent.class);

    private String redisKey;

    private List<UserBo> users;

    public StudentImportThread(String redisKey, List<UserBo> users) {
        this.redisKey = redisKey;
        this.users = users;
    }

    @Override
    @Async
    public void run() {
        Object object = redisComponent.get(redisKey);
        Progress progress = JSON.parseObject(object.toString(), Progress.class);
        Integer total = progress.getTotal();
        Integer successCount = progress.getSuccess();
        Integer failureCount = progress.getFail();
        StringBuilder errorMessage = new StringBuilder("");
        for(UserBo user : users) {
            try{
                String classesNo = user.getClassesName().replace("班", "");
                if(Integer.parseInt(classesNo) < 10) {//班级序号补0
                    classesNo = "0" + classesNo;
                }

                // 学号为1为 补0
                int stuNoLen = 2;
                String remark = user.getRemark();
                if(remark.length() < stuNoLen){
                    remark = "0" + remark;
                }
                //获取账号
                AjaxResult account = accountGenComponent.generateAccount(user.getOrganizationId(), user.getEnrollmentYear()
                        , remark, classesNo);
                if(account.isSuccess()) {
                    user.setAccountName(account.getData().toString());
//                    user.setEmail(user.getAccountName()+emailDefaultSuffix);
                }else{
                    failureCount ++;
                    //设置错误信息
                    errorMessage.append("第"+ (successCount + failureCount) +"条数据生成账号失败，原因是："+account.getMsg()+"，请重新导入该条数据\n");
                    continue;
                }
                List<UserBo> adds = new ArrayList<UserBo>();
                adds.add(user);
                AjaxResult result = userApiService.addBatchUser(adds);
                if (result.isSuccess()) {
                    successCount ++ ;
                    progress.setSuccess(successCount);
                    progress.setProgress(((successCount + failureCount) * 100d) / total);
                    progress.setErrorMessage(errorMessage.toString());
                    redisComponent.set(redisKey, JSON.toJSONString(progress));
                    //刷新班级的缓存
                    this.redisComponent.deleteByPrex("cache:GroupUserController:getClassGroupUserList_" + user.getClazzId());
                }else{
                    failureCount++;
                    errorMessage.append("第"+ (successCount + failureCount) +"条数据导入失败，原因："+result.getMsg()+"\n");
                    progress.setFail( failureCount);
                    progress.setProgress( ((successCount + failureCount) * 100d) / total);
                    progress.setErrorMessage(errorMessage.toString());
                    redisComponent.set(redisKey, JSON.toJSONString(progress));
                }
            }catch(Exception e){
                log.error("导入学生线程出错：{}", getStackTrace(e));
                failureCount++;
                errorMessage.append("第"+ (successCount + failureCount) +"条数据导入失败\n");
                progress.setFail( failureCount);
                progress.setProgress( ((successCount + failureCount) * 100d) / total);
                progress.setErrorMessage(errorMessage.toString());
                redisComponent.set(redisKey, JSON.toJSONString(progress));
            }
        }
        progress.setProgress(100d);
        progress.setErrorMessage(errorMessage.toString());
        progress.setFail(failureCount);
        progress.setSuccess(successCount);
        redisComponent.set(redisKey, JSON.toJSONString(progress));
    }

    /**
     * 获取堆栈信息
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
}
