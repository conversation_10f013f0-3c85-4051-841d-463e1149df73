package com.light.aiszzy.web.utils;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.light.redis.utils.ExcelUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.util.List;
import java.util.NoSuchElementException;

/**
 * <AUTHOR>
 * @date 2022/4/13 10:04
 */
public class ExcelKit extends ExcelUtils {

    /**
     * 导入excel
     *
     * @param file
     * @param titleRows
     * @param headerRows
     * @param pojoClass
     * @param keyIndex
     * @param <T>
     * @return
     * @throws IOException
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, Class<T> pojoClass, Integer keyIndex) throws IOException {
        if (file == null) {
            return null;
        }
        InputStream inputStream = file.getInputStream();
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setSaveUrl("/excel/");
        params.setNeedSave(true);
        params.setKeyIndex(keyIndex);
        try {
            return ExcelImportUtil.importExcel(inputStream, pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new IOException("excel文件不能为空");
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        } finally {
            inputStream.close();
        }
    }

    /**
     * excel 导出
     *
     * @param list 数据
     * @param title 标题
     * @param sheetName sheet名称
     * @param pojoClass pojo类型
     * @param fileName 文件名称
     * @param response the response
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-16 15:04:21
     */
    public static void exportExcel2Response(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName, HttpServletResponse response) throws Exception {
        export2ResponseConvert(list, pojoClass, fileName, response, new ExportParams(title, sheetName, ExcelType.XSSF));
    }

    /**
     * 默认的 excel 导出
     *
     * @param list 数据
     * @param pojoClass pojo类型
     * @param fileName 文件名称
     * @param response
     * @param exportParams 导出参数
     */
    private static void export2ResponseConvert(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response, ExportParams exportParams) throws Exception {
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        downLoadExcel2Response(fileName, response, workbook);
    }

    /**
     * 下载exlcel到response
     *
     * @param fileName the file name
     * @param response the response
     * @param workbook the workbook
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-16 11:35:37
     */
    private static void downLoadExcel2Response(String fileName, HttpServletResponse response, Workbook workbook) throws Exception {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest();
        OutputStream out = response.getOutputStream();
        response.reset();
        String excelFileName = fileName + ".xls";
        URI uri = new URI(null, null, excelFileName, null);
        response.setHeader("Content-Disposition", "attachment; filename=" + uri.toASCIIString() + ";filename*=utf-8''"
                + uri.toASCIIString());
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.addHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
        response.addHeader("Access-Control-Allow-Headers", "*");
        response.addHeader("Access-Control-Allow-Methods", "*");
        response.addHeader("Access-Control-Allow-Credentials", "true");
        response.setContentType("application/vnd.ms-excel");
        workbook.write(out);
    }
}
