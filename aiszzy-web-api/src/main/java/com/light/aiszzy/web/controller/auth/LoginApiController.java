package com.light.aiszzy.web.controller.auth;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.light.base.config.api.ConfigApi;
import com.light.base.config.entity.bo.RedisBo;
import com.light.base.config.entity.vo.ApiBusinessConfigVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import com.light.user.account.api.AccountApi;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.organization.api.OrganizationApi;
import com.light.user.organization.entity.vo.OrganizationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 作业
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-28 16:01:05
 */
@RestController
@Validated
@Api(value = "", tags = "首页接口")
public class LoginApiController {

    @Resource
    private OrganizationApi organizationApi;

    @Resource
    private ConfigApi configApi;

    @Resource
    private AccountApi accountApi;

    @GetMapping("/school/check")
    @ApiOperation(value = "查询开通学校信息", httpMethod = "GET")
    public AjaxResult schoolCheck(@RequestParam("accountName") String accountName) {

        if (StringUtils.isEmpty(accountName)) {
            return AjaxResult.fail("参数错误");
        }
        String businessConfig = configApi.getConfigValue(SystemConstants.API_LOGIN_BUSINESS_CHECK_CONFIG).getData();
        if (StrUtil.isEmpty(businessConfig) || !JSONUtil.isJson(businessConfig)) {
            return AjaxResult.success(true);
        }
        final ApiBusinessConfigVo apiBusinessConfigVo = JSONUtil.toBean(businessConfig, ApiBusinessConfigVo.class);

        // 开启限制
        final String open = apiBusinessConfigVo.getOpen();
        if (StrUtil.isEmpty(open) || open.trim().equalsIgnoreCase(Boolean.FALSE.toString())) {
            return AjaxResult.success(true);
        }
        RedisBo redisBo = new RedisBo();
        redisBo.setKey(apiBusinessConfigVo.getRedisKey() + accountName);
        redisBo.setTime(5 * 60);

        AjaxResult accountInfoByAccountName = accountApi.getAccountInfoByAccountName(accountName);
        if (accountInfoByAccountName.isFail()) {
            return AjaxResult.success(false);
        }
        LoginAccountVo loginAccountVo = JSONUtil.toBean(JSONUtil.toJsonStr(accountInfoByAccountName.getData()), LoginAccountVo.class);

        if (loginAccountVo.getCurrentUser() == null || loginAccountVo.getCurrentUser().getUserOrg() == null || loginAccountVo.getCurrentUser().getUserOrg().getCode() == null) {
            return AjaxResult.success(false);
        }
        Long organizationId = loginAccountVo.getCurrentUser().getUserOrg().getOrganizationId();
        AjaxResult<OrganizationVo> orgResult = this.organizationApi.getDetailById(organizationId);
        OrganizationVo data = orgResult.getData();
        if (data.getIsUsed().equals(StatusEnum.NO.getCode())) {
            AjaxResult fail = AjaxResult.fail("学校暂未开通或已过期");
            redisBo.setValue(JSONUtil.toJsonStr(fail));
            this.configApi.setRedis(redisBo);
            return AjaxResult.success(false);
        }
        Date now = new Date();
        if(now.after(data.getStartTime()) && now.before(data.getDeadline())) {
            AjaxResult success = AjaxResult.success("验证成功");
            redisBo.setValue(JSONUtil.toJsonStr(success));
            this.configApi.setRedis(redisBo);
            return AjaxResult.success(true);
        }else {
            AjaxResult fail = AjaxResult.fail("学校暂未开通或已过期");
            redisBo.setValue(JSONUtil.toJsonStr(fail));
            this.configApi.setRedis(redisBo);
            return AjaxResult.success(false);
        }
    }
}
