package com.light.aiszzy.web.controller.basicInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.aiszzy.baseInfo.entity.dto.ClassesBoExt;
import com.light.base.config.api.ConfigApi;
import com.light.base.dictionary.api.DictionaryDataApi;
import com.light.base.dictionary.entity.bo.DictionaryDataListConditionBo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.security.service.CurrentUserService;
import com.light.user.clazz.api.ClazzApi;
import com.light.user.clazz.api.ClazzHeadmasterApi;
import com.light.user.clazz.entity.bo.ClazzConditionBo;
import com.light.user.clazz.entity.bo.ClazzHeadmasterBo;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.organization.entity.bo.OrganizationConfig;
import com.light.user.role.api.RoleApi;
import com.light.user.role.entity.bo.RoleConditionBo;
import com.light.user.teacher.api.TeacherApi;
import com.light.user.teacher.api.TeacherClassesSubjectApi;
import com.light.user.teacher.entity.bo.TeacherClassesSubjectBo;
import com.light.user.teacher.entity.vo.TeacherClassesSubjectVo;
import com.light.user.user.api.UserRoleApi;
import com.light.user.user.entity.bo.UserRoleBo;
import com.light.user.user.entity.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Api(tags = "班级管理")
@RestController
@RequestMapping("classes")
public class ClassesApiController {


    @Resource
    private ClazzApi clazzApi;

    @Resource
    private DictionaryDataApi dictionaryDataApi;

    @Resource
    private TeacherClassesSubjectApi teacherClassesSubjectApi;

    @Resource
    private ClazzHeadmasterApi clazzHeadmasterApi;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private TeacherApi teacherApi;

    @Resource
    private RoleApi roleApi;

    @Resource
    private UserRoleApi userRoleApi;

    @Resource
    private ConfigApi configApi;

    @ApiOperation("任教列表")
    @PostMapping("teachingList")
    public AjaxResult teachingList(@RequestBody ClazzConditionBo bo) {
        bo.setOrganizationId(this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId());
        AjaxResult<PageInfo<ClazzVo>> calzzResult = this.clazzApi.queryPageListByCondition(bo);
        if(calzzResult.isFail() || calzzResult.getData() == null ) {
            return calzzResult;
        }

        // 班级
        PageInfo<ClazzVo> pageInfo = calzzResult.getData();
        List<ClazzVo> list = pageInfo.getList();

        Map<String, Object> result = Maps.newHashMap();
        result.put("total", pageInfo.getTotal());
        if(CollUtil.isEmpty(list)) {
            return AjaxResult.success(result);
        }

        // 学科
        DictionaryDataListConditionBo dictBo = new DictionaryDataListConditionBo();
        dictBo.setDictType("subject");
        AjaxResult<List<DictionaryDataVo>> availableList = this.dictionaryDataApi.getAvailableList(dictBo);
        List<DictionaryDataVo> data = availableList.getData();

        // 查询任教
        List<Long> classIdList = list.stream().map(ClazzVo::getId).distinct().collect(Collectors.toList());
        Map<Long, List<TeacherClassesSubjectVo>> clazzTeacherClassListMap = this.teacherClassesSubjectApi.queryMapByClassIdList(classIdList).getData();

        // 查询班主任信息
        Map<Long, List<ClazzHeadmasterVo>> clazzHeadmasterListMap = Optional.ofNullable(this.clazzHeadmasterApi.queryByClassIdList(classIdList).getData())
                .filter(CollUtil::isNotEmpty)
                .map(x -> x.stream().collect(Collectors.groupingBy(ClazzHeadmasterVo::getClassesId)))
                .orElse(Maps.newHashMap());

        // 组装信息
        List<Map<String, Object>> classMapList = list.stream().map(x -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("id", x.getId());
            map.put("classesName", x.getClassesName());

            //班主任
            List<ClazzHeadmasterVo> clazzHeadmasterMap = Optional.ofNullable(clazzHeadmasterListMap.get(x.getId()))
                    .filter(CollUtil::isNotEmpty)
                    .orElse(Lists.newArrayList());

            map.put("headmaster", clazzHeadmasterMap);

            // 处理任教
            Map<String, List<TeacherClassesSubjectVo>> sujectTeacherClassMap =
                    Optional.ofNullable(clazzTeacherClassListMap.get(x.getId()))
                            // 任教信息按照学科分组
                            .map(classesSubjectVos ->
                                    classesSubjectVos.stream().collect(Collectors.groupingBy(TeacherClassesSubjectVo::getSubjectCode)))
                            // 未获得 返回默认 empty map
                            .orElse(Maps.newHashMap());

            data.forEach(subject -> {
                String subjectCode = subject.getDictValue();
                List<UserVo> userVos = Optional.ofNullable(sujectTeacherClassMap.get(subjectCode))
                        .filter(CollUtil::isNotEmpty)
                        // 将老师 名称进行拼接
                        .map(voList ->
                                voList.stream().map(TeacherClassesSubjectVo::getUserVo).collect(Collectors.toList())
                        ).orElse(Lists.newArrayList());
                map.put(subjectCode, userVos);
            });
            return map;
        }).collect(Collectors.toList());

        result.put("list", classMapList);

        return AjaxResult.success(result);
    }


    @ApiOperation("保存任教信息")
    @PostMapping("saveTeaching/{classesId}")
    public AjaxResult saveTeaching(@PathVariable("classesId") Long classesId,  @RequestBody ClassesBoExt bo) {

        // 查询班主任信息
        List<String> masterUserOid =
                Optional.ofNullable(this.clazzHeadmasterApi.queryByClassIdList(Collections.singletonList(classesId)).getData())
                                .map(x-> x.stream().map(ClazzHeadmasterVo::getUser).map(UserVo::getOid)
                                        .filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList()))
                        .orElse(Lists.newArrayList());

        // 任教老师 OID
         List<String> teacherClassesUserOidList =
                Optional.ofNullable(this.teacherClassesSubjectApi.queryMapByClassIdList(Collections.singletonList(classesId)).getData())
                        .map(x->
                                x.values().stream().flatMap(Collection::stream).map(TeacherClassesSubjectVo::getUserVo)
                                        .map(UserVo::getOid).collect(Collectors.toList()))
                        .orElse(Lists.newArrayList());


        List<ClazzHeadmasterBo> masterList = bo.getMasterList();
        AjaxResult ajaxResult = this.clazzHeadmasterApi.delAndSaveBatchByClassId(classesId, masterList);
        if(ajaxResult.isFail()) {
            return ajaxResult;
        }

        // 处理班主任信息
        this.handleHeadmaster(masterList, masterUserOid);


        List<TeacherClassesSubjectBo> teacherClassesSubjectList = bo.getTeacherClassesSubjectList();
        AjaxResult teacherClassSubjectResult = this.teacherClassesSubjectApi.delAndSaveBatchByClassId(classesId, teacherClassesSubjectList);

        if (teacherClassSubjectResult.isSuccess()) {
            // 处理任教角色
            this.handleTeacherRole(teacherClassesSubjectList, teacherClassesUserOidList);
        }
        return teacherClassSubjectResult;
    }


    /**
     * 根据 code 查询角色 ID
     * @param code the role code
     * @return {@link Long }
     */
    private Long queryByCode(String code) {
        if(StrUtil.isEmpty(code)) {
            return null;
        }
        RoleConditionBo roleConditionBo = new RoleConditionBo();
        roleConditionBo.setCode(code);
        return Optional.ofNullable( this.roleApi.getRoleListByCondition(roleConditionBo).getData()).map(x-> {
                    com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(x));
                    return jsonObject.getJSONArray("list");
                })
                .filter(CollUtil::isNotEmpty)
                .map(x-> x.getJSONObject(0).getLong("id")).orElse(null);
    }


    /**
     *  处理班主任信息
     * @param masterList 新班主任相关信息
     * @param headmasterUserOidList 原班主任用户 OID集合
     */
    private void handleHeadmaster(List<ClazzHeadmasterBo> masterList, List<String> headmasterUserOidList) {
        Long organizationId = this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        // 处理班主任角色
        Optional.ofNullable(masterList)
                .map(x->
                        x.stream().map(ClazzHeadmasterBo::getUserOid).filter(StrUtil::isNotEmpty).collect(Collectors.toList())
                )
                .ifPresent(headmasterUserOidList::addAll);
        if(CollUtil.isNotEmpty(headmasterUserOidList)) {
            // 1. 删除 班主任角色
            String headmasterRoleCode = this.getHeadmasterRoleCode();
            Optional.of(headmasterUserOidList.parallelStream().distinct()
                    .filter(StrUtil::isNotEmpty).collect(Collectors.toList())
                    ).ifPresent(x-> {
                        this.userRoleApi.deleteByUserOidListAndRoleCode(x, headmasterRoleCode);
                    });


            Long roleId = this.queryByCode(headmasterRoleCode);
            // 2. 过滤有任教信息的用户信息 存储班主任角色
            List<UserRoleBo> userRoleBos = headmasterUserOidList.parallelStream().distinct()
                    .filter(StrUtil::isNotEmpty)
                    .filter(x -> CollUtil.isNotEmpty(this.clazzHeadmasterApi.getByUserOid(x).getData()))
                    .map(x-> {
                        UserRoleBo userRoleBo = new UserRoleBo();
                        userRoleBo.setRoleId(roleId);
                        userRoleBo.setUserOid(x);
                        userRoleBo.setOrganizationId(organizationId);
                        return userRoleBo;
                    }).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(userRoleBos)) {
                this.userRoleApi.saveBatchUserRole(userRoleBos);
            }
        }
    }


    /**
     *  处理班主任信息
     * @param teacherClassesSubjectBoList 新任教相关信息
     * @param teacherUserOidList 原任教用户 OID集合
     */
    private void handleTeacherRole(List<TeacherClassesSubjectBo> teacherClassesSubjectBoList, List<String> teacherUserOidList) {
        Long organizationId = this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        // 处理班主任角色
        Optional.ofNullable(teacherClassesSubjectBoList)
                .map(x->
                        x.stream().map(TeacherClassesSubjectBo::getTeacherUserOid).filter(StrUtil::isNotEmpty).collect(Collectors.toList())
                )
                .ifPresent(teacherUserOidList::addAll);
        if(CollUtil.isNotEmpty(teacherUserOidList)) {
            // 1. 全部删除 任教老师角色
            String teacherRoleCode = this.getTeacherRoleCode();
            Optional.of(teacherUserOidList.parallelStream().distinct()
                    .filter(StrUtil::isNotEmpty)
                    .collect(Collectors.toList())).filter(CollUtil::isNotEmpty)
                    .ifPresent(x-> {
                        this.userRoleApi.deleteByUserOidListAndRoleCode(x, teacherRoleCode);
                    });


            Long roleId = this.queryByCode(teacherRoleCode);

            // 2. 过滤有任教信息的用户信息 存储任教角色
            List<UserRoleBo> userRoleBos = teacherUserOidList.parallelStream().distinct()
                    .filter(StrUtil::isNotEmpty)
                    .filter(x -> CollUtil.isNotEmpty(this.teacherClassesSubjectApi.getByUserOid(x).getData()))
                    .map(x-> {
                        UserRoleBo userRoleBo = new UserRoleBo();
                        userRoleBo.setRoleId(roleId);
                        userRoleBo.setUserOid(x);
                        userRoleBo.setOrganizationId(organizationId);
                        return userRoleBo;
                    }).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(userRoleBos)) {
                this.userRoleApi.saveBatchUserRole(userRoleBos);
            }
        }
    }


    /**
     * 获取班主任 role code
     * @return {@link String }
     */
    private String getHeadmasterRoleCode() {
        return Optional.ofNullable(configApi.getConfigValue(SystemConstants.ORG_ADD_WITH_USER).getData())
                .map(param -> JSONUtil.toBean(param, OrganizationConfig.class)).map(OrganizationConfig::getSchoolHeadmasterRoleKey)
                .orElse(null);
    }


    /**
     * 获取老师 role code
     * @return {@link String }
     */
    private String getTeacherRoleCode() {
        return Optional.ofNullable(configApi.getConfigValue(SystemConstants.ORG_ADD_WITH_USER).getData())
                .map(param -> JSONUtil.toBean(param, OrganizationConfig.class)).map(OrganizationConfig::getSchoolTeacherRoleKey)
                .orElse(null);
    }

    @PostMapping("/grade")
    @ApiOperation(value = "班级年级", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult classGrade() {
        String eduSystem = currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getEduSystem();
        Map res = new HashMap();
        res.put("2", "1,2,3,4,5,6");
        res.put("3", "7,8,9");
        res.put("4", "10,11,12");
        if (StringUtils.isNotEmpty(eduSystem) && eduSystem.equals("2")) {
            res.put("2", "1,2,3,4,5");
            res.put("3", "6,7,8,9");
        }
        return AjaxResult.success(res);
    }
}
