package com.light.aiszzy.web.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * @ClassName: ConfigUtils
 * @Description: 获取authorize配置工具类
 * <AUTHOR>
 * @date 2017年12月20日 上午11:39:09 
 *
 */
@Configuration
@PropertySource(value="classpath:authorize.properties",name="authorize",ignoreResourceNotFound=true)
public class ConfigUtils {
	
	@Value("${appKey}")
    private String appKey;
	@Value("${appSecret}")
    private String appSecret;
	@Value("${service}")
	private String service;
	@Value("${oauthServerUrl}")
	private String oauthServerUrl;

	@Value("${redirectUri:}")
	private String redirectUri;

	public String getAppKey() {
		return appKey;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	public String getAppSecret() {
		return appSecret;
	}

	public void setAppSecret(String appSecret) {
		this.appSecret = appSecret;
	}

	public String getService() {
		return service;
	}

	public void setService(String service) {
		this.service = service;
	}

	public String getOauthServerUrl() {
		return oauthServerUrl;
	}

	public void setOauthServerUrl(String oauthServerUrl) {
		this.oauthServerUrl = oauthServerUrl;
	}

	public String getRedirectUri() {
		return redirectUri;
	}

	public void setRedirectUri(String redirectUri) {
		this.redirectUri = redirectUri;
	}
}
