package com.light.aiszzy.web.controller.homework;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.aiszzy.homework.service.HomeworkBookApiService;
import com.light.base.attachment.service.AttachmentApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.StringUtils;
import com.light.enums.Enable;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.security.service.CurrentUserService;
import com.light.user.teacher.api.TeacherClassesSubjectApi;
import com.light.user.teacher.entity.vo.TeacherClassesSubjectVo;
import com.light.user.user.api.UserRoleApi;
import com.light.user.user.entity.vo.UserRoleVo;
import com.light.utils.PdfUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 作业本
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:30
 */
@RestController
@Validated
@Api(value = "", tags = "作业本接口")
public class HomeworkBookApiController {

    @Autowired
    private HomeworkBookApiService homeworkBookApiService;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private AttachmentApiService attachmentApiService;

    @Resource
    private UserRoleApi userRoleApi;

    @Resource
    private TeacherClassesSubjectApi teacherClassesSubjectApi;

    @PostMapping("/homeworkBook/pageList")
    @ApiOperation(value = "分页查询作业本", httpMethod = "POST")
    public AjaxResult<PageInfo<HomeworkBookVo>> getHomeworkBookPageListByCondition(@RequestBody HomeworkBookConditionBo condition) {
        return homeworkBookApiService.getHomeworkBookPageListByCondition(condition);
    }

    @PostMapping("/homeworkBook/list")
    @ApiOperation(value = "查询所有作业本", httpMethod = "POST")
    public AjaxResult<List<HomeworkBookVo>> getHomeworkBookAllListByCondition(@RequestBody HomeworkBookConditionBo condition) {
        return homeworkBookApiService.getHomeworkBookListByCondition(condition);
    }

    @PostMapping("/homeworkBook/listSchool")
    @ApiOperation(value = "查询学校所有作业本", httpMethod = "POST")
    public AjaxResult<List<HomeworkBookVo>> getSchoolHomeworkBookListByCondition(@RequestBody HomeworkBookConditionBo condition) {
        condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        return homeworkBookApiService.getHomeworkBookListByCondition(condition);
    }

    @PostMapping("/homeworkBook/listGroup")
    @ApiOperation(value = "查询所有作业本", httpMethod = "POST")
    public AjaxResult getHomeworkBookGroupListByCondition(@RequestBody HomeworkBookConditionBo condition) {
        String currentOid = currentUserService.getCurrentOid();
        condition.setUserOid(currentUserService.getCurrentOid());
        condition.setStatus(Enable.YES.getValue());
        AjaxResult<List<UserRoleVo>> listByUserOid = userRoleApi.getListByUserOid(currentOid);

        if (listByUserOid.isFail()) {
            return AjaxResult.success(new ArrayList<>());
        }
        List<UserRoleVo> roleVoList = listByUserOid.getData();
        if (CollectionUtil.isNotEmpty(roleVoList)) {
            List<Long> collect = roleVoList.stream().map(UserRoleVo::getRoleId).collect(Collectors.toList());
            if (!collect.contains(5L)) {
                AjaxResult<List<TeacherClassesSubjectVo>> byUserOid = teacherClassesSubjectApi.getByUserOid(currentOid);
                if (byUserOid.isFail()) {
                    return AjaxResult.success(new ArrayList<>());
                }
                List<TeacherClassesSubjectVo> subjectList = byUserOid.getData();
                if (CollectionUtil.isEmpty(subjectList)) {
                    return AjaxResult.success(new ArrayList<>());
                }
                condition.setSubjects(subjectList.stream().map(TeacherClassesSubjectVo::getSubjectCode).collect(Collectors.joining(",")));
            }
        }
        condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());

        return homeworkBookApiService.getHomeworkBookGroupListByCondition(condition);
    }

    @PostMapping("/homeworkBook/currentBook")
    @ApiOperation(value = "获取当前作业本", httpMethod = "POST")
    public AjaxResult<HomeworkBookVo> currentBook(@RequestBody HomeworkBookConditionBo condition) {
        condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        condition.setCreateBy(currentUserService.getCurrentOid());
        return homeworkBookApiService.currentBook(condition);
    }

    @PostMapping("/homeworkBook/updateCurrentBook")
    @ApiOperation(value = "更新当前作业本", httpMethod = "POST")
    public AjaxResult updateCurrentBook(@RequestBody HomeworkBookBo condition) {
        condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        condition.setCreateBy(currentUserService.getCurrentOid());
        return homeworkBookApiService.updateCurrentBook(condition);
    }

    @PostMapping("/homeworkBook/listSchoolNotAdd")
    @ApiOperation(value = "分页查询未添加学校作业本", httpMethod = "POST")
    public AjaxResult getSchoolNotAddHomeworkBookListByCondition(@RequestBody HomeworkBookConditionBo condition) {
        condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        condition.setCreateBy(currentUserService.getCurrentOid());
        return homeworkBookApiService.getSchoolNotAddHomeworkBookListByCondition(condition);
    }

    @PostMapping("/homeworkBook/add")
    @ApiOperation(value = "新增作业本", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "新增作业本", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addHomeworkBook(@Validated @RequestBody HomeworkBookBo homeworkBookBo) {
        return homeworkBookApiService.addHomeworkBook(homeworkBookBo);
    }

    @PostMapping("/homeworkBook/update")
    @ApiOperation(value = "修改作业本", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "修改作业本", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateHomeworkBook(@Validated @RequestBody HomeworkBookBo homeworkBookBo) {
        return homeworkBookApiService.updateHomeworkBook(homeworkBookBo);
    }

    @GetMapping("/homeworkBook/detail")
    @ApiOperation(value = "查询作业本详情", httpMethod = "GET")
    public AjaxResult<HomeworkBookVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
        return homeworkBookApiService.getDetail(oid);
    }

    @GetMapping("/homeworkBook/delete")
    @ApiOperation(value = "删除作业本", httpMethod = "GET")
    @OperationLogAnnotation(moduleName = "删除作业本", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult<Void> delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
        return homeworkBookApiService.delete(oid);
    }


    @GetMapping("/homeworkBook/textbookVersions")
    @OperationLogAnnotation(moduleName = "教材版本", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult textbookVersions(@RequestParam("grade") String grade, @RequestParam("subject") String subject) {
        return homeworkBookApiService.textbookVersions(grade, subject);
    }

    @GetMapping("/homeworkBook/textbook")
    @OperationLogAnnotation(moduleName = "教材", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult textbook(@RequestParam("versionId") String versionId, @RequestParam(value = "gradeId", required = false) String gradeId) {
        return homeworkBookApiService.textbook(versionId, gradeId);
    }

    @GetMapping("/homeworkBook/textbookCatalog")
    @ApiOperation(value = "获取目录", httpMethod = "GET")
    @OperationLogAnnotation(moduleName = "获取目录", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult textbookCatalog(@NotNull(message = "请选择需要删除的数据") @RequestParam("bookOid") String bookOid) {
        return homeworkBookApiService.textbookCatalog(bookOid);
    }

    @GetMapping("/homeworkBook/saveTextbookCatalog")
    @ApiOperation(value = "获取目录", httpMethod = "GET")
    @OperationLogAnnotation(moduleName = "获取目录", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult saveTextbookCatalog(@NotNull(message = "请选择需要删除的数据") @RequestParam("homeworkBookOid") String homeworkBookOid,@RequestParam("textBookId") String textBookId) {
        return homeworkBookApiService.saveTextbookCatalog(homeworkBookOid,textBookId);
    }


    @GetMapping("/homeworkBook/checkTextbookCatalog")
    @ApiOperation(value = "获取是否存在目录", httpMethod = "GET")
    @OperationLogAnnotation(moduleName = "获取是否存在目录", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult checkTextbookCatalog(@NotNull(message = "请选择需要删除的数据") @RequestParam("bookOid") String bookOid) {
        return homeworkBookApiService.checkTextbookCatalog(bookOid);
    }

    @GetMapping("/homeworkBook/copyHomeworkBook")
    @ApiOperation(value = "复制作业本", httpMethod = "GET")
    @OperationLogAnnotation(moduleName = "复制作业本", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult<HomeworkBookVo> copyHomeworkBook(@RequestParam("homeworkBookOid") String homeworkBookOid) {
        return homeworkBookApiService.copyHomeworkBook(homeworkBookOid);
    }

    @GetMapping("/homeworkBook/practiceBookToHomeworkBook")
    @ApiOperation(value = "同步教辅到作业本", httpMethod = "GET")
    @OperationLogAnnotation(moduleName = "同步教辅到作业本", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult<HomeworkBookVo> practiceBookToHomeworkBook(@RequestParam("practiceBookOid") String practiceBookOid) {
        return homeworkBookApiService.practiceBookToHomeworkBook(practiceBookOid, currentUserService.getCurrentOid(), currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
    }

    @GetMapping("/homeworkBook/downloadBookZip")
    @ApiOperation(value = "下载作业本zip", httpMethod = "GET")
    public void downloadBookZip(String oid, HttpServletResponse response) {
        try {
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/zip; charset=utf-8");

            AjaxResult ajaxResult = homeworkBookApiService.downloadBookZip(oid);

            if (ajaxResult.getSuccess()) {
                Map data = (Map) ajaxResult.getData();
                String fileName = data.get("name").toString() + ".zip";
                String pathArr = data.get("pathArr").toString();
                String streamArr = data.get("streamArr").toString();
                List<InputStream> insArr = new ArrayList<>();
                for (String path : streamArr.split(",")) {
                    insArr.add(new ByteArrayInputStream(HttpUtil.downloadBytes(path)));
                }
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLUtil.encode(fileName));
                ZipUtil.zip(response.getOutputStream(), pathArr.split(","), insArr.toArray(new InputStream[0]));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @GetMapping("/homeworkBook/downloadBookPdf")
    @ApiOperation(value = "下载作业本pdf", httpMethod = "GET")
    public void downloadBookPdf(String oid, HttpServletResponse response) {
        try {
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/pdf; charset=utf-8");

            AjaxResult ajaxResult = homeworkBookApiService.downloadBookPdf(oid);

            if (ajaxResult.getSuccess()) {
                Map data = (Map) ajaxResult.getData();
                String fileName = data.get("name").toString() + ".pdf";
                String pathArr = data.get("pathArr").toString();
                String pdfUrls = data.get("pdfUrl").toString();
                String answerUrls = data.get("answerUrl").toString();
                List<InputStream> insArr = new ArrayList<>();
                if (StringUtils.isNotEmpty(pdfUrls)) {
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    PdfUtil.mergePDFs(Arrays.asList(pdfUrls.split(",")), byteArrayOutputStream);
                    insArr.add(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));
                }

                if (StringUtils.isNotEmpty(answerUrls)) {
                    ByteArrayOutputStream answerByteArrayOutputStream = new ByteArrayOutputStream();
                    PdfUtil.mergePDFs(Arrays.asList(answerUrls.split(",")), answerByteArrayOutputStream);
                    insArr.add(new ByteArrayInputStream(answerByteArrayOutputStream.toByteArray()));
                }

                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLUtil.encode(fileName));
                ZipUtil.zip(response.getOutputStream(), pathArr.split(","), insArr.toArray(new InputStream[0]));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
