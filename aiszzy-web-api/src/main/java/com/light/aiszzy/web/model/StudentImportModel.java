package com.light.aiszzy.web.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 学生导入bo
 *
 * <AUTHOR>
 * @date 2022/4/11 10:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StudentImportModel {

    @Excel(name = "姓名", width = 20.0, fixedIndex = 0)
    @NotBlank(message = "姓名不能为空")
    private String realName;

    @Excel(name = "学籍号", width = 20.0, fixedIndex = 1)
    private String studentCode;

    @Excel(name = "学号", width = 20.0, fixedIndex = 2)
    private String studentNo;

    @Excel(name = "性别", width = 10.0, fixedIndex = 3, replace = {"男_1", "女_2"})
    @NotBlank(message = "性别不能为空")
    private String sex;

    @Excel(name = "出生日期", width = 10.0, fixedIndex = 4)
    private String birthday;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "民族", width = 10.0, fixedIndex = 5, replace = {"汉族_1", "蒙古族_2", "回族_3", "藏族_4", "维吾尔族_5", "苗族_6", "彝族_7", "壮族_8", "布依族_9", "朝鲜族_10", "满族_11", "侗族_12", "瑶族_13", "白族_14", "土家族_15", "哈尼族_16", "哈萨克族_17", "傣族_18", "黎族_19", "傈僳族_20", "佤族_21", "畲族_22", "高山族_23", "拉祜族_24", "水族_25", "东乡族_26", "纳西族_27", "景颇族_28", "柯尔克孜族_29", "土族_30", "达斡尔族_31", "仫佬族_32", "羌族_33", "布朗族_34", "撒拉族_35", "毛难族_36", "仡佬族_37", "锡伯族_38", "阿昌族_39", "普米族_40", "塔吉克族_41", "怒族_42", "乌孜别克族_43", "俄罗斯族_44", "鄂温克族_45", "崩龙族_46", "保安族_47", "裕固族_48", "京族_49", "塔塔尔族_50", "独龙族_51", "鄂伦春族_52", "赫哲族_53", "门巴族_54", "珞巴族_55", "基诺族_56", "其他_57"})
    private String nation;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "国籍/地区", width = 10.0, fixedIndex = 6, replace = {"中国_1", "其他_2"})
    private String nationalityId;

    /**
     * 改成输入
     */
    @Excel(name = "籍贯（市）", width = 10.0, fixedIndex = 7)
    private String nativePlaceId;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "身份证件类型", width = 10.0, fixedIndex = 8, replace = {"居民身份证_1", "香港特区护照/身份证明_2", "港澳特区护照/身份证明_3", "台湾居民来往大陆通行证_4", "境外永久居住证_5", "护照_6", "其他_7"})
    private String identityType;

    @Excel(name = "身份证件号", width = 20.0, fixedIndex = 9)
    private String identityCardNumber;

    @Excel(name = "港澳台侨外", width = 10.0, fixedIndex = 10, replace = {"是_1", "否_0"})
    private String overseasChinese;

    @Excel(name = "户口所在地", width = 10.0, fixedIndex = 11)
    private String registeredResidence;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "户口性质", width = 10.0, fixedIndex = 12, replace = {"未落户常住户口_1", "农业户口_2", "非农业户口_3"})
    private String registrationType;

    @Excel(name = "现住址", width = 10.0, fixedIndex = 13)
    private String homeAddress;

    @Excel(name = "联系电话", width = 10.0, fixedIndex = 14)
    private String phone;

    @Excel(name = "是否独生子女", width = 10.0, fixedIndex = 15, replace = {"是_1", "否_0"})
    private String isSingle;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "健康状况", width = 10.0, fixedIndex = 16, replace = {"健康_1", "良好_2", "一般_3", "较弱_4", "残疾_5"})
    private String healthType;

    /**
     * 监护人一姓名
     */
    @Excel(name = "姓名", width = 20.0, fixedIndex = 17)
    private String mainRealName;
    /**
     * 监护人一关系
     */
    @Excel(name = "关系", width = 10.0, fixedIndex = 18, replace = {"父亲_1", "母亲_2", "祖父_3", "祖母_4", "外祖父_5", "外祖母_6", "其他_7"})
    private String mainRelationType;
    /**
     * 监护人一民族
     */
    @Excel(name = "民族", width = 10.0, fixedIndex = 19, replace = {"汉族_1", "蒙古族_2", "回族_3", "藏族_4", "维吾尔族_5", "苗族_6", "彝族_7", "壮族_8", "布依族_9", "朝鲜族_10", "满族_11", "侗族_12", "瑶族_13", "白族_14", "土家族_15", "哈尼族_16", "哈萨克族_17", "傣族_18", "黎族_19", "傈僳族_20", "佤族_21", "畲族_22", "高山族_23", "拉祜族_24", "水族_25", "东乡族_26", "纳西族_27", "景颇族_28", "柯尔克孜族_29", "土族_30", "达斡尔族_31", "仫佬族_32", "羌族_33", "布朗族_34", "撒拉族_35", "毛难族_36", "仡佬族_37", "锡伯族_38", "阿昌族_39", "普米族_40", "塔吉克族_41", "怒族_42", "乌孜别克族_43", "俄罗斯族_44", "鄂温克族_45", "崩龙族_46", "保安族_47", "裕固族_48", "京族_49", "塔塔尔族_50", "独龙族_51", "鄂伦春族_52", "赫哲族_53", "门巴族_54", "珞巴族_55", "基诺族_56", "其他_57"})
    private String mainNation;
    /**
     * 监护人一健康状况
     */
    @Excel(name = "健康状况", width = 10.0, fixedIndex = 20, replace = {"健康_1", "良好_2", "一般_3", "较弱_4", "残疾_5"})
    private String mainHealthType;
    /**
     * 监护人一手机号码
     */
    @Excel(name = "手机号码", width = 10.0, fixedIndex = 21)
    private String mainPhone;

    /**
     * 监护人二姓名
     */
    @Excel(name = "姓名", width = 20.0, fixedIndex = 22)
    private String secondRealName;
    /**
     * 监护人二关系
     */
    @Excel(name = "关系", width = 10.0, fixedIndex = 23, replace = {"父亲_1", "母亲_2", "祖父_3", "祖母_4", "外祖父_5", "外祖母_6", "其他_7"})
    private String secondRelationType;
    /**
     * 监护人二民族
     */
    @Excel(name = "民族", width = 10.0, fixedIndex = 24, replace = {"汉族_1", "蒙古族_2", "回族_3", "藏族_4", "维吾尔族_5", "苗族_6", "彝族_7", "壮族_8", "布依族_9", "朝鲜族_10", "满族_11", "侗族_12", "瑶族_13", "白族_14", "土家族_15", "哈尼族_16", "哈萨克族_17", "傣族_18", "黎族_19", "傈僳族_20", "佤族_21", "畲族_22", "高山族_23", "拉祜族_24", "水族_25", "东乡族_26", "纳西族_27", "景颇族_28", "柯尔克孜族_29", "土族_30", "达斡尔族_31", "仫佬族_32", "羌族_33", "布朗族_34", "撒拉族_35", "毛难族_36", "仡佬族_37", "锡伯族_38", "阿昌族_39", "普米族_40", "塔吉克族_41", "怒族_42", "乌孜别克族_43", "俄罗斯族_44", "鄂温克族_45", "崩龙族_46", "保安族_47", "裕固族_48", "京族_49", "塔塔尔族_50", "独龙族_51", "鄂伦春族_52", "赫哲族_53", "门巴族_54", "珞巴族_55", "基诺族_56", "其他_57"})
    private String secondNation;
    /**
     * 监护人二健康状况
     */
    @Excel(name = "健康状况", width = 10.0, fixedIndex = 25, replace = {"健康_1", "良好_2", "一般_3", "较弱_4", "残疾_5"})
    private String secondHealthType;
    /**
     * 监护人二手机号
     */
    @Excel(name = "手机号码", width = 10.0, fixedIndex = 26)
    private String secondPhone;

    /**
     * 机构或者学校id
     */
    private Long organizationId;
    /**
     * 班级id
     */
    private Long classesId;

    /**
     * 入学年份（导出匹配字段）
     */
    private String enrollmentYear;
}
