package com.light.aiszzy.web.controller.userPaper;


import com.light.aiszzy.userPaper.api.UserPaperPageApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户试卷
 */
@Api(tags = "校本页码接口")
@RestController
@RequestMapping("userPaperPage")
public class UserPaperPageApiController {


    @Resource
    private UserPaperPageApi userPaperPageApi;



    /**
     * 根据校本 OID 查询页码数据
     * @param userPaperOid the user paper oid 校本 OID
     * @return {@link AjaxResult }
     */
    @GetMapping("queryByUserPaperOid/{userPaperOid}")
    public AjaxResult queryByUserPaperOid(@PathVariable("userPaperOid") String userPaperOid) {
        return this.userPaperPageApi.queryByUserPaperOid(userPaperOid);
    }

}
