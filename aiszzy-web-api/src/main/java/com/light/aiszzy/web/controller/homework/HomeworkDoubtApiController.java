package com.light.aiszzy.web.controller.homework;

import com.light.aiszzy.homeworkResult.api.HomeworkPageResultApi;
import com.light.aiszzy.homeworkResult.api.HomeworkResultAnswerApi;
import com.light.aiszzy.homeworkResult.api.HomeworkResultApi;
import com.light.aiszzy.homeworkResult.entity.bo.*;
import com.light.contants.AISzzyConstants;
import com.light.core.entity.AjaxResult;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Validated
@Api(value = "", tags = "疑问项")
public class HomeworkDoubtApiController {

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private HomeworkResultApi homeworkResultApi;

    @Resource
    private HomeworkPageResultApi homeworkPageResultApi;

    @Resource
    private HomeworkResultAnswerApi homeworkResultAnswerApi;

    @PostMapping("/doubt/noStudent")
    @ApiOperation(value = "学生未匹配", httpMethod = "POST")
    public AjaxResult noStudent(@RequestBody HomeworkPageResultConditionBo bo) {
        bo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        bo.setDoubtType(AISzzyConstants.DoubtType.NOERROR.getType());
        return homeworkPageResultApi.getHomeworkPageResultPageListByCondition(bo);
    }

    @PostMapping("/doubt/repeat")
    @ApiOperation(value = "学生重复", httpMethod = "POST")
    public AjaxResult repeat(@RequestBody HomeworkResultConditionBo bo) {
        bo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        return homeworkResultApi.getDuplicateHomeworkResultListByCondition(bo);
    }

    @PostMapping("/doubt/answer")
    @ApiOperation(value = "题目疑问", httpMethod = "POST")
    public AjaxResult answer(@RequestBody HomeworkResultAnswerConditionBo bo) {
        bo.setIsDoubt(AISzzyConstants.DoubtStatus.EXIST.getType());
        return homeworkResultAnswerApi.getHomeworkResultAnswerPageListByCondition(bo);
    }

    @PostMapping("/doubt/dealNoStudent")
    @ApiOperation(value = "处理学生未匹配", httpMethod = "POST")
    public AjaxResult dealNoStudent(@RequestBody HomeworkPageResultBo bo) {
        return homeworkPageResultApi.dealNoStudent(bo);
    }

    @PostMapping("/doubt/dealRepeat")
    @ApiOperation(value = "处理学生重复", httpMethod = "POST")
    public AjaxResult dealRepeat(@RequestBody HomeworkPageResultBo bo) {
        return homeworkPageResultApi.dealRepeat(bo);
    }

    @PostMapping("/doubt/dealAnswer")
    @ApiOperation(value = "处理题目疑问", httpMethod = "POST")
    public AjaxResult dealAnswer(@RequestBody HomeworkResultAnswerBo bo) {
        return homeworkResultAnswerApi.updateHomeworkResultAnswer(bo);
    }

}
