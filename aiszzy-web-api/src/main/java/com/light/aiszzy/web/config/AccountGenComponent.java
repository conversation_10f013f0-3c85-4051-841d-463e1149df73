package com.light.aiszzy.web.config;

import com.light.base.area.entity.vo.AreaVo;
import com.light.base.area.service.AreaApiService;
import com.light.base.config.service.ConfigApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.exception.UnifiedException;
import com.light.redis.component.RedisComponent;
import com.light.user.account.service.AccountApiService;
import com.light.user.organization.entity.bo.OrganizationConditionBo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.organization.service.OrganizationApiService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/7/22
 */
@Component
public class AccountGenComponent {

    @Autowired
    private AreaApiService areaApiService;

    @Autowired
    private OrganizationApiService organizationApiService;

    @Autowired
    private AccountApiService accountApiService;

    @Autowired
    private RedisComponent redisComponent;

    @Autowired
    private ConfigApiService configApiService;

    private static final String[] AREA_CODE = {"3","4","6","7","8","9","A","B","C","D","E","F","G","H","J","K","L","M","N","P","Q","R","T","U","V","W","X","Y"};

    private static final String[] SCHOOL_CODE = {"3","4","6","7","8","9","a","b","c","d","e","f","g","h","j","k","m","n","p","q","r","t","u","v","w","x","y","A","B","C","D","E","F","G","H","J","K","L","M","N","P","Q","R","T","U","V","W","X","Y"};

    private static final String RANDOM_CODE = "abcdefghjkmnpqrtuvwxyABCDEFGHJKLMNPQRTUVWXY346789";

    private final static String STUDENT_ACCOUNT_SCHOOL_PREFIX = "student:account:school:prefix";

    private final static String ACCOUNT_EXIST_CHECK = "ACCOUNT_EXIST_CHECK";

    private final static int LENGTH = SCHOOL_CODE.length;

    private static Map<Long, String> map = new HashMap() {{
        put(320100000000l, "A");
        put(320200000000l, "B");
        put(320300000000l, "C");
        put(320400000000l, "D");
        put(320500000000l, "E");
        put(320600000000l, "F");
        put(320700000000l, "G");
        put(320800000000l, "H");
        put(320900000000l, "J");
        put(321000000000l, "K");
        put(321100000000l, "L");
        put(321200000000l, "M");
        put(321300000000l, "N");
    }};

    public AjaxResult<String> generateAccount(Long organizationId, Long enrollmentYear, String studentNo, String classNo) {
        StringBuilder builder = new StringBuilder("");
        String accountName = null;
        //先从缓存中获取学校的学生账号前缀
        Object object = redisComponent.hget(STUDENT_ACCOUNT_SCHOOL_PREFIX, organizationId.toString());
        if(null != object) {
            builder.append(object.toString());
        }else{
            //查询学校信息
            OrganizationVo organizationVo = organizationApiService.getDetailById(organizationId).getData();
            if(null != organizationVo) {
                if(null == organizationVo.getCityId() || !map.containsKey(organizationVo.getCityId())) {
                    throw new UnifiedException("学校所属市为空或者错误");
                }
                builder.append(map.get(organizationVo.getCityId()));
                // 根据市查询所有的县区（按照排序）
                List<AreaVo> areas = areaApiService.getOrderdAreaListByParentId(organizationVo.getCityId()).getData();
                if(!CollectionUtils.isEmpty(areas)) {
                    for(int i = 0; i < areas.size(); i++) {
                        if(areas.get(i).getId().equals(organizationVo.getAreaId())) {//匹配县区，拼接第二位
                            builder.append(AREA_CODE[i]);
                        }
                    }
                }
                //根据县区查询所有的组织结构
                OrganizationConditionBo condition = new OrganizationConditionBo();
                condition.setAreaId(organizationVo.getAreaId());
                condition.setType(SystemConstants.ORGANIZATION_TYPE_SCHOOL);
                List<OrganizationVo> orgs = organizationApiService.getByCondition(condition).getData();
                if(!CollectionUtils.isEmpty(orgs)) {
                    List<OrganizationVo> orders = orgs.stream().sorted(Comparator.comparing(OrganizationVo::getId)).collect(Collectors.toList());
                    for(int i = 0; i < orders.size(); i++) {
                        if(orders.get(i).getId().equals(organizationVo.getId())) {
                            int a = i + 1;
                            builder.append(SCHOOL_CODE[(a / LENGTH)]).append(SCHOOL_CODE[(a % LENGTH)]);
                        }
                    }
                }
                //把学校的前缀放入缓存
                redisComponent.hset(STUDENT_ACCOUNT_SCHOOL_PREFIX, organizationId.toString(), builder.toString());
            }else{
                return AjaxResult.fail("学校不存在");
            }
        }
        //入学年份
        builder.append(String.valueOf(enrollmentYear - 2000));
        //获取是否校验账号存在参数
        String config = configApiService.getConfigValue(ACCOUNT_EXIST_CHECK).getData();
        if (!StringUtils.isEmpty(config)) {//参数不为空
            if(Boolean.valueOf(config)) {//true, 进行校验
                accountName = this.getAccountName(builder.toString(), studentNo, classNo);
            }else{//否则不校验
                accountName = builder.toString() + classNo + studentNo;
            }
        }else{//参数为空，默认校验
            accountName = this.getAccountName(builder.toString(), studentNo, classNo);
        }
        if(!StringUtils.isEmpty(accountName)) {
            return AjaxResult.success(accountName);
        }else{
            return AjaxResult.fail("学号为"+studentNo+"的学生账号已存在");
        }
    }

    private String getAccountName(String fixed, String studentNo, String classesNo) {
        //查询账号是否重复
        Boolean exist = accountApiService.accountNameExist(fixed + classesNo + studentNo).getData();
        if(null != exist && exist) {//如果账号重复，则返回null
            return null;
        }
        return fixed + classesNo + studentNo;
    }
}
