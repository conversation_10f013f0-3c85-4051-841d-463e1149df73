package com.light.aiszzy.web.controller.xkw;

import com.light.aiszzy.xkw.xkwTextbookCatalog.api.XkwTextbookCatalogApi;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.bo.XkwTextbookCatalogConditionBo;
import com.light.aiszzy.xkw.xkwTextbookCatalog.entity.vo.XkwTextbookCatalogVo;
import com.light.core.entity.AjaxResult;
import com.light.enums.Semester;
import com.light.utils.TreeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("xkwTextbookCatalog")
@Api(tags = "学科网目录")
public class XkwTextbookCatalogApiController {




    @Resource
    private XkwTextbookCatalogApi xkwTextbookCatalogApi;

    @ApiOperation("获取目录")
    @GetMapping("tree")
    public AjaxResult<List<XkwTextbookCatalogVo>> getTree(@RequestParam("grade") Integer grade,
                                                          @RequestParam("versionId") Long versionId,
                                                          @RequestParam("term") Integer term) {
        Semester semester = Semester.getByValue(term);
        if(semester == null) {
            return AjaxResult.fail("学期不正确");
        }
        XkwTextbookCatalogConditionBo bo = new XkwTextbookCatalogConditionBo();
        bo.setVersionId(versionId);
        bo.setGrade(grade);
        bo.setTerm(semester.getCode());
        AjaxResult<List<XkwTextbookCatalogVo>> result = this.xkwTextbookCatalogApi.getXkwTextbookCatalogListByCondition(bo);

        if(result.isFail()) {
            return result;
        }
        List<XkwTextbookCatalogVo> tree = TreeUtil.tree(result.getData(), "0", x -> {});
        return AjaxResult.success(tree);
    }

    @ApiOperation("根据教材获取目录")
    @GetMapping("treeByTextbook")
    public AjaxResult<List<XkwTextbookCatalogVo>> getTree(@RequestParam("textbookId") Long textbookId) {
        XkwTextbookCatalogConditionBo bo = new XkwTextbookCatalogConditionBo();
        bo.setTextbookId(textbookId);
        AjaxResult<List<XkwTextbookCatalogVo>> result = this.xkwTextbookCatalogApi.getXkwTextbookCatalogListByCondition(bo);

        if(result.isFail()) {
            return result;
        }
        List<XkwTextbookCatalogVo> tree = TreeUtil.tree(result.getData(), "0", x -> {});
        return AjaxResult.success(tree);
    }
}
