package com.light.aiszzy.web.controller.resourcesUserAddToCart;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartConditionBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.vo.ResourcesUserAddToCartVo;
import com.light.aiszzy.resourcesUserAddToCart.service.ResourcesUserAddToCartApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-21 15:17:15
 */
@RestController
@Validated
@Api(value = "", tags = "用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)接口" )
public class ResourcesUserAddToCartApiController {
	
    @Autowired
    private ResourcesUserAddToCartApiService resourcesUserAddToCartApiService;

	@Resource
	private CurrentUserService currentUserService;

	@PostMapping("/resourcesUserAddToCart/pageList")
	@ApiOperation(value = "分页查询用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "POST")
	public AjaxResult<PageInfo<ResourcesUserAddToCartVo>> getResourcesUserAddToCartPageListByCondition(@RequestBody ResourcesUserAddToCartConditionBo condition){
		return resourcesUserAddToCartApiService.getResourcesUserAddToCartPageListByCondition(condition);
    }

	@PostMapping("/resourcesUserAddToCart/list")
	@ApiOperation(value = "查询所有用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "POST")
	public AjaxResult<List<ResourcesUserAddToCartVo>> getResourcesUserAddToCartAllListByCondition(@RequestBody ResourcesUserAddToCartConditionBo condition){
		condition.setPageNo(SystemConstants.NO_PAGE);
		return resourcesUserAddToCartApiService.getResourcesUserAddToCartListByCondition(condition);
	}

	@PostMapping("/resourcesUserAddToCart/add")
	@ApiOperation(value = "新增用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addResourcesUserAddToCart(@Validated @RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo){
		resourcesUserAddToCartBo.setUserOid(currentUserService.getCurrentOid());
		resourcesUserAddToCartBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
		return resourcesUserAddToCartApiService.addResourcesUserAddToCart(resourcesUserAddToCartBo);
    }

	@PostMapping("/resourcesUserAddToCart/update")
	@ApiOperation(value = "修改用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateResourcesUserAddToCart(@Validated @RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
		return resourcesUserAddToCartApiService.updateResourcesUserAddToCart(resourcesUserAddToCartBo);
	}

	@GetMapping("/resourcesUserAddToCart/detail")
	@ApiOperation(value = "查询用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)详情",httpMethod = "GET")
	@ApiImplicitParam(name = "resourcesUserAddToCartId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<List<ResourcesUserAddToCartVo>> getDetail(@NotNull(message = "请选择数据") @RequestParam("subject") Integer subject) {
		return resourcesUserAddToCartApiService.getDetail(currentUserService.getCurrentOid(),currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode(),subject);
	}

	@GetMapping("/resourcesUserAddToCart/delete")
	@ApiOperation(value = "删除用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return resourcesUserAddToCartApiService.delete(oid);
	}

	@PostMapping("/resourcesUserAddToCart/deleteQuestion")
	@ApiOperation(value = "根据题目oid删除",httpMethod = "POST")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "根据题目oid删除", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult deleteQuestion(@RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
		resourcesUserAddToCartBo.setUserOid(currentUserService.getCurrentOid());
		resourcesUserAddToCartBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
		return resourcesUserAddToCartApiService.deleteQuestion(resourcesUserAddToCartBo);
	}

	@PostMapping("/resourcesUserAddToCart/deleteQuestionType")
	@ApiOperation(value = "根据题目类型删除",httpMethod = "POST")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "根据题目类型删除", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult deleteQuestionType(@RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
		resourcesUserAddToCartBo.setUserOid(currentUserService.getCurrentOid());
		resourcesUserAddToCartBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
		return resourcesUserAddToCartApiService.deleteQuestion(resourcesUserAddToCartBo);
	}

	@PostMapping("/resourcesUserAddToCart/deleteAll")
	@ApiOperation(value = "清空",httpMethod = "POST")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "清空", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult deleteAll(@RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
		resourcesUserAddToCartBo.setUserOid(currentUserService.getCurrentOid());
		resourcesUserAddToCartBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
		return resourcesUserAddToCartApiService.deleteQuestion(resourcesUserAddToCartBo);
	}


}
