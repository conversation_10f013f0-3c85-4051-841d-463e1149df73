package com.light.aiszzy.web.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/4/12 17:36
 */
public class NumberKit {

    /**
     * 字符串转数字
     *
     * @return
     */
    public static Integer str2Integer(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return Integer.valueOf(str);
    }

    /**
     * 字符串转数字
     *
     * @return
     */
    public static Long str2Long(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return Long.valueOf(str);
    }

    /**
     * long转str
     *
     * @param val
     * @return
     */
    public static String long2Str(Long val) {
        if (val == null) {
            return "";
        }
        return String.valueOf(val);
    }

    /**
     * integer转str
     *
     * @param val
     * @return
     */
    public static String integer2Str(Integer val) {
        if (val == null) {
            return "";
        }
        return String.valueOf(val);
    }
}
