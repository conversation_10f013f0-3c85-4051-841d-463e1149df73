package com.light.aiszzy.web.controller.basicInfo;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.light.aiszzy.baseInfo.entity.dto.OrganizationBoExt;
import com.light.aiszzy.baseInfo.entity.dto.UserRoleBoExt;
import com.light.aiszzy.baseInfo.entity.vo.OrganizationVoExt;
import com.light.base.config.api.ConfigApi;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.exception.WarningException;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.security.service.CurrentUserService;
import com.light.user.organization.api.OrganizationApi;
import com.light.user.organization.api.OrganizationSetupApi;
import com.light.user.organization.entity.bo.OrganizationConfig;
import com.light.user.organization.entity.bo.OrganizationSetupBo;
import com.light.user.organization.entity.vo.OrganizationSetupVo;
import com.light.user.role.api.RoleApi;
import com.light.user.role.entity.bo.RoleConditionBo;
import com.light.user.user.api.UserApi;
import com.light.user.user.api.UserRoleApi;
import com.light.user.user.entity.bo.UserRoleBo;
import com.light.user.user.entity.vo.UserVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@ApiOperation("学校管理")
@RestController
@RequestMapping("school")
public class SchoolApiController {

    @Resource
    private OrganizationApi organizationApi;

    @Resource
    private OrganizationSetupApi organizationSetupApi;

    @Resource
    private UserApi userApi;

    @Resource
    private ConfigApi configApi;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private UserRoleApi userRoleApi;

    @Resource
    private RoleApi roleApi;


    /**
     * 获取组织机构详情
     *
     * @return org detail
     * @throws Exception the exception
     */
    @ApiOperation(value = "获取组织机构详情", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public AjaxResult getOrgDetail()  {

        Long organizationId = currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        AjaxResult detail = organizationApi.getDetail(organizationId);
        if (detail.isFail() || detail.getData() == null) {
            return detail;
        }
        Map<String, Object> map = (Map<String, Object>) detail.getData();
        OrganizationVoExt organizationVo = JSONObject.parseObject(JSONObject.toJSONString(map.get("organizationVo")), OrganizationVoExt.class);
        if (null != organizationVo) {
            // 副标题，logo,建校日期存org_setup
            AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationId);
            if (byOrgId.isSuccess() && byOrgId.getData() != null) {
                OrganizationSetupVo organizationSetupVo = byOrgId.getData();
                organizationVo.setLogo(organizationSetupVo.getLogo());
                organizationVo.setWebName(organizationSetupVo.getWebName());
                organizationVo.setOtherConfig(organizationSetupVo.getOtherConfig());
            }

            // 获取超级管理员
            Optional.ofNullable(configApi.getConfigValue(SystemConstants.ORG_ADD_WITH_USER).getData())
                    .map(param -> JSONUtil.toBean(param, OrganizationConfig.class)).map(OrganizationConfig::getSchoolRoleKey)
                    .map(schoolRoleKey -> this.userApi.queryByRoleCodeAndOrgId(schoolRoleKey, organizationId).getData())
                    .filter(CollUtil::isNotEmpty)
                    .flatMap(x -> x.stream().min(Comparator.comparing(UserVo::getCreateTime)))
                    .ifPresent(userVo -> {
                        organizationVo.setSuperAdminAccount(userVo.getAccountName());
                    });

            map.put("organizationVo", organizationVo);
        }

        return detail;
    }


    @ApiOperation("学校管理员列表")
    @GetMapping("adminList")
    public AjaxResult adminList() {
        Long organizationId = this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        // 获取超级管理员
        List<UserVo> userVos = Optional.ofNullable(configApi.getConfigValue(SystemConstants.ORG_ADD_WITH_USER).getData())
                .map(param -> JSONUtil.toBean(param, OrganizationConfig.class)).map(OrganizationConfig::getSchoolAdminRoleKey)
                .map(schoolRoleKey -> this.userApi.queryByRoleCodeAndOrgId(schoolRoleKey, organizationId).getData())
                .filter(CollUtil::isNotEmpty)
                .orElse(Lists.newArrayList());
        return AjaxResult.success(userVos);
    }


    @ApiOperation("学校管理员删除")
    @PostMapping("deleteAdmin")
    @OperationLogAnnotation(moduleName = "学校管理员删除")
    public AjaxResult deleteAdmin(@RequestBody UserRoleBo userBo) {
        String oid = userBo.getUserOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("用户OID不能为空");
        }
        String schoolAdminCode = Optional.ofNullable(configApi.getConfigValue(SystemConstants.ORG_ADD_WITH_USER).getData())
                .map(param -> JSONUtil.toBean(param, OrganizationConfig.class)).map(OrganizationConfig::getSchoolAdminRoleKey)
                .orElseThrow(() -> new WarningException("未获取到管理员角色CODE"));
        return this.userRoleApi.deleteByUserOidAndRoleCode(oid, schoolAdminCode);
    }


    @ApiOperation("批量保存管理员")
    @PostMapping("batchAddAdmin")
    @OperationLogAnnotation(moduleName = "批量保存管理员")
    public AjaxResult batchAddAdmin(@RequestBody UserRoleBoExt userBo) {
        List<String> userOidList = userBo.getUserOidList();
        if(CollUtil.isEmpty(userOidList)) {
            return AjaxResult.fail("用户OID不能为空");
        }

        // 获取预至 管理员角色 code
        Long organizationId = this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        String schoolAdminCode = Optional.ofNullable(configApi.getConfigValue(SystemConstants.ORG_ADD_WITH_USER).getData())
                .map(param -> JSONUtil.toBean(param, OrganizationConfig.class)).map(OrganizationConfig::getSchoolAdminRoleKey)
                .orElseThrow(() -> new WarningException("未获取到管理员角色CODE"));

        // 根据 code获取角色 Id
        RoleConditionBo roleConditionBo = new RoleConditionBo();
        roleConditionBo.setCode(schoolAdminCode);
        Long roleId = Optional.ofNullable( this.roleApi.getRoleListByCondition(roleConditionBo).getData()).map(x-> {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(x));
            return jsonObject.getJSONArray("list");
        })
        .filter(CollUtil::isNotEmpty)
        .map(x-> x.getJSONObject(0).getLong("id")).orElseThrow(()-> new WarningException("未获取到管理员角色信息"));

        // 保存用户角色
        List<UserRoleBo> userRoleBoList = userOidList.stream().map(x -> {
            UserRoleBo userRoleBo = new UserRoleBo();
            userRoleBo.setUserOid(x);
            userRoleBo.setRoleId(roleId);
            userRoleBo.setOrganizationId(organizationId);
            return userRoleBo;
        }).collect(Collectors.toList());

        return this.userRoleApi.saveBatchUserRole(userRoleBoList);
    }





    /**
     * 编辑组织机构
     *
     * @param organizationBo 组织机构信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-31 15:37:40
     */
    @ApiOperation(value = "修改组织机构信息", httpMethod = "POST")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @OperationLogAnnotation(moduleName = "修改组织机构信息")
    public AjaxResult updateOrg(@RequestBody OrganizationBoExt organizationBo)  {
        organizationBo.setId(this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId());
        AjaxResult ajaxResult = organizationApi.updateOrganization(organizationBo);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationBo.getId());
        if (byOrgId.isFail()) {
            return byOrgId;
        }

        OrganizationSetupVo data = byOrgId.getData();
        OrganizationSetupBo organizationSetupBo = new OrganizationSetupBo();
        organizationSetupBo.setOrganizationId(organizationBo.getId());
        organizationSetupBo.setLogo(organizationBo.getLogo());
        organizationSetupBo.setWebName(organizationBo.getWebName());
        organizationSetupBo.setOtherConfig(organizationBo.getOtherConfig());
        if (data == null) {
            organizationSetupApi.saveOrganizationSetup(organizationSetupBo);
        } else {
            organizationSetupBo.setId(data.getId());
            organizationSetupApi.updateOrganizationSetup(organizationSetupBo);
        }

        return ajaxResult;
    }


}
