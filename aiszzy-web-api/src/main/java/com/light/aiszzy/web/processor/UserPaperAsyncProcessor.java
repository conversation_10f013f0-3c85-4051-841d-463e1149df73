package com.light.aiszzy.web.processor;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.aiszzy.hwlOpen.service.HwlOpenApiService;
import com.light.aiszzy.userPaper.api.UserPaperApi;
import com.light.aiszzy.userPaper.api.UserPaperPageApi;
import com.light.aiszzy.userPaper.entity.bo.UserPaperBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperPageBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionBo;
import com.light.aiszzy.web.config.AiszzyThreadExecutor;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.attachment.service.AttachmentApiService;
import com.light.beans.HwlOpenAutomaticBoxVO;
import com.light.beans.ImageModel;
import com.light.beans.ImageRequestBo;
import com.light.core.entity.AjaxResult;
import com.light.core.exception.WarningException;
import com.light.core.exception.util.ThrowableUtil;
import com.light.redis.component.RedisComponent;
import com.light.utils.PdfUtil;
import com.light.utils.PositionUtil;
import com.light.utils.ProgressService;
import com.light.utils.ZipUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2025/07/10
 */
@Slf4j
@Service
public class UserPaperAsyncProcessor {


    @Resource
    private AttachmentApiService attachmentApiService;

    @Resource
    private UserPaperApi userPaperApi;

    @Resource
    private HwlOpenApiService hwlOpenApiService;

    @Resource
    private UserPaperPageApi userPaperPageApi;


    @Resource
    private ProgressService progressService;


    @Resource
    private RedisComponent redisComponent;


    @Async(AiszzyThreadExecutor.EXECUTOR_USER_PAPER_ANALYSIS)
    public void analysisByUserPaperOid(String url, UserPaperBo userPaperBo) {
        String userPaperOid = userPaperBo.getOid();
        try {
            String extName = FileUtil.extName(url);
            // 文件转换为 map
            Map<String, ImageModel> imgMap ;
            try(InputStream inputStream = new URL(url).openStream()) {
                imgMap = this.parseInput2ImgMap(inputStream, extName);
            }  catch (Exception e) {
                log.error("【校本解析】 文件解析失败，地址：{}, 校本 OID:{}, 异常信息:{}", url, userPaperOid, ThrowableUtil.getStackTrace(e));
                this.progressService.configStatus(userPaperOid, ProgressService.Business.USER_PAPER_ANALYSIS, ProgressService.Status.FAILURE.getVal(), "文件解析失败");
                return;
            }

            if(imgMap == null || imgMap.isEmpty()) {
                log.error("【校本解析】 文件解析失败，地址：{}, 校本 OID:{}, 空数据", url, userPaperOid);
                this.progressService.configStatus(userPaperOid, ProgressService.Business.USER_PAPER_ANALYSIS, ProgressService.Status.FAILURE.getVal(), "文件解析失败，请检查文件内容");
                return;
            }

            // 删除所有页码数据
            this.userPaperPageApi.deleteByUserPaperOid(userPaperOid);
            this.progressService.configStatus(userPaperOid, ProgressService.Business.USER_PAPER_ANALYSIS,  ProgressService.Status.IN_PROGRESS.getVal(), "进行中");
            // 设置总页数
            this.progressService.configTotalTaskNum(userPaperOid, ProgressService.Business.USER_PAPER_ANALYSIS, imgMap.size());

            //每 5 页进行一个线程处理
            int diff = 20;
            List<List<String>> keyList = Lists.partition(new ArrayList<>(imgMap.keySet()), diff);
            int size = keyList.size();
            CountDownLatch countDownLatch = new CountDownLatch(size);
            UserPaperAsyncProcessor practiceBookAsyncProcessor = SpringUtil.getBean(UserPaperAsyncProcessor.class);
            keyList.forEach(x-> {
                Map<String, ImageModel> splitMap = Maps.newHashMap();
                x.forEach(y-> splitMap.put(y, imgMap.get(y)));
                practiceBookAsyncProcessor.practiceBookAnalysisImgMap(userPaperBo, splitMap, pageList-> {
                    countDownLatch.countDown();
                    this.progressService.addFinishTaskNum(userPaperOid, ProgressService.Business.USER_PAPER_ANALYSIS, splitMap.size());
                });
            });

            // 等待所有线程执行结束
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } finally {
            log.info("【校本解析】 解析完成 ，教辅 OID: {}", userPaperOid);
            this.progressService.configStatus(userPaperOid, ProgressService.Business.USER_PAPER_ANALYSIS, ProgressService.Status.SUCCESS.getVal(), "成功");
        }
    }


    /**
     *  解析文件为 map
     * @param inputStream the inputstream 文件流
     * @param extName the extname 文件后缀名
     * @return {@link Map }<{@link Integer }, {@link String }>
     */
    public Map<String, ImageModel> parseInput2ImgMap(InputStream inputStream, String extName) {

        // 构建处理上传函数
        BiFunction<BufferedImage, String, ImageModel> func = (img, name)->{
            ImageModel.ImageModelBuilder builder = ImageModel.builder().width(img.getWidth()).height(img.getHeight());
            // 保存图片到本地
            try (ByteArrayOutputStream output = new ByteArrayOutputStream();) {
                ImageIO.write(img, "JPEG", output);
                MultipartFile multipartFile = new MockMultipartFile("file", name, MediaType.MULTIPART_FORM_DATA_VALUE, output.toByteArray());
                String url = Optional.ofNullable(this.attachmentApiService.uploadFile(multipartFile).getData()).map(AttachmentVo::getNginxUrl)
                        .orElseThrow(() -> new WarningException("教辅解析失败"));
                builder.url(url);
                return builder.build();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        };
        if(extName.equalsIgnoreCase("pdf")) {
            return PdfUtil.partitionPdf2ImgMap(inputStream, func);
        }
        if((extName.equalsIgnoreCase("zip"))) {
            return ZipUtils.unImgZip2ImgMap(inputStream, func);
        }
        throw new WarningException("不支持文件类型【"+ extName +"】解析");
    }


    /**
     *  解析教辅图片信息 （调用好未来进行数据解析)
     * @param userPaperBo user paper bo
     * @param pageImgMap the page img map 图片 bas64数据 , key 为 页码
     */
    @Async(AiszzyThreadExecutor.EXECUTOR_USER_PAPER_ANALYSIS)
    public void practiceBookAnalysisImgMap(UserPaperBo userPaperBo, Map<String, ImageModel> pageImgMap, Consumer<List<UserPaperPageBo>> consumer) {
        String userPaperOid = userPaperBo.getOid();
        List<UserPaperPageBo> list = null;
        try {
            log.info("【校本异步解析】 线程名称：{}, 校本 OID: {}", Thread.currentThread().getName(), userPaperOid);
            list = pageImgMap.entrySet().stream().map((v)-> {
                String name = v.getKey();
                UserPaperPageBo userPaperPageBo = new UserPaperPageBo();
                userPaperPageBo.setUserPaperOid(userPaperOid);
                Integer pageNo = getPageNo(name);
                userPaperPageBo.setPageNo(pageNo);

                String url = v.getValue().getUrl() ;
                userPaperPageBo.setImageUrl(url);

                // 获取图片宽度
                int width = v.getValue().getWidth();
                int height = v.getValue().getHeight();

                // 好未来内容解析
                HwlOpenAutomaticBoxVO hwlOpenAutomaticBoxVO = this.automaticBox(url);
                if(hwlOpenAutomaticBoxVO == null) {
                    log.error("【校本异步解析】 内容解析失败，好未来调用失败，校本 OID: {}, 页码:{}, 文件地址：{}", userPaperOid, name, url);
                }else {
                    List<HwlOpenAutomaticBoxVO.ItemData> itemDataList = hwlOpenAutomaticBoxVO.getData();
                    int size = itemDataList.size();
                    userPaperPageBo.setAnalysisQuestionNum((long) size);
                    userPaperPageBo.setQuestionNum((long) size);
                    userPaperPageBo.setAnalysisJson(JSON.toJSONString(hwlOpenAutomaticBoxVO));
                    // 处理 坐标点高度
                    IntStream.range(0, size).forEach(i -> {
                        HwlOpenAutomaticBoxVO.ItemData item = itemDataList.get(i);
                        List<List<Integer>> nextPosition = null;
                        if (i < size - 1) {
                            nextPosition = itemDataList.get(i + 1).getItemPosition();
                        }
                        // 处理最大高度
                        item.setItemPosition(PositionUtil.adjustHeightPoints(height, item.getItemPosition(), nextPosition));
                    });
                    // 处理宽度
                    IntStream.range(0, size).forEach(i -> this.widenWidth(itemDataList, i, width));

                    // 获取题目坐标信息进行组装
                    List<UserPaperQuestionBo> questionList = IntStream.range(0, size).mapToObj(i -> {
                        UserPaperQuestionBo questionBo = new UserPaperQuestionBo();
                        questionBo.setUserPaperOid(userPaperOid);
                        questionBo.setOrgCode(userPaperBo.getOrgCode());
                        HwlOpenAutomaticBoxVO.ItemData itemData = itemDataList.get(i);
                        String position = PositionUtil.covert2Wh(itemData.getItemPosition());
                        questionBo.setPosition(position);
                        questionBo.setOrderNum(i + 1L);
                        return questionBo;
                    }).collect(Collectors.toList());
                    // 设置教辅题目合集信息
                    userPaperPageBo.setUserPaperQuestionList(questionList);
                }

                return userPaperPageBo;
            }).collect(Collectors.toList());


            if(CollUtil.isEmpty(list)) {
                log.warn("【校本异步解析】 解析内容为空，线程名称：{}, 校本 OID: {}", Thread.currentThread().getName(), userPaperOid);
                return;
            }
            // 存储数据
            this.userPaperPageApi.saveBatchByUserPaperOid(userPaperOid, list);
        } finally {
            consumer.accept(list);
        }
    }


    private void widenWidth(List<HwlOpenAutomaticBoxVO.ItemData> itemDataList,int index, Integer width) {
        int size = itemDataList.size();
        HwlOpenAutomaticBoxVO.ItemData itemData = itemDataList.get(index);
        List<List<Integer>> itemPosition = itemData.getItemPosition();

        int maxWidth = IntStream.range(0, size).filter(x -> x != index).mapToObj(itemDataList::get)
                // 第一个坐标点的 x  大于 当前第二个坐标点的 x 轴
                .filter(x -> x.getItemPosition().get(0).get(0) >= itemPosition.get(1).get(0))
                //
                .filter(x -> {
                    // 第一个坐标点的 y 在 当前 第一个坐标点 y  到第四个坐标点的 y 之间
                    // 或者
                    // 第四个坐标点 y 在当前 第一个坐标点 y  到第四个坐标点的 y 之间
                    List<Integer> firstPoints = x.getItemPosition().get(0);
                    List<Integer> fourPoints = x.getItemPosition().get(3);
                    return firstPoints.get(1) >= itemPosition.get(0).get(1) && firstPoints.get(1) <= itemPosition.get(3).get(1)
                            ||
                            fourPoints.get(1) >= itemPosition.get(0).get(1) && fourPoints.get(1) <= itemPosition.get(3).get(1)
                            ||
                            itemPosition.get(0).get(1) >= firstPoints.get(1) && itemPosition.get(0).get(1) <= fourPoints.get(1)
                            ||
                            itemPosition.get(3).get(1) >= firstPoints.get(1) && itemPosition.get(3).get(1) <= fourPoints.get(1)
                            ;
                }).mapToInt(x-> x.getItemPosition().get(0).get(0)).min().orElse(width);


        itemData.setItemPosition(PositionUtil.adjustWidthPoints(maxWidth, itemPosition));

    }




    /**
     *  获取页号
     * @param name the name
     * @return {@link Long }
     */
    private Integer getPageNo(String name) {
        String pageNoStr = name.substring(0, name.lastIndexOf("."));
        return Integer.valueOf(pageNoStr);
    }

    /**
     * 调用好未来数据
     * @param imgUrl the img url
     * @return {@link HwlOpenAutomaticBoxVO }
     */
    private HwlOpenAutomaticBoxVO automaticBox(String imgUrl) {
        // 好未来内容解析
        ImageRequestBo bo = new ImageRequestBo();
        bo.setImageUrl(imgUrl);
        AjaxResult ajaxResult = this.hwlOpenApiService.automaticBox(bo);
        if(ajaxResult.isSuccess()) {
            // 解析内容
            Object automaticObj = ajaxResult.getData();
            String jsonString = JSON.toJSONString(automaticObj);

            // 设置题目数量
            return  JSON.parseObject(jsonString, HwlOpenAutomaticBoxVO.class);
        }
        return null;
    }
}
