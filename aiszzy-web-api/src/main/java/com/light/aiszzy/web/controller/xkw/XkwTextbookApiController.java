package com.light.aiszzy.web.controller.xkw;


import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.service.HomeworkBookApiService;
import com.light.aiszzy.xkw.xkwTextbook.api.XkwTextbookApi;
import com.light.aiszzy.xkw.xkwTextbook.entity.bo.XkwTextbookConditionBo;
import com.light.aiszzy.xkw.xkwTextbook.entity.vo.XkwTextbookVo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsConditionBo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo;
import com.light.aiszzy.xkw.xkwTextbookVersions.service.XkwTextbookVersionsApiService;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 教材
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
@RestController
@Validated
@Api(value = "", tags = "教材接口" )
public class XkwTextbookApiController {

	@Autowired
	private HomeworkBookApiService homeworkBookApiService;

	@Autowired
	private XkwTextbookVersionsApiService xkwTextbookVersionsApiService;

	@Resource
	private XkwTextbookApi xkwTextbookApi;

	@ApiOperation("教材分页列表")
	@PostMapping("/xkwTextbook/pageList")
	public AjaxResult<PageInfo<XkwTextbookVo>> textBookPageList(@RequestBody XkwTextbookConditionBo conditionBo) {
		return this.xkwTextbookApi.getXkwTextbookPageListByCondition(conditionBo);
	}

	@ApiOperation("教材列表")
	@PostMapping("/xkwTextbook/list")
	public AjaxResult<List<XkwTextbookVo>> textbookList(@RequestBody XkwTextbookConditionBo conditionBo) {
		return this.xkwTextbookApi.getXkwTextbookListByCondition(conditionBo);
	}

	@PostMapping("/xkwTextbookVersions/pageList")
	@ApiOperation(value = "分页查询教材版本",httpMethod = "POST")
	public AjaxResult<PageInfo<XkwTextbookVersionsVo>> getXkwTextbookVersionsPageListByCondition(@RequestBody XkwTextbookVersionsConditionBo condition){
		return xkwTextbookVersionsApiService.getXkwTextbookVersionsPageListByCondition(condition);
	}

	@PostMapping("/xkwTextbookVersions/list")
	@ApiOperation(value = "查询所有教材版本",httpMethod = "POST")
	public AjaxResult<List<XkwTextbookVersionsVo>> getXkwTextbookVersionsAllListByCondition(@RequestBody XkwTextbookVersionsConditionBo condition){
		return xkwTextbookVersionsApiService.getXkwTextbookVersionsListByCondition(condition);
	}

	@ApiOperation("根据年级学科获取版本")
	@GetMapping("/xkw/textbookVersions")
	@OperationLogAnnotation(moduleName = "教材版本", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult textbookVersions(@RequestParam("grade") String grade, @RequestParam("subject") String subject) {
		return homeworkBookApiService.textbookVersions(grade, subject);
	}

	@ApiOperation("根据学段学科获取版本")
	@GetMapping("/xkwTextbookVersions/queryList")
	public AjaxResult queryList(@RequestParam("stage") Integer stage, @RequestParam("subject") String subject) {
		return xkwTextbookVersionsApiService.getByStageAndSubject(stage, subject);
	}
}
