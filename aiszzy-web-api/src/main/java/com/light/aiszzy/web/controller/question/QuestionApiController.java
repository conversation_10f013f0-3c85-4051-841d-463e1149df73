package com.light.aiszzy.web.controller.question;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.question.entity.bo.QuestionConditionBo;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import com.light.aiszzy.question.service.QuestionApiService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 题目表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "题目表接口")
public class QuestionApiController {

    @Autowired
    private QuestionApiService questionService;

    @GetMapping("/question/detail")
    @ApiOperation(value = "查询题目表详情",httpMethod = "GET")
    public AjaxResult<QuestionVo> getDetail(@RequestParam("questionOid") String questionOid,@RequestParam(name = "practiceBookOid",required = false) String practiceBookOid) {
        return questionService.getDetail(questionOid,practiceBookOid);
    }


}
