package com.light.aiszzy.web.controller.homework;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookOptionRecordVo;
import com.light.aiszzy.homework.service.HomeworkBookOptionRecordApiService;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业本映射印送记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:31
 */
@RestController
@Validated
@Api(value = "", tags = "作业本映射印送记录接口" )
public class HomeworkBookOptionRecordApiController {
	
    @Autowired
    private HomeworkBookOptionRecordApiService homeworkBookOptionRecordApiService;

	@PostMapping("/homeworkBookOptionRecord/pageList")
	@ApiOperation(value = "分页查询作业本映射印送记录",httpMethod = "POST")
	public AjaxResult<PageInfo<HomeworkBookOptionRecordVo>> getHomeworkBookOptionRecordPageListByCondition(@RequestBody HomeworkBookOptionRecordConditionBo condition){
		return homeworkBookOptionRecordApiService.getHomeworkBookOptionRecordPageListByCondition(condition);
    }

	@PostMapping("/homeworkBookOptionRecord/list")
	@ApiOperation(value = "查询所有作业本映射印送记录",httpMethod = "POST")
	public AjaxResult<List<HomeworkBookOptionRecordVo>> getHomeworkBookOptionRecordAllListByCondition(@RequestBody HomeworkBookOptionRecordConditionBo condition){
		return homeworkBookOptionRecordApiService.getHomeworkBookOptionRecordListByCondition(condition);
	}

	@PostMapping("/homeworkBookOptionRecord/count")
	@ApiOperation(value = "查询作业本映射印送记录个数",httpMethod = "POST")
	public AjaxResult getHomeworkBookOptionRecordCountByCondition(@RequestBody HomeworkBookOptionRecordConditionBo condition){
		return homeworkBookOptionRecordApiService.getHomeworkBookOptionRecordCountByCondition(condition);
	}

	@PostMapping("/homeworkBookOptionRecord/add")
	@ApiOperation(value = "新增作业本映射印送记录",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增作业本映射印送记录", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addHomeworkBookOptionRecord(@Validated @RequestBody HomeworkBookOptionRecordBo homeworkBookOptionRecordBo){
		return homeworkBookOptionRecordApiService.addHomeworkBookOptionRecord(homeworkBookOptionRecordBo);
    }

	@PostMapping("/homeworkBookOptionRecord/update")
	@ApiOperation(value = "修改作业本映射印送记录",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改作业本映射印送记录", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateHomeworkBookOptionRecord(@Validated @RequestBody HomeworkBookOptionRecordBo homeworkBookOptionRecordBo) {
		return homeworkBookOptionRecordApiService.updateHomeworkBookOptionRecord(homeworkBookOptionRecordBo);
	}

	@GetMapping("/homeworkBookOptionRecord/detail")
	@ApiOperation(value = "查询作业本映射印送记录详情",httpMethod = "GET")
	@ApiImplicitParam(name = "homeworkBookOptionRecordId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<HomeworkBookOptionRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return homeworkBookOptionRecordApiService.getDetail(oid);
	}

	@GetMapping("/homeworkBookOptionRecord/delete")
	@ApiOperation(value = "删除作业本映射印送记录",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除作业本映射印送记录", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return homeworkBookOptionRecordApiService.delete(oid);
	}
}
