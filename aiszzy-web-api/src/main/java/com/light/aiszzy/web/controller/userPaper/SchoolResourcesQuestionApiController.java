package com.light.aiszzy.web.controller.userPaper;

import com.light.aiszzy.schoolResourcesQuestion.api.SchoolResourcesQuestionApi;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionConditionBo;
import com.light.contants.AISzzyConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "校本题")
@RestController
@RequestMapping("schoolResourcesQuestion")
public class SchoolResourcesQuestionApiController {


    @Resource
    private SchoolResourcesQuestionApi schoolResourcesQuestionApi;

    @Resource
    private CurrentUserService currentUserService;


    @ApiOperation("更新题目信息")
    @PostMapping("update")
    public AjaxResult update(@RequestBody SchoolResourcesQuestionBo bo) {
        return this.schoolResourcesQuestionApi.updateSchoolResourcesQuestion(bo);
    }


    @ApiOperation("题库列表")
    @PostMapping("list")
    public AjaxResult list(@RequestBody SchoolResourcesQuestionConditionBo bo) {
        bo.setUserPaperIsPublish(StatusEnum.YES.getCode());
        bo.setOrgCode(this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        if(bo.getStage() != null) {
            bo.setGradeList(AISzzyConstants.getXkwGradeListByStage(bo.getStage()));
        }
        return this.schoolResourcesQuestionApi.getSchoolResourcesQuestionPageListByCondition(bo);
    }

}
