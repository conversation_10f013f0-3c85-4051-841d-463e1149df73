package com.light.aiszzy.web.controller.userPaper;

import cn.hutool.core.util.StrUtil;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.userPaper.api.UserPaperQuestionApi;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionBo;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "校本题目")
@RestController
@RequestMapping("userPaperQuestion")
public class UserPaperQuestionApiController {

    @Resource
    private UserPaperQuestionApi userPaperQuestionApi;



    /**
     *  根据校本 OID 查询题目数据
     * @param userPaperOid the user paper oid 校本 OID
     * @return {@link AjaxResult }
     */
    @ApiOperation("根据试卷 OID 查询")
    @GetMapping("queryByUserPaperOid")
    public AjaxResult queryByUserPaperOid(@RequestParam("userPaperOid") String userPaperOid) {
        return this.userPaperQuestionApi.queryByUserPaperOid(userPaperOid);
    }

    /**
     *  根据校本页码 OID 查询题目数据
     * @param userPaperPageOid the user paper oid 校本 OID
     * @return {@link AjaxResult }
     */
    @ApiOperation("根据试卷页码 OID 查询")
    @GetMapping("queryByUserPaperPageOid")
    public AjaxResult queryByUserPaperPageOid(@RequestParam("userPaperPageOid") String userPaperPageOid) {
        return this.userPaperQuestionApi.queryByUserPaperPageOid(userPaperPageOid);
    }

    /**
     * 根据 OID 获取题目信息，如没有题目信息，进行（学科网|好未来）查询 题目数据进行
     *
     * @param oid the practice book question oid
     * @param position the practice book question
     * @return {@link AjaxResult }<{@link PracticeBookQuestionVo }>
     */
    @ApiOperation(value = "根据 OID 获取题目信息")
    @GetMapping("/getOrInitQuestionInfoByOid")
    public AjaxResult getOrInitQuestionInfoByOid(@RequestParam("oid") String oid , @RequestParam("position") String position) {
        UserPaperQuestionBo bo = new UserPaperQuestionBo();
        bo.setOid(oid);
        bo.setPosition(position);
        return this.userPaperQuestionApi.getOrInitQuestionInfoByOid(bo);
    }

    /**
     *  根据 OID 重新获取题目
     *  注： 坐标更改 将从第三方（学科网/好未来) 获取数据进行存储并返回
     * @param bo the practice book question oid 教辅题目 OID {oid, position }
     * @return {@link AjaxResult }
     */
    @ApiOperation(value = "根据 OID 重新获取题目")
    @PostMapping("/reFetchQuestionInfoByOid")
    @OperationLogAnnotation(moduleName = "根据OID重新加载题目信息", operationType = OperationLogConstants.OP_TYPE_UPDATE)
    public AjaxResult reFetchQuestionInfoByOid( @RequestBody UserPaperQuestionBo bo) {
        String oid = bo.getOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("OID 不能为空");
        }
        String position = bo.getPosition();
        if(StrUtil.isEmpty(position)) {
            return AjaxResult.fail("坐标不能为空");
        }
        return this.userPaperQuestionApi.reFetchQuestionInfoByOid(bo);
    }


    @ApiOperation(value = "根据坐标新增数据")
    @PostMapping("/addQuestionByPosition")
    public AjaxResult addQuestionByPosition(@RequestBody UserPaperQuestionBo bo) {
        return this.userPaperQuestionApi.addQuestionByPosition(bo);
    }




    @ApiOperation(value = "取消滑题")
    @PostMapping("/cancel")
    public AjaxResult cancelUserPaperQuestion(@RequestBody UserPaperQuestionBo bo) {
        return this.userPaperQuestionApi.cancelUserQuestion(bo);
    }

    @ApiOperation(value = "标注校本作业题目")
    @PostMapping("/mark")
    public AjaxResult markUserPaperQuestion(@RequestBody UserPaperQuestionBo bo) {
        String oid = bo.getOid();
        // 标注作业题
        return this.userPaperQuestionApi.markUserQuestion(oid);
    }
}
