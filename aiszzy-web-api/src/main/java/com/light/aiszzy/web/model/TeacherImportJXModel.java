package com.light.aiszzy.web.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @ClassName: TeacherImportJXModel
 * @Description:
 * <AUTHOR> @Date 2022/4/14
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TeacherImportJXModel {

    @Excel(name = "姓名", width = 20.0, fixedIndex = 0)
    @NotBlank(message = "姓名不能为空")
    private String name;

    @Size(max = 20, min =1, message = "长度必须小于等于20")
    @Excel(name = "手机号码", width = 10.0, fixedIndex = 1)
    @NotBlank(message = "手机号码不能为空")
    private String phone;

    @Excel(name = "性别", width = 10.0, fixedIndex = 2, replace = {"男_1", "女_2"})
    private String sex;

    @Excel(name = "出生日期", width = 10.0, fixedIndex = 3)
    private String birthday;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "民族", width = 10.0, fixedIndex = 4, replace = {"汉族_1", "蒙古族_2", "回族_3", "藏族_4", "维吾尔族_5", "苗族_6", "彝族_7", "壮族_8", "布依族_9", "朝鲜族_10", "满族_11", "侗族_12", "瑶族_13", "白族_14", "土家族_15", "哈尼族_16", "哈萨克族_17", "傣族_18", "黎族_19", "傈僳族_20", "佤族_21", "畲族_22", "高山族_23", "拉祜族_24", "水族_25", "东乡族_26", "纳西族_27", "景颇族_28", "柯尔克孜族_29", "土族_30", "达斡尔族_31", "仫佬族_32", "羌族_33", "布朗族_34", "撒拉族_35", "毛难族_36", "仡佬族_37", "锡伯族_38", "阿昌族_39", "普米族_40", "塔吉克族_41", "怒族_42", "乌孜别克族_43", "俄罗斯族_44", "鄂温克族_45", "崩龙族_46", "保安族_47", "裕固族_48", "京族_49", "塔塔尔族_50", "独龙族_51", "鄂伦春族_52", "赫哲族_53", "门巴族_54", "珞巴族_55", "基诺族_56", "其他_57"})
    private String nation;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "国籍/地区", width = 10.0, fixedIndex = 5, replace = {"中国_1", "其他_2"})
    private String nationalityId;

    /**
     * 改成输入
     */
    @Excel(name = "籍贯（市）", width = 10.0, fixedIndex = 6)
    private String nativePlaceId;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "身份证件类型", width = 10.0, fixedIndex = 7, replace = {"居民身份证_1", "香港特区护照/身份证明_2", "港澳特区护照/身份证明_3", "台湾居民来往大陆通行证_4", "境外永久居住证_5", "护照_6", "其他_7"})
    private String identityType;

    @Excel(name = "身份证件号", width = 20.0, fixedIndex = 8)
    private String identityCardNumber;

    @Excel(name = "户口所在地", width = 10.0, fixedIndex = 9)
    private String registeredResidence;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "户口性质", width = 10.0, fixedIndex = 10, replace = {"未落户常住户口_1", "农业户口_2", "非农业户口_3"})
    private String registrationType;

    @Excel(name = "港澳台侨外", width = 10.0, fixedIndex = 11, replace = {"是_1", "否_0"})
    private String overseasChinese;

    /**
     * 政治面貌 字典表
     */
    @Excel(name = "政治面貌", width = 10.0, fixedIndex = 12, replace = {"中共党员_1", "中共预备党员_2", "群众_3", "其他民主党派_4"})
    private String politicalOutlookType;

    /**
     * 婚姻状态 字典表
     */
    @Excel(name = "婚姻状态", width = 10.0, fixedIndex = 13, replace = {"未婚_1", "已婚_2", "离异_3", "丧偶_4"})
    private String maritalType;

    /**
     * 宗教信仰  20
     */
    @Excel(name = "宗教信仰", width = 10.0, fixedIndex = 14)
    private String religiousBelief;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "健康状况", width = 10.0, fixedIndex = 15, replace = {"健康_1", "良好_2", "一般_3", "较弱_4", "残疾_5"})
    private String healthType;

    @Excel(name = "家庭住址", width = 10.0, fixedIndex = 16)
    private String homeAddress;

    /**
     * 邮箱 20
     */
    @Excel(name = "邮箱", width = 10.0, fixedIndex = 17)
    private String postalCode;

    /**
     * 学历 ，字典表
     */
    @Excel(name = "学历", width = 10.0, fixedIndex = 18, replace = {"博士研究生_1", "硕士研究生_2", "大学本科_3", "大学专科_4", "中等专科_5","高中_6","高中以下_7"})
    private String education;

    /**
     * 毕业院校 20
     */
    @Excel(name = "毕业院校", width = 10.0, fixedIndex = 19)
    private String graduateSchool;

    /**
     *学制 20
     */
    @Excel(name = "学制", width = 10.0, fixedIndex = 20)
    private String educationSystem;

    /**
     *所学专业 20
     */
    @Excel(name = "所学专业", width = 10.0, fixedIndex = 21)
    private String major;

    /**
     *入学年月
     */
    @Excel(name = "入学年月", width = 10.0, fixedIndex = 22)
    private String joinDate;

    /**
     *毕业时间
     */
    @Excel(name = "毕业时间", width = 10.0, fixedIndex = 23)
    private String graduationDate;


    /**
     * 教师资格证类型 字典表，原型图没有介绍
     */
    @Excel(name = "教师资格证类型", width = 10.0, fixedIndex = 24, replace = {"幼儿园教师资格证_1", "小学教师资格证_2", "初级中级教师资格证_3", "高级中学教师资格证_5", "中等职业学校教师资格证_6","中等职业学校实习指导教师资格证_7","高等学校教师资格证_8"})
    private String qualificationType;

    /**
     * 教师资格证学科 原型图没有介绍
     */
    @Excel(name = "教师资格证学科", width = 10.0, fixedIndex = 25)
    private String qualificationSubject;

    /**
     * 参加工作年月
     */
    @Excel(name = "参加工作年月", width = 10.0, fixedIndex = 26)
    private String workDate;

    /**
     *从教年月
     */
    @Excel(name = "从教年月", width = 10.0, fixedIndex = 27)
    private String educationDate;

    /**
     *来校年月
     */
    @Excel(name = "来校年月", width = 10.0, fixedIndex = 28)
    private String schoolDate;

    /**
     *教职工类别  字典表
     */
    @Excel(name = "教职工类别", width = 10.0, fixedIndex = 29, replace = {"专任教师_1", "聘任教师_2", "行政人员_3", "教辅人员_5", "工勤人员_6"})
    private String employeeType;

    /**
     *是否在编 字典表
     */
    @Excel(name = "是否在编", width = 10.0, fixedIndex = 30,replace = {"在编_1", "备案_2","自聘_3"})
    private String isQuotas;

    /**
     *编制类别 字典表
     */
    @Excel(name = "编制类别", width = 10.0, fixedIndex = 31,replace = {"教学类_1", "行政类_2","教辅类_3"})
    private String quotasType;

    /**
     *职称 字典表
     */
    @Excel(name = "职称", width = 10.0, fixedIndex = 32,replace = {"无_1",  "中小学正高级教师_2", "中小学高级教师_3", "中小学一级教师_4", "中小学二级教师_5", "中小学三级教师_6", "未定职级_7", "教授_8", "副教授_9"})
    private String titleType;

    /**
     *职位 20
     */
    @Excel(name = "职位", width = 10.0, fixedIndex = 33)
    private String position;


    /**
     *职业技能 字典表
     */
    @Excel(name = "职业技能", width = 10.0, fixedIndex = 34,replace = {"职业资格一级（高级技师）_1",  "职业资格二级（技师）_2", "职业资格三级（高级）_3", "职业资格四级（中级）_4", "职业资格五级（初级）_5"})
    private String skill;

    /**
     *当前任教科目  20
     */
    @Excel(name = "当前任教科目", width = 10.0, fixedIndex = 35)
    public String nowSubject;

    /**
     *曾经任教科目 20
     */
    @Excel(name = "曾经任教科目", width = 10.0, fixedIndex = 36)
    public String agoSubject;
}
