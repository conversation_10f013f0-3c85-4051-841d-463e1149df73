package com.light.aiszzy.web.controller.homework;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogInfoVo;
import com.light.aiszzy.homework.service.HomeworkBookCatalogInfoApiService;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业本目录关联作业
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:31
 */
@RestController
@Validated
@Api(value = "", tags = "作业本目录关联作业接口" )
public class HomeworkBookCatalogInfoApiController {
	
    @Autowired
    private HomeworkBookCatalogInfoApiService homeworkBookCatalogInfoApiService;

	@PostMapping("/homeworkBookCatalogInfo/pageList")
	@ApiOperation(value = "分页查询作业本目录关联作业",httpMethod = "POST")
	public AjaxResult<PageInfo<HomeworkBookCatalogInfoVo>> getHomeworkBookCatalogInfoPageListByCondition(@RequestBody HomeworkBookCatalogInfoConditionBo condition){
		return homeworkBookCatalogInfoApiService.getHomeworkBookCatalogInfoPageListByCondition(condition);
    }

	@PostMapping("/homeworkBookCatalogInfo/list")
	@ApiOperation(value = "查询所有作业本目录关联作业",httpMethod = "POST")
	public AjaxResult<List<HomeworkBookCatalogInfoVo>> getHomeworkBookCatalogInfoAllListByCondition(@RequestBody HomeworkBookCatalogInfoConditionBo condition){
		return homeworkBookCatalogInfoApiService.getHomeworkBookCatalogInfoListByCondition(condition);
	}

	@PostMapping("/homeworkBookCatalogInfo/bind")
	@ApiOperation(value = "绑定作业本目录关联作业",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "绑定作业本目录关联作业", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult bind(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo){
		return homeworkBookCatalogInfoApiService.bind(homeworkBookCatalogInfoBo);
	}

	@PostMapping("/homeworkBookCatalogInfo/unbind")
	@ApiOperation(value = "解绑作业本目录关联作业",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "解绑作业本目录关联作业", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult unbind(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
		return homeworkBookCatalogInfoApiService.unbind(homeworkBookCatalogInfoBo);
	}

	@PostMapping("/homeworkBookCatalogInfo/sort")
	@ApiOperation(value = "排序作业本目录关联作业",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "排序作业本目录关联作业", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult sortHomeworkBookCatalogInfo(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
		return homeworkBookCatalogInfoApiService.sortHomeworkBookCatalogInfo(homeworkBookCatalogInfoBo);
	}

	@PostMapping("/homeworkBookCatalogInfo/add")
	@ApiOperation(value = "新增作业本目录关联作业",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增作业本目录关联作业", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addHomeworkBookCatalogInfo(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo){
		return homeworkBookCatalogInfoApiService.addHomeworkBookCatalogInfo(homeworkBookCatalogInfoBo);
    }

	@PostMapping("/homeworkBookCatalogInfo/update")
	@ApiOperation(value = "修改作业本目录关联作业",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改作业本目录关联作业", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateHomeworkBookCatalogInfo(@Validated @RequestBody HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
		return homeworkBookCatalogInfoApiService.updateHomeworkBookCatalogInfo(homeworkBookCatalogInfoBo);
	}

	@GetMapping("/homeworkBookCatalogInfo/detail")
	@ApiOperation(value = "查询作业本目录关联作业详情",httpMethod = "GET")
	@ApiImplicitParam(name = "homeworkBookCatalogInfoId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<HomeworkBookCatalogInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return homeworkBookCatalogInfoApiService.getDetail(oid);
	}

	@GetMapping("/homeworkBookCatalogInfo/delete")
	@ApiOperation(value = "删除作业本目录关联作业",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除作业本目录关联作业", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return homeworkBookCatalogInfoApiService.delete(oid);
	}
}
